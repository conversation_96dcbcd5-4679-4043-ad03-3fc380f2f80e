package com.unicom.realtime.function;

import com.alibaba.fastjson.JSONObject;
import com.unicom.realtime.bean.TFttrCode;
import com.unicom.realtime.constant.CommonConstant;
import com.unicom.realtime.util.JdbcUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class InitBeanFunction extends RichSourceFunction<HashMap> {
    private final static Logger logger = LoggerFactory.getLogger(InitBeanFunction.class);

    private ParameterTool conf;
    private volatile boolean isRunning = true;
    private JdbcUtil jdbcUtil;

    private String host;

    private String user;

    private String pwd;

    public InitBeanFunction(ParameterTool parameters) throws Exception {


        this.conf = parameters;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        isRunning = true;
        host = conf.get(CommonConstant.MYSQL_URL);
        user = conf.get(CommonConstant.MYSQL_USER);
        pwd = conf.get(CommonConstant.MYSQL_PASSWD);
    }

    @Override
    public void run(SourceContext<HashMap> sourceContext) throws Exception {
        int count = 0;
        while (isRunning) {
            //logger.info("=======================================广播变量开始===========");
            Date date = new Date();
            int hours = date.getHours();
            if (hours >= 6 && count == 0) {
               // logger.info("=======================================广播变量===查询数据库===========hours：{}",hours);
                jdbcUtil = new JdbcUtil();
                jdbcUtil.openMySqlConnection(host, user, pwd);

                String sql_context = CommonConstant.FTTR_SQL;
//                logger.info("InitBeanFunction  FTTR_SQL:{}", sql_context);

                List<TFttrCode> tableArr = jdbcUtil.select(sql_context, TFttrCode.class);
               // logger.info("==========================tableArr:{}", JSONObject.toJSONString(tableArr));
                if (tableArr.size() > 0) {
                    HashMap fttrMap = new HashMap();
                    tableArr.forEach(one -> {
                        TFttrCode fttrCode = one;
                        fttrMap.put(fttrCode.getProductId(), fttrCode.getSvcType());
                    });
                    sourceContext.collect(fttrMap);
                    count++;
                }
                jdbcUtil.close();

            }

            if (count > 0 && hours <6) {
            //    logger.info("==========================count:{},hours:{}", count, hours);
                count = 0;
            }
            Thread.sleep(1000 * 60 * 10);
        }
    }

    @Override
    public void cancel() {
        isRunning = false;
    }


}
