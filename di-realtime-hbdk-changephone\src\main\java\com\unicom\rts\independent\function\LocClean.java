package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocData;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Loc<PERSON>lean extends RichFlatMapFunction<String, LocData> {
    private final static Logger logger = LoggerFactory.getLogger(LocClean.class);
    private final Long delayMinute;

    public LocClean (Long delayMinute) {
        this.delayMinute = delayMinute;
    }

    @Override
    public void flatMap(String locStr, Collector<LocData> out) throws Exception {
        try {
            String[] lines = StringUtils.splitPreserveAllTokens(locStr, "|");
            for (int i = 1; i < lines.length; i++) {
                String[] line = StringUtils.splitPreserveAllTokens(lines[i], "\u0001");

                if (StringUtils.isBlank(line[4]) || StringUtils.isBlank(line[5])) {
                    continue;
                }
                if (line[0].length() != 11) {
                    continue;
                }
                if (System.currentTimeMillis() - Long.parseLong(line[1]) > delayMinute * 60 * 1000) {
                    continue;
                }
                LocData loc = new LocData();
                loc.setDeviceNumber(line[0]);
                loc.setTime(Long.parseLong(line[1]));
                loc.setImei(line[2]);
                loc.setImsi(line[3]);
                loc.setLac(line[4]);
                loc.setCi(line[5]);
                loc.setProvId(line[8]);
                loc.setInTime(line[9]);
                loc.setDatasource(line[10]);
                loc.setLacci(line[4] + "_" + line[5]);
                //cdh 时间
                loc.setCdhTime(line[11]);
                out.collect(loc);
            }
        } catch (Exception e) {
            logger.error("LocClean Exception:{} locStr:{}", e, locStr);
        }
    }
}
