hive初始化文件

test
hadoop fs -mkdir hdfs:///user/hh_slfn2_sschj_gray/rts-payment
hadoop fs -mkdir hdfs:///user/hh_slfn2_sschj_gray/rts-payment/hive

show databases;
use ;
show tables;
show ubd_sscj_gray_data

CREATE TABLE `payment_data`(
  `dts_collect_time` string,
  `dts_kaf_time` string,
  `dts_kaf_offset` string,
  `dts_kaf_part` string,
  `operate_type` string,
  `operate_time` string,
  `operate_type_desc` string,
  `Id` string,
  `phone_num` string,
  `is_delete` string,
  `create_user` string,
  `create_name` string,
  `create_time` string
  )
stored as textfile
PARTITIONED BY (
  `phone_num` string
  )
ROW FORMAT SERDE
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
  'escape.delim'='\\',
  'field.delim'='\u0001',
  'serialization.null.format'='')
STORED AS INPUTFORMAT
  'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
  'hdfs:///user/hh_slfn2_sschj_gray/rts-payment/hive'

hadoop fs -get hdfs://10.177.78.195:8020/user/hh_arm_prod_sschj/liuyanhui/sscj_data/dwd_r_tbl_gdzx_subscribe_user.txt

hadoop fs -get hdfs://10.177.78.195:8020/data2/ncbssdata/data_unload/dc_unload/dwd_r_tbl_gdzx_subscribe_user_20230928.txt

hadoop fs -put /data/disk01/hh_slfn2_sschj_gray/dwd_r_tbl_gdzx_subscribe_user.txt hdfs:///user/hh_slfn2_sschj_gray/rts-payment/hive

load data inpath "hdfs:///user/hh_slfn2_sschj_gray/rts-payment/hive/dwd_r_tbl_gdzx_subscribe_user.txt" into table payment_data;





初始化入湖

export _JAVA_OPTIONS=-Djava.io.tmpdir=/data/disk01/hh_slfn2_sschj/private/wyp/tmp/flink17/
~/components/flink-1.17.0-sql/bin/yarn-session.sh -qu hh_slfn2_sschj -s 4 -nm flink-17-sql_wyp -jm 16g -tm 16g -d

~/components/flink-1.17.0-sql/bin/sql-client.sh -s yarn-session -i ~/shell/catalog/paimon_catalog.sql

use catalog hive_catalog;

USE CATALOG paimon_catalog;

set sql-client.execution.result-mode=TABLEAU;
set execution.runtime-mode=BATCH;
set parallelism.default=10;


--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_gray_flink`.`payment_data_paimom` (
  dts_collect_time STRING,
  dts_kaf_time STRING,
  dts_kaf_offset STRING,
  dts_kaf_part STRING,
  operate_type STRING,
  operate_time STRING,
  operate_type_desc STRING,
  Id STRING,
  phone_num STRING,
  is_delete STRING,
  create_user STRING,
  create_name STRING,
  create_time STRING,
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  PRIMARY KEY (phone_num) NOT ENFORCED
) WITH (
  'bucket' = '50',
  'bucket-key' = 'phone_num',
  'write-only' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'snapshot.time-retained' = '1h',
  'num-sorted-run.stop-trigger'='2147483647',
  'sort-spill-threshold'='10'
);


insert OVERWRITE
  `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_paimom`
SELECT
  dts_collect_time,
  dts_kaf_time,
  dts_kaf_offset,
  dts_kaf_part,
  operate_type,
  operate_time,
  operate_type_desc,
  id,
  phone_num,
  is_delete,
  create_user,
  create_name,
  create_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`payment_data`;




Stream park
Flink sql
/************************************************************************************
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj_gray/paimon',
  'default-database' = 'ubd_sscj_gray_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_gray_flink',
  'hive-conf-dir' = '/etc/hive/3.1.5.0-152/0'
);
USE CATALOG hive_catalog;
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
SET 'table.exec.sink.not-null-enforcer'='DROP';

CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';
--kafka 表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_gray_flink`.`kafka_payment_independent_item` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME()
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.58.154:9092,10.177.58.155:9092,10.177.58.156:9092,10.177.58.157:9092,10.177.58.158:9092,10.177.58.159:9092,10.177.58.154:9092',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="not_cbss_k" password="Notcbss@2021";',
  'topic' = 'XKF_TBL_GDZX_SUBSCRIBE_USER',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_gray_flink`.`payment_indepen_data_paimom` (
  dts_collect_time STRING,
  dts_kaf_time STRING,
  dts_kaf_offset STRING,
  dts_kaf_part STRING,
  operate_type STRING,
  operate_time STRING,
  operate_type_desc STRING,
  Id STRING,
  phone_num STRING,
  is_delete STRING,
  create_user STRING,
  create_name STRING,
  create_time STRING,
  event_time TIMESTAMP(3),
  kafka_time TIMESTAMP(3),
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  PRIMARY KEY (phone_num) NOT ENFORCED
) WITH (
  'bucket' = '50',
  'bucket-key' = 'phone_num',
  'write-only' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'snapshot.time-retained' = '1h',
  'num-sorted-run.stop-trigger'='2147483647',
  'sort-spill-threshold'='10'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_gray_flink`.`payment_indepen_data_paimom`
SELECT
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',0) ) dts_collect_time,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',1) ) dts_kaf_time,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',2) ) dts_kaf_offset,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',3) ) dts_kaf_part,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',4) ) operate_type,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',5) ) operate_time,
   ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',5) ) operate_type_desc,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',6) ) id,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',7) ) phone_num,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',8) ) is_delete,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',9) ) create_user,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',10) ) create_name,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',11) ) create_time,
  DATA_RELEASE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME
  from `hive_catalog`.`ubd_sscj_gray_flink`.`kafka_payment_independent_item`/*+ OPTIONS('properties.group.id'='payment_gray','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;








/********************************************************************************
hive初始化文件

生产


hadoop fs -mkdir hdfs:///user/hh_slfn2_sschj/rts-payment
hadoop fs -mkdir hdfs:///user/hh_slfn2_sschj/rts-payment/hive

show databases;
use ubd_sscj_prod_flink;
show tables;
show ubd_sscj_prod_flink;

CREATE TABLE `payment_data`(
  `dts_collect_time` string,
  `dts_kaf_time` string,
  `dts_kaf_offset` string,
  `dts_kaf_part` string,
  `operate_type` string,
  `operate_time` string,
  `operate_type_desc` string,
  `Id` string,
  `phone_num` string,
  `is_delete` string,
  `create_user` string,
  `create_name` string,
  `create_time` string
  )
stored as textfile



PARTITIONED BY (
  `phone_num` string
  )
ROW FORMAT SERDE
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
  'escape.delim'='\\',
  'field.delim'='\u0001',
  'serialization.null.format'='')
STORED AS INPUTFORMAT
  'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
  'hdfs:///user/hh_slfn2_sschj_gray/rts-payment/hive'

hadoop fs -get hdfs://10.177.78.195:8020/user/hh_arm_prod_sschj/liuyanhui/sscj_data/dwd_r_tbl_gdzx_subscribe_user.txt
hadoop fs -get hdfs://10.177.78.194:8020/user/hh_arm_prod_sschj/liuyanhui/sscj_data/dwd_r_tbl_gdzx_subscribe_user_20230928.txt
hadoop fs -put /data/disk01/hh_slfn2_sschj/dwd_r_tbl_gdzx_subscribe_user_20230928.txt hdfs:///user/hh_slfn2_sschj/rts-payment/hive

load data inpath "hdfs:///user/hh_slfn2_sschj/rts-payment/hive/dwd_r_tbl_gdzx_subscribe_user_20230928.txt" into table payment_data;

初始化入湖

export _JAVA_OPTIONS=-Djava.io.tmpdir=/data/disk01/hh_slfn2_sschj/private/jyt/tmp/flink17/
~/components/flink-1.17.0-sql/bin/yarn-session.sh -qu hh_slfn2_sschj -s 4 -nm flink-17-sql_jyt -jm 16g -tm 16g -d
;
~/components/flink-1.17.0-sql/bin/sql-client.sh -s yarn-session -i ~/shell/catalog/paimon_catalog.sql

use catalog hive_catalog;

USE CATALOG paimon_catalog;

set sql-client.execution.result-mode=TABLEAU;
set execution.runtime-mode=BATCH;
set parallelism.default=10;


--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon` (
  dts_collect_time STRING,
  dts_kaf_time STRING,
  dts_kaf_offset STRING,
  dts_kaf_part STRING,
  operate_type STRING,
  operate_time STRING,
  operate_type_desc STRING,
  Id STRING,
  phone_num STRING,
  is_delete STRING,
  create_user STRING,
  create_name STRING,
  create_time STRING,
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  PRIMARY KEY (phone_num) NOT ENFORCED
) WITH (
  'bucket' = '50',
  'bucket-key' = 'phone_num',
  'write-only' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'snapshot.time-retained' = '1h',
  'num-sorted-run.stop-trigger'='2147483647',
  'sort-spill-threshold'='10'
);

delete from `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon`;

select * from `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon` limit 1;
select count(create_user) as total from `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon`;

select count(create_user) as total from `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon` where is_delete = "0" ;
insert OVERWRITE
  `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon`
SELECT
  dts_collect_time,
  dts_kaf_time,
  dts_kaf_offset,
  dts_kaf_part,
  operate_type,
  operate_time,
  operate_type_desc,
  id,
  phone_num,
  is_delete,
  create_user,
  create_name,
  create_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`payment_data`;

SELECT
  count(*) as total
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`payment_data`;

、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、


CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = '/etc/hive/3.1.5.0-152/0'
);
USE CATALOG hive_catalog;
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
SET 'table.exec.sink.not-null-enforcer'='DROP';

-- CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';
--kafka 表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`kafka_payment_independent_item` (
  dts_collect_time STRING,
  dts_kaf_time STRING,
  dts_kaf_offset STRING,
  dts_kaf_part STRING,
  operate_type STRING,
  operate_time STRING,
  operate_type_desc STRING,
  id STRING,
  phone_num STRING,
  is_delete STRING,
  create_user STRING,
  create_name STRING,
  create_time STRING
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.58.154:9092,10.177.58.155:9092,10.177.58.156:9092,10.177.58.157:9092,10.177.58.158:9092,10.177.58.159:9092,10.177.58.154:9092',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="not_cbss_k" password="Notcbss@2021";',
  'topic' = 'XKF_TBL_GDZX_SUBSCRIBE_USER',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'json'
);
--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon` (
  dts_collect_time STRING,
  dts_kaf_time STRING,
  dts_kaf_offset STRING,
  dts_kaf_part STRING,
  operate_type STRING,
  operate_time STRING,
  operate_type_desc STRING,
  id STRING,
  phone_num STRING,
  is_delete STRING,
  create_user STRING,
  create_name STRING,
  create_time STRING,
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  PRIMARY KEY (phone_num) NOT ENFORCED
) WITH (
  'bucket' = '50',
  'bucket-key' = 'phone_num',
  'write-only' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'snapshot.time-retained' = '1h',
  'num-sorted-run.stop-trigger'='2147483647',
  'sort-spill-threshold'='10'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon`
SELECT
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',0) ) dts_collect_time,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',1) ) dts_kaf_time,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',2) ) dts_kaf_offset,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',3) ) dts_kaf_part,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',4) ) operate_type,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',5) ) operate_time,
   ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',6) ) operate_type_desc,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',7) ) id,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',8) ) phone_num,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',9) ) is_delete,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',10) ) create_user,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',11) ) create_name,
  ( COP_SPLIT_INDEX(RAW_FIELD,'\u0001',12) ) create_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME
  from `hive_catalog`.`ubd_sscj_prod_flink`.`kafka_payment_independent_item`/*+ OPTIONS('properties.group.id'='payment_prod','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='earliest','properties.auto.commit.interval.ms'='1000')*/
;





kafka/bin/kafka-console-consumer.sh --bootstrap-server 10.177.58.154:9092,10.177.58.155:9092,10.177.58.156:9092,10.177.58.157:9092,10.177.58.158:9092,10.177.58.159:9092,10.177.58.154:9092 --consumer.config cjzhsecurity.conf --topic XKF_TBL_GDZX_SUBSCRIBE_USER




cjzhsecurity.conf
security.protocol=SASL_PLAINTEXT
sasl.mechanism=SCRAM-SHA-256
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="not_cbss_k" password="";