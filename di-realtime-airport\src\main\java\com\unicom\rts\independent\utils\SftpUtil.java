package com.unicom.rts.independent.utils;

import com.jcraft.jsch.*;
import com.unicom.rts.independent.AirportDataMain;
import org.apache.commons.io.IOUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * sftp工具类，包含以下功能：
 * 获取sftp链接
 * 关闭sftp链接
 * 下载文件
 * 上传文件
 * 删除文件
 * 查找文件
 * 更多功能自行拓展
 */
public class SftpUtil {

    private final static Logger logger = LoggerFactory.getLogger(SftpUtil.class);
    /**
     * 获取一个sftp链接
     *
     * @param host     sftp服务器ip
     * @param username 用户名
     * @param password 密码
     * @return 返回ChannelSftp
     * @throws Exception 获取通道时的异常
     */
    public static ChannelSftp getSftpChannel(String host, String username, String password) throws Exception {
        Session session;
        Channel channel = null;
        JSch jSch = new JSch();
        try {
            System.out.print("-----建立sftp通道");
            session = jSch.getSession(username, host, 22);
            session.setPassword(password);

            // 配置链接的属性
            Properties properties = new Properties();
            properties.setProperty("StrictHostKeyChecking", "no");
            session.setConfig(properties);

            // 进行sftp链接
            session.connect(1500);

            // 获取通信通道
            channel = session.openChannel("sftp");
            channel.connect();
            System.out.print("-----建立sftp成功");
        } catch (JSchException e) {
            e.printStackTrace();
            throw e;
        }
        return (ChannelSftp) channel;
    }

    /**
     * 上传文件
     *
     * @param channelSftp sftp通道
     * @param pathStr     本地文件
     * @param remoteFile  远程文件
     */
    public static void upload(ChannelSftp channelSftp, String pathStr, String remoteFile,String timeStr) {
        System.out.print("------------------sftp开始上传");
        InputStream inputStream = null;
        try {
            Configuration conf = new Configuration();
            String dataStr = pathStr+"/data/"+timeStr+"/";
            FileSystem dataFs = FileSystem.get(URI.create(dataStr), conf);

            Path dataPath = new Path(dataStr);
            boolean isExists = dataFs.exists(dataPath);
            System.out.println("--------------------isExists:"+isExists);
            FileSystem resultsFs = FileSystem.get(URI.create(pathStr+"results/"), conf);
            Path resultsPath = new Path(pathStr+"results/"+timeStr+".txt");
            if (isExists) {
                System.out.println("------------------开始合并-----");
                System.out.println("------------------sourcePath-----"+resultsPath);
                //final FSDataOutputStream create = fs.create(resultsPath);
//                FileStatus[] dataStatus = dataFs.listStatus(dataPath);
//                for (int i = 0; i < dataStatus.length;i++ ) {
//                    System.out.println("------------------status["+i+"].getPath()-----" + dataStatus[i].getPath().toString());
 //                   final  FSDataInputStream hdfsInStream = dataFs.open(dataStatus[i].getPath());
                    copyMerge(dataFs,dataPath,resultsFs,resultsPath,false);
//                    final List<String> readLines = IOUtils.readLines(hdfsInStream);
//                    for (String line : readLines) {
//                        create.write(line.getBytes());
//                    }
//                    hdfsInStream.close();
                    System.out.println("------------------文件合并-----");
//                }
               // create.close();
                System.out.println("------------------remoteFile-----" + remoteFile);
                FSDataInputStream hdfsInFile = resultsFs.open(resultsPath);
                channelSftp.put(hdfsInFile, remoteFile);
                System.out.println("------------------文件上传了！-----");
//                FileStatus[] status = fs.listStatus(path);
//                for (int i = 0; i < status.length; ) {
//                    System.out.println("------------------pathStr-----" + status[i].getPath().toString());
//                    System.out.println("------------------remoteFile-----" + remoteFile);
//                        FSDataInputStream hdfsInStream = fs.open(status[i].getPath());
//                        channelSftp.put(hdfsInStream, remoteFile);
//                        System.out.println("--------------------上传了");
//                        i++;
//                }


            }

        } catch (Exception e) {
            logger.error("文件上传失败，exported_job：{}" , e);
        }
    }

    /**
     * 从sftp服务器上下载文件
     *
     * @param channelSftp sftp通道
     * @param remoteFile  远程文件
     * @param localFile   本地文件
     */
    public static void download(ChannelSftp channelSftp, String remoteFile, String localFile) {
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(localFile);
        } catch (FileNotFoundException e) {
            logger.error("文件download失败，exported_job：{}" , e);
        }

        try {
            channelSftp.get(remoteFile, outputStream);
        } catch (SftpException e) {
            logger.error("文件download失败，exported_job：{}" , e);
        }
    }

    /**
     * 删除文件
     *
     * @param channelSftp sftp通道
     * @param remoteFile  远程文件
     */
    public static void deleteFile(ChannelSftp channelSftp, String remoteFile) throws Exception {
        try {
            channelSftp.rm(remoteFile);
        } catch (SftpException e) {
            logger.error("文件deleteFile失败，exported_job：{}" , e);
        }
    }

    /**
     * 关闭sftp链接
     *
     * @param channelSftp sftp通道
     * @throws Exception 关闭session的异常
     */
    public static void closeSession(ChannelSftp channelSftp) throws Exception {
        if (channelSftp == null) {
            return;
        }

        Session session = null;
        try {
            session = channelSftp.getSession();
        } catch (JSchException e) {
            logger.error("文件closeSession失败，exported_job：{}" , e);
            throw e;
        } finally {
            if (session != null) {
                session.disconnect();
            }
        }

    }
    public static boolean copyMerge(FileSystem srcFS, Path srcDir,
                                    FileSystem dstFS, Path dstFile,
                                    boolean deleteSource) throws IOException {
        if (!srcFS.getFileStatus(srcDir).isDirectory()) {
            return false;
        }

        OutputStream out = dstFS.create(dstFile);

        try {
            FileStatus contents[] = srcFS.listStatus(srcDir);
            Arrays.sort(contents);
            for (int i = 0; i < contents.length; i++) {
                System.out.println("------------------文件contents.length-----"+contents[i].getPath().toString());
                if (contents[i].isFile()) {
                    InputStream in = srcFS.open(contents[i].getPath());
                    try {
                        IOUtils.copy(in, out);
                    } finally {
                        in.close();
                    }
                }
            }
        } finally {
            out.close();
        }

        if (deleteSource) {
            return srcFS.delete(srcDir, true);
        } else {
            return true;
        }
    }
}