package com.unicom.rts.independent.processor;

import com.unicom.rts.independent.bean.RuleBean;
import com.unicom.rts.independent.beans.RecordBean;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.HTable;

import java.util.HashMap;


public interface Processor {
    void process(RecordBean recordBean, RuleBean ruleBean, HTable hTableRtsUserTag, String userTagColumnFamily,
                 MapState<String, String> checkoutState, HashMap<String,String> provMap, HashMap<String,String> areaMap,
                 Collector<Tuple2<String, String>> out) throws Exception;
}
