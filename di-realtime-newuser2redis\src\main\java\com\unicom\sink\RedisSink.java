package com.unicom.sink;

import com.alibaba.fastjson.JSONObject;
import com.unicom.beans.NewUserBean;
import com.unicom.utils.RedisUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import redis.clients.jedis.JedisCluster;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/23
 **/
public class RedisSink extends RichSinkFunction<NewUserBean> {

    private ParameterTool conf;

    private Integer expireTime;

    private RedisUtil redisutil;

    private JedisCluster jedis;

    public RedisSink(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        //初始化redis连接
        this.redisutil = new RedisUtil();
        this.jedis = redisutil.getConn(conf);
        this.expireTime = Integer.parseInt(conf.get("redis_expire_time"));
    }

    @Override
    public void invoke(NewUserBean input, Context context) throws Exception {
        String deviceNumber = input.getDeviceNumber();
        String key = "nui_"+deviceNumber;
        input.setInsertTime(new Date().toString());
        String jsonStr = JSONObject.toJSONString(input);

        System.out.println("to redis is " + key);
        redisutil.addKeyValueEx(jedis,key,jsonStr,expireTime);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(jedis != null) {
            jedis.close();
        }
    }
}
