package com.unicom.rts.util;

import com.unicom.rts.entity.ThrData;
import com.unicom.rts.entity.UserTagBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class PaimonUtil {

    public DataStream<ThrData> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(TF_F_USER + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, ThrData>) (row, collector) -> {
            if(StringUtils.isBlank((String) row.getField("SERIAL_NUMBER"))) {
                return;
            }
            ThrData thrData = new ThrData();
            thrData.setDeviceNumber((String) row.getField("SERIAL_NUMBER"));
            UserTagBean userTagBean = new UserTagBean();
            userTagBean.setUserId(row.getField("USER_ID") == null ? null : (String) row.getField("USER_ID"));
            userTagBean.setProvId(row.getField("PROVINCE_CODE") == null ? null : (String) row.getField("PROVINCE_CODE"));
            if(StringUtils.isNotBlank(userTagBean.getProvId()) && userTagBean.getProvId().trim().length() == 2) {
                userTagBean.setProvId("0" + userTagBean.getProvId().trim());
            }
            userTagBean.setCityCode(row.getField("CITY_CODE") == null ? null : (String) row.getField("CITY_CODE"));
            userTagBean.setAreaId(row.getField("EPARCHY_CODE") == null ? null : (String) row.getField("EPARCHY_CODE"));
            thrData.setUserTagBean(userTagBean);
            thrData.setDatasource("tag");
            collector.collect(thrData);
        }).returns(ThrData.class).uid("UserTagFlatMap").name("UserTagFlatMap")
                .setParallelism(conf.getInt("UserTagFlatMap.parallelism", 8));
    }

    private static final String TF_F_USER = "SELECT\n" +
            "    USER_ID USER_ID,\n" +
            "    SERIAL_NUMBER SERIAL_NUMBER,\n" +
            "    PROVINCE_CODE PROVINCE_CODE,\n" +
            "    EPARCHY_CODE EPARCHY_CODE,\n" +
            "    CITY_CODE CITY_CODE\n" +
            "    FROM ods_r_paimon_tf_f_user";
}
