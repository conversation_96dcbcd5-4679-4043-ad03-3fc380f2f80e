package com.unicom.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.LinkedHashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * @create 2021-06-11 19:58
 */

@Slf4j
public class RedisPool {

	private static JedisCluster jedisCluster = null;


	private RedisPool() {
	}

	public static synchronized JedisCluster getPool(ParameterTool conf) {
		// 只有当jedisCluster为空时才实例化
		if (jedisCluster == null) {
			JedisPoolConfig config = new JedisPoolConfig();
			Integer timeout = Integer.valueOf(conf.get("redis_client_timeout"));
			config.setMaxTotal(Integer.valueOf(conf.get("redis_max_total")));//最大空闲连接数
			config.setMaxIdle(Integer.valueOf(conf.get("redis_max_idle")));
			config.setMinIdle(Integer.valueOf(conf.get("redis_min_idle")));
			config.setNumTestsPerEvictionRun(Integer.valueOf(conf.get("redis_numTestsPerEvictionRun")));//每次释放连接的最大数目
			config.setTimeBetweenEvictionRunsMillis(Integer.valueOf(conf.get("redis_timeBetweenEvictionRunsMillis"))); //释放连接的扫描间隔（毫秒）
			config.setMinEvictableIdleTimeMillis(Integer.valueOf(conf.get("redis_minEvictableIdleTimeMillis")));//连接最小空闲时间
			config.setSoftMinEvictableIdleTimeMillis(Integer.valueOf(conf.get("redis_softMinEvictableIdleTimeMillis")));//连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放
			config.setMaxWaitMillis(Integer.valueOf(conf.get("redis_maxWaitMillis")));//获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
			config.setTestOnBorrow(Boolean.valueOf(conf.get("redis_testOnBorrow")));//在获取连接的时候检查有效性, 默认false
			config.setTestWhileIdle(Boolean.valueOf(conf.get("redis_testWhileIdle")));//在空闲时检查有效性, 默认false
			config.setBlockWhenExhausted(Boolean.valueOf(conf.get("redis_blockWhenExhausted")));//连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true

			//redis集群，多个节点
			String serverip = conf.get("redis_hosts").trim();
			String password = conf.get("redis_auth_password");
			System.out.println("password is " + password);
			String[] serveripArray = serverip.split(",");

			Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
			for (String str : serveripArray) {
				String ip = str.split(":")[0];
				int port = Integer.parseInt(str.split(":")[1]);
				nodes.add(new HostAndPort(ip, port));
			}

			jedisCluster = new JedisCluster(nodes, timeout, timeout, 5, password,config);

		}
		return jedisCluster;
	}

}
