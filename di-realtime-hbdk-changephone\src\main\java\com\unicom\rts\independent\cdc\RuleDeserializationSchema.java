// package com.unicom.rts.independent.cdc;
//
// import com.alibaba.fastjson.JSONObject;
// import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
// import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
// import org.apache.flink.api.common.typeinfo.TypeInformation;
// import org.apache.flink.util.Collector;
// import org.apache.kafka.connect.data.Field;
// import org.apache.kafka.connect.data.Struct;
// import org.apache.kafka.connect.source.SourceRecord;
//
// public class RuleDeserializationSchema implements DebeziumDeserializationSchema<String> {
//     @Override
//     public void deserialize(SourceRecord sourceRecord, Collector<String> collector) throws Exception {
//         JSONObject jsonObject = new JSONObject();
//         Struct value = ((Struct) sourceRecord.value());
//         String operatorType = value.getString("op");
//
//         // c : create, u: update, d: delete, r: read
//         // insert update
//         if (!"d".equals(operatorType)) {
//             Struct after = value.getStruct("after");
//             JSONObject afterJsonObject = parseRecord(after);
//             jsonObject.put("after", afterJsonObject);
//
//         }
//
//         // delete
//         if ("d".equals(operatorType)) {
//             Struct source = value.getStruct("before");
//             JSONObject beforeJsonObject = parseRecord(source);
//             jsonObject.put("after", beforeJsonObject);
//         }
//
//         jsonObject.put("op", operatorType);
//         collector.collect(jsonObject.toString());
//     }
//
//     @Override
//     public TypeInformation<String> getProducedType() {
//         return BasicTypeInfo.STRING_TYPE_INFO;
//     }
//
//     private JSONObject parseRecord(Struct after) {
//         JSONObject jo = new JSONObject();
//         for (Field field : after.schema().fields()) {
//             switch ((field.schema()).type()) {
//                 case INT8:
//                     int resultInt8 = after.getInt8(field.name());
//                     jo.put(field.name(), resultInt8);
//                     break;
//                 case INT32:
//                     int resultInt32 = after.getInt32(field.name());
//                     jo.put(field.name(), resultInt32);
//                     break;
//                 case INT64:
//                     Long resultInt = after.getInt64(field.name());
//                     jo.put(field.name(), resultInt);
//                     break;
//                 case FLOAT32:
//                     Float resultFloat32 = after.getFloat32(field.name());
//                     jo.put(field.name(), resultFloat32);
//                     break;
//                 case FLOAT64:
//                     Double resultFloat64 = after.getFloat64(field.name());
//                     jo.put(field.name(), resultFloat64);
//                     break;
//                 case BYTES:
// //                                     json ignore byte column
// //                    byte[] resultByte = after.getBytes(field.name());
// //                    jo.put(field.name(), String.valueOf(resultByte));
//                     break;
//                 case STRING:
//                     String resultStr = after.getString(field.name());
//                     jo.put(field.name(), resultStr);
//                     break;
//                 default:
//             }
//         }
//
//         return jo;
//     }
//
// }
