package com.unicom.rts.pojo;


import com.unicom.rts.util.StringUtil;
import com.unicom.rts.util.TimeUtil;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.unicom.rts.util.TimeUtil.convertTimestamp2Date;


/**
 * <AUTHOR>
 * @date 2021-04-08 15:03
 */
public class SetXhzwZCEntry {
    private final static Logger logger = LoggerFactory.getLogger(SetXhzwZCEntry.class);


    public static XhzwZCEntry setXhzwEntry(XhzwZCEntry xhzwZCEntry, String[] splitsValueArr, Cell[] cells) {

        xhzwZCEntry.setZhangqi(TimeUtil.getYmdDate()); //设置账期(yyyyMMdd格式)
        String revosdishi = StringUtil.revosdishi(splitsValueArr[12]); //重新构建地市编码（原来最后一位编码放到第一位）
        //通过topic数据填充数据实体省份地市字段
        //如果mpd里的省份编码为011、013、031、083，设置省份和地市均为重构后的地市编码
        if (("011").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(revosdishi);
            xhzwZCEntry.setDishi(revosdishi);
        } else if (("013").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(revosdishi);
            xhzwZCEntry.setDishi(revosdishi);
        } else if (("031").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(revosdishi);
            xhzwZCEntry.setDishi(revosdishi);
        } else if (("083").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(revosdishi);
            xhzwZCEntry.setDishi(revosdishi);
            //如果mpd里的省份编码为050，地市编码需要进行如下转换
        } else if (("050").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(splitsValueArr[11]);
            if (("50a079").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else if (("460100").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else if (("0898").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else {
                xhzwZCEntry.setDishi(revosdishi);
            }
            //其他情况下，省份字段设置为省份编码，地市字段设置为重构地市编码
        } else {
            xhzwZCEntry.setProv(splitsValueArr[11]);
            xhzwZCEntry.setDishi(revosdishi);
        }
        //通过hbase数据填充数据实体
        for (Cell cell : cells) {
            String cloumn = new String(CellUtil.cloneQualifier(cell));
            if (("q9").equals(cloumn)) {
                xhzwZCEntry.setYhbm(new String(CellUtil.cloneValue(cell)));//用户编码
            } else if (("q18").equals(cloumn)) {
                xhzwZCEntry.setSlgh(new String(CellUtil.cloneValue(cell)));//受理员工
            } else if (("q25").equals(cloumn)) {
                xhzwZCEntry.setRwsj(new String(CellUtil.cloneValue(cell)));//入网时间（待修改）
            } else if (("q43").equals(cloumn)) {
                xhzwZCEntry.setRwqdbm(new String(CellUtil.cloneValue(cell)));//入网渠道编码
            } else if (("q44").equals(cloumn)) {
                xhzwZCEntry.setQdlx(new String(CellUtil.cloneValue(cell)));//渠道类型
            } else if (("q45").equals(cloumn)) {
                xhzwZCEntry.setRwqdmc(new String(CellUtil.cloneValue(cell)));//入网渠道名称
            }
        }
        //通过topic数据填充数据实体字段
        xhzwZCEntry.setShoujihao(splitsValueArr[1]);//手机号
        xhzwZCEntry.setSqxcsj(splitsValueArr[2]);//申请携出时间（待修改）
        xhzwZCEntry.setWlday("0");
        xhzwZCEntry.setSxsj(splitsValueArr[8]);//开始时间（待修改）

        xhzwZCEntry.setYsyys(StringUtil.get3yys(splitsValueArr[5]));//号码拥有方网络id
        xhzwZCEntry.setXcyys(StringUtil.get3yys(splitsValueArr[3]));//携出方网络id
        xhzwZCEntry.setXryys(StringUtil.get3yys(splitsValueArr[4]));//携入方网络id

        try {
//            String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
//                    splitsValueArr[1], "f", "off_k002996");
//            String productType = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
//                    splitsValueArr[1], "f", "off_k000058");
            xhzwZCEntry.setXcdycplx("");
            xhzwZCEntry.setCpmc("");

            xhzwZCEntry.setSyzdwllx("");
            xhzwZCEntry.setXcsyyhczsr("");
            xhzwZCEntry.setSfcg("");
            xhzwZCEntry.setSbyy("");
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return xhzwZCEntry;


    }

    public static String pjProducerValue(XhzwZCEntry xhzwZCEntry) {

        String producerValue = xhzwZCEntry.getZhangqi() + "\u0001" + xhzwZCEntry.getProv() + "\u0001" + xhzwZCEntry.getDishi() + "\u0001" + xhzwZCEntry.getQdlx() + "\u0001" +
                xhzwZCEntry.getRwqdbm() + "\u0001" + xhzwZCEntry.getRwqdmc() + "\u0001" + xhzwZCEntry.getSlgh() + "\u0001" + xhzwZCEntry.getShoujihao() + "\u0001" + xhzwZCEntry.getYhbm() + "\u0001" +
                xhzwZCEntry.getRwsj() + "\u0001" + xhzwZCEntry.getWlday() + "\u0001" + xhzwZCEntry.getSqxcsj() + "\u0001" + xhzwZCEntry.getSxsj() + "\u0001" + xhzwZCEntry.getYsyys() + "\u0001" +
                xhzwZCEntry.getXcyys() + "\u0001" + xhzwZCEntry.getXryys() + "\u0001" + xhzwZCEntry.getXcdycplx() + "\u0001" + xhzwZCEntry.getCpmc() + "\u0001" + xhzwZCEntry.getSyzdwllx() + "\u0001" +
                xhzwZCEntry.getXcsyyhczsr() + "\u0001" + xhzwZCEntry.getSfcg() + "\u0001" + xhzwZCEntry.getSbyy();
        return producerValue;
    }


    public static XhzwZCEntry setXhzwEntry_new(XhzwZCEntry xhzwZCEntry, String[] splitsValueArr, Cell[] cells) {

        xhzwZCEntry.setZhangqi(TimeUtil.getYmdDate()); //设置账期(yyyyMMdd格式)
        String revosdishi = StringUtil.revosdishi(splitsValueArr[12]); //重新构建地市编码（原来最后一位编码放到第一位）
        //通过topic数据填充数据实体省份地市字段
        //如果mpd里的省份编码为011、013、031、083，设置省份和地市均为重构后的地市编码
        if (("011").equals(splitsValueArr[11]) || ("0010").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv("011");
            xhzwZCEntry.setDishi("011");
        } else if (("013").equals(splitsValueArr[11]) || ("0022").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv("013");
            xhzwZCEntry.setDishi("013");
        } else if (("031").equals(splitsValueArr[11]) || ("0021").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv("031");
            xhzwZCEntry.setDishi("031");
        } else if (("083").equals(splitsValueArr[11]) || ("0023").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv("083");
            xhzwZCEntry.setDishi("083");
            //如果mpd里的省份编码为050，地市编码需要进行如下转换
        } else if (("050").equals(splitsValueArr[11])) {
            xhzwZCEntry.setProv(splitsValueArr[11]);
            if (("50a079").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else if (("460100").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else if (("0898").equals(splitsValueArr[12])) {
                xhzwZCEntry.setDishi("502521");
            } else {
                xhzwZCEntry.setDishi(revosdishi);
            }
            //其他情况下，省份字段设置为省份编码，地市字段设置为重构地市编码
        } else {
            xhzwZCEntry.setProv(splitsValueArr[11]);
            xhzwZCEntry.setDishi(revosdishi);
        }
        for (Cell cell : cells) {
            String cloumn = new String(CellUtil.cloneQualifier(cell));
            if (("q9").equals(cloumn)) {
                xhzwZCEntry.setYhbm(new String(CellUtil.cloneValue(cell)));//用户编码
            } else if (("q18").equals(cloumn)) {
                xhzwZCEntry.setSlgh(new String(CellUtil.cloneValue(cell)));//受理员工
            } else if (("q25").equals(cloumn)) {
                xhzwZCEntry.setRwsj(convertTimestamp2Date(new String(CellUtil.cloneValue(cell)), "yyyyMMddHHmmss"));//入网时间（待修改）
            } else if (("q43").equals(cloumn)) {
                xhzwZCEntry.setRwqdbm(new String(CellUtil.cloneValue(cell)));//入网渠道编码
            } else if (("q44").equals(cloumn)) {
                xhzwZCEntry.setQdlx(new String(CellUtil.cloneValue(cell)));//渠道类型
            } else if (("q45").equals(cloumn)) {
                xhzwZCEntry.setRwqdmc(new String(CellUtil.cloneValue(cell)));//入网渠道名称
            }
        }
        //通过topic数据填充数据实体字段
        xhzwZCEntry.setShoujihao(splitsValueArr[1]);//手机号
        xhzwZCEntry.setSqxcsj(convertTimestamp2Date(splitsValueArr[2], "yyyyMMddHHmmss"));//申请携出时间（待修改）
        xhzwZCEntry.setWlday("0");
        xhzwZCEntry.setSxsj(convertTimestamp2Date(splitsValueArr[8], "yyyyMMddHHmmss"));//开始时间（待修改）

        xhzwZCEntry.setYsyys(StringUtil.get3yys(splitsValueArr[5]));//号码拥有方网络id
        xhzwZCEntry.setXcyys(StringUtil.get3yys(splitsValueArr[3]));//携出方网络id
        xhzwZCEntry.setXryys(StringUtil.get3yys(splitsValueArr[4]));//携入方网络id

        try {
//            String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
//                    splitsValueArr[1], "f", "off_k002996");
//            String productType = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
//                    splitsValueArr[1], "f", "off_k000058");
            xhzwZCEntry.setXcdycplx("");
            xhzwZCEntry.setCpmc("");

            xhzwZCEntry.setSyzdwllx("");
            xhzwZCEntry.setXcsyyhczsr("");
            xhzwZCEntry.setSfcg("");
            xhzwZCEntry.setSbyy("");
        } catch (Exception e) {
            logger.error("runException:{}", e.getMessage());
        }

        return xhzwZCEntry;


    }


}