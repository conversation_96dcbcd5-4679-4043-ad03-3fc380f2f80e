package com.unicom.rts.independent.utils;

import com.unicom.rts.independent.bean.CheckoutDataBean;
import com.unicom.rts.independent.bean.MidDataBean;
import com.unicom.rts.independent.sink.bucketassigners.MyDateTimeBucketAssigner;
import com.unicom.rts.independent.sink.writerFactory.GzipBulkStringWriterFactory;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.filesystem.OutputFileConfig;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.OnCheckpointRollingPolicy;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DataStreamToHdfsUtil {

    public static void stringDataStreamToHdfs (DataStream<String> dataStream, int parallelism, String hdfsHomePath, String partprefix) {

        Path homePath =  new Path(hdfsHomePath);
        OutputFileConfig outputFileConfig = new OutputFileConfig(partprefix + "part",".gz");

        //落盘，按账期分目录落文件
        StreamingFileSink<String> streamingFileSinkForBulkFormat = StreamingFileSink
                .forBulkFormat(homePath, new GzipBulkStringWriterFactory()) //指定父目录，gzip压缩格式
                .withBucketAssigner(new MyDateTimeBucketAssigner<String>("yyyyMMdd")) //按日期分目录
                .withRollingPolicy(OnCheckpointRollingPolicy.build())
                .withBucketCheckInterval(10)
                .withOutputFileConfig(outputFileConfig)
                .build();

        dataStream.addSink(streamingFileSinkForBulkFormat).name("tohdfs").setParallelism(parallelism);
    }


    public static void midDataStreamToHdfs (DataStream<MidDataBean> dataStream, ParameterTool appConf) {
        int parallelism = Integer.parseInt(appConf.get("hdfs.middata.parallelism"));
        String homePath = appConf.get("hdfs.middata.homepath");
        DataStream<String> hdfsStream = dataStream.map(new MapFunction<MidDataBean, String>() {
            @Override
            public String map(MidDataBean midDataInfoBean) throws Exception {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
                return midDataInfoBean.toString() + "|" + formatter.format(new Date());
            }
        }).name("transToString").setParallelism(parallelism);
        stringDataStreamToHdfs(hdfsStream,parallelism,homePath,appConf.get("partprefix"));
    }


    public static void checkoutStreamToHdfs (DataStream<CheckoutDataBean> dataStream, ParameterTool appConf) {
        int parallelism = Integer.parseInt(appConf.get("hdfs.checkout.parallelism"));
        String homePath = appConf.get("hdfs.checkout.homepath");
        DataStream<String> hdfsStream = dataStream.map(new MapFunction<CheckoutDataBean, String>() {
            @Override
            public String map(CheckoutDataBean checkoutDataBean) throws Exception {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
                return checkoutDataBean.toString() + "|" + formatter.format(new Date());
            }
        }).name("transToString").setParallelism(parallelism);
        stringDataStreamToHdfs(hdfsStream,parallelism,homePath,appConf.get("partprefix"));
    }
}
