CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_template_conf` (
  template_id string,
  name string,
  product_category_classification string,
  composition_type string,
  use_range string,
  amount string,
  settlement_purpose string,
  equity_required string,
  is_conversion_increase string,
  is_polling string,
  receive_type string,
  first_receive string,
  receive_frequency string,
  receive_frequency_unit string,
  receive_amount string,
  receive_limit_amount string,
  receive_number string,
  receive_total_count string,
  exchange_type string,
  exchange_start string,
  exchange_end string,
  exchange_end_unit string,
  status string,
  last_status string,
  create_time string,
  create_staff_id string,
  create_staff_code string,
  update_time string,
  update_staff_id string,
  update_staff_code string,
  province_code string,
  start_time string,
  end_time string,
  enable_tag string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_TEMPLATE_CONF',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_template_conf_log` (
  template_id string,
  name string,
  product_category_classification string,
  composition_type string,
  use_range string,
  amount string,
  settlement_purpose string,
  equity_required string,
  is_conversion_increase string,
  is_polling string,
  receive_type string,
  first_receive string,
  receive_frequency string,
  receive_frequency_unit string,
  receive_amount string,
  receive_limit_amount string,
  receive_number string,
  receive_total_count string,
  exchange_type string,
  exchange_start string,
  exchange_end string,
  exchange_end_unit string,
  status string,
  last_status string,
  create_time string,
  create_staff_id string,
  create_staff_code string,
  update_time string,
  update_staff_id string,
  update_staff_code string,
  province_code string,
  start_time string,
  end_time string,
  enable_tag string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '2',
  'bucket-key' = 'template_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_template_conf` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_template_conf`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_TEMPLATE_CONF_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_template_conf`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_TEMPLATE_CONF_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_template_conf_log`
select
  template_id,
  name,
  product_category_classification,
  composition_type,
  use_range,
  amount,
  settlement_purpose,
  equity_required,
  is_conversion_increase,
  is_polling,
  receive_type,
  first_receive,
  receive_frequency,
  receive_frequency_unit,
  receive_amount,
  receive_limit_amount,
  receive_number,
  receive_total_count,
  exchange_type,
  exchange_start,
  exchange_end,
  exchange_end_unit,
  status,
  last_status,
  create_time,
  create_staff_id,
  create_staff_code,
  update_time,
  update_staff_id,
  update_staff_code,
  province_code,
  start_time,
  end_time,
  enable_tag,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_template_conf`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_template_conf`
select
  template_id,
  name,
  product_category_classification,
  composition_type,
  use_range,
  amount,
  settlement_purpose,
  equity_required,
  is_conversion_increase,
  is_polling,
  receive_type,
  first_receive,
  receive_frequency,
  receive_frequency_unit,
  receive_amount,
  receive_limit_amount,
  receive_number,
  receive_total_count,
  exchange_type,
  exchange_start,
  exchange_end,
  exchange_end_unit,
  status,
  last_status,
  create_time,
  create_staff_id,
  create_staff_code,
  update_time,
  update_staff_id,
  update_staff_code,
  province_code,
  start_time,
  end_time,
  enable_tag,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_template_conf_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_TEMPLATE_CONF_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_party_product` (
  sp_product_id string,
  sp_product_name string,
  state string,
  sp_id string,
  sp_service_id string,
  is_package string,
  is_group string,
  is_gift string,
  confirm_prompt string,
  success_prompt string,
  cancel_prompt string,
  price_describe string,
  pay_mode_code string,
  need_confm string,
  freeuse_flag string,
  freeuse_time string,
  third_party_flag string,
  billing_mode string,
  billing_value string,
  max_use_sum string,
  max_use_times string,
  max_use_octets string,
  start_date string,
  end_date string,
  update_date string,
  product_type_code string,
  billing_id string,
  discount_id string,
  popularize_start string,
  popularize_stop string,
  discount_des string,
  send_num string,
  order_command string,
  order_cmd_match string,
  order_acct string,
  order_acc_match string,
  cancel_command string,
  cancel_cmd_match string,
  cancel_acc string,
  cancel_acc_match string,
  request_cmd string,
  request_cmd_match string,
  request_acc string,
  request_acc_match string,
  vac_sub string,
  notify_type string,
  product_city string,
  confirm string,
  rsrv_str1 string,
  rsrv_str2 string,
  party_id string,
  update_type string,
  sequence_id string,
  orderchannel string,
  cancelchannel string,
  pop_billing_id string,
  pop_discount_id string,
  is_every_month string,
  oper_type string,
  send_times string,
  tag string,
  productid string,
  spec_productid string,
  product_mode string,
  product_unit string,
  prod_period_grade string,
  prod_service_grade string,
  prod_credit string,
  group_service string,
  service_compose string,
  intro_url string,
  sp_order_url string,
  syn_order_func string,
  sp_psedo_flag string,
  net_tag string,
  classify string,
  duerece string,
  productnet string,
  productsubject string,
  parastring string,
  paravalues string,
  province_code string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_PARTY_PRODUCT',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_party_product_log` (
  sp_product_id string,
  sp_product_name string,
  state string,
  sp_id string,
  sp_service_id string,
  is_package string,
  is_group string,
  is_gift string,
  confirm_prompt string,
  success_prompt string,
  cancel_prompt string,
  price_describe string,
  pay_mode_code string,
  need_confm string,
  freeuse_flag string,
  freeuse_time string,
  third_party_flag string,
  billing_mode string,
  billing_value string,
  max_use_sum string,
  max_use_times string,
  max_use_octets string,
  start_date string,
  end_date string,
  update_date string,
  product_type_code string,
  billing_id string,
  discount_id string,
  popularize_start string,
  popularize_stop string,
  discount_des string,
  send_num string,
  order_command string,
  order_cmd_match string,
  order_acct string,
  order_acc_match string,
  cancel_command string,
  cancel_cmd_match string,
  cancel_acc string,
  cancel_acc_match string,
  request_cmd string,
  request_cmd_match string,
  request_acc string,
  request_acc_match string,
  vac_sub string,
  notify_type string,
  product_city string,
  confirm string,
  rsrv_str1 string,
  rsrv_str2 string,
  party_id string,
  update_type string,
  sequence_id string,
  orderchannel string,
  cancelchannel string,
  pop_billing_id string,
  pop_discount_id string,
  is_every_month string,
  oper_type string,
  send_times string,
  tag string,
  productid string,
  spec_productid string,
  product_mode string,
  product_unit string,
  prod_period_grade string,
  prod_service_grade string,
  prod_credit string,
  group_service string,
  service_compose string,
  intro_url string,
  sp_order_url string,
  syn_order_func string,
  sp_psedo_flag string,
  net_tag string,
  classify string,
  duerece string,
  productnet string,
  productsubject string,
  parastring string,
  paravalues string,
  province_code string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '2',
  'bucket-key' = 'sp_product_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_party_product` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_party_product`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join(
    select
      distinct a.element_id,
      a.element_type_code
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      a
      inner join(
        select
          distinct a.package_id
        from
          `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
          /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
          a
          inner join (
            select
              distinct b.product_id
            from
              `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
              /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
              b
              inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
              /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
              c on b.package_id = c.package_id
            where
              c.element_type_code = 'Q'
              and UPPER(b.opt) <> 'DELETE'
              and UPPER(c.opt) <> 'DELETE'
          ) d on a.product_id = d.product_id
      ) e on a.package_id = e.package_id
    where
      a.element_type_code = 'X'
  ) f on a.sp_service_id = f.element_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_party_product`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_party_product_log`
select
  sp_product_id,
  sp_product_name,
  state,
  sp_id,
  sp_service_id,
  is_package,
  is_group,
  is_gift,
  confirm_prompt,
  success_prompt,
  cancel_prompt,
  price_describe,
  pay_mode_code,
  need_confm,
  freeuse_flag,
  freeuse_time,
  third_party_flag,
  billing_mode,
  billing_value,
  max_use_sum,
  max_use_times,
  max_use_octets,
  start_date,
  end_date,
  update_date,
  product_type_code,
  billing_id,
  discount_id,
  popularize_start,
  popularize_stop,
  discount_des,
  send_num,
  order_command,
  order_cmd_match,
  order_acct,
  order_acc_match,
  cancel_command,
  cancel_cmd_match,
  cancel_acc,
  cancel_acc_match,
  request_cmd,
  request_cmd_match,
  request_acc,
  request_acc_match,
  vac_sub,
  notify_type,
  product_city,
  confirm,
  rsrv_str1,
  rsrv_str2,
  party_id,
  update_type,
  sequence_id,
  orderchannel,
  cancelchannel,
  pop_billing_id,
  pop_discount_id,
  is_every_month,
  oper_type,
  send_times,
  tag,
  productid,
  spec_productid,
  product_mode,
  product_unit,
  prod_period_grade,
  prod_service_grade,
  prod_credit,
  group_service,
  service_compose,
  intro_url,
  sp_order_url,
  syn_order_func,
  sp_psedo_flag,
  net_tag,
  classify,
  duerece,
  productnet,
  productsubject,
  parastring,
  paravalues,
  province_code,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_party_product`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_party_product`
select
  sp_product_id,
  sp_product_name,
  state,
  sp_id,
  sp_service_id,
  is_package,
  is_group,
  is_gift,
  confirm_prompt,
  success_prompt,
  cancel_prompt,
  price_describe,
  pay_mode_code,
  need_confm,
  freeuse_flag,
  freeuse_time,
  third_party_flag,
  billing_mode,
  billing_value,
  max_use_sum,
  max_use_times,
  max_use_octets,
  start_date,
  end_date,
  update_date,
  product_type_code,
  billing_id,
  discount_id,
  popularize_start,
  popularize_stop,
  discount_des,
  send_num,
  order_command,
  order_cmd_match,
  order_acct,
  order_acc_match,
  cancel_command,
  cancel_cmd_match,
  cancel_acc,
  cancel_acc_match,
  request_cmd,
  request_cmd_match,
  request_acc,
  request_acc_match,
  vac_sub,
  notify_type,
  product_city,
  confirm,
  rsrv_str1,
  rsrv_str2,
  party_id,
  update_type,
  sequence_id,
  orderchannel,
  cancelchannel,
  pop_billing_id,
  pop_discount_id,
  is_every_month,
  oper_type,
  send_times,
  tag,
  productid,
  spec_productid,
  product_mode,
  product_unit,
  prod_period_grade,
  prod_service_grade,
  prod_credit,
  group_service,
  service_compose,
  intro_url,
  sp_order_url,
  syn_order_func,
  sp_psedo_flag,
  net_tag,
  classify,
  duerece,
  productnet,
  productsubject,
  parastring,
  paravalues,
  province_code,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_party_product_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PARTY_PRODUCT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;

CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_service` (
  service_id string,
  service_name string,
  net_type_code string,
  intf_mode string,
  parent_type_code string,
  service_mode string,
  service_brand_code string,
  service_level string,
  service_state string,
  service_type string,
  start_date string,
  end_date string,
  remark string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_SERVICE',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_service_log` (
  service_id string,
  service_name string,
  net_type_code string,
  intf_mode string,
  parent_type_code string,
  service_mode string,
  service_brand_code string,
  service_level string,
  service_state string,
  service_type string,
  start_date string,
  end_date string,
  remark string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '2',
  'bucket-key' = 'service_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_service` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_service`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join(
    select
      distinct a.element_id,
      a.element_type_code
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      a
      inner join(
        select
          distinct a.package_id
        from
          `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
          /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
          a
          inner join (
            select
              distinct b.product_id
            from
              `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
              /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
              b
              inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
              /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
              c on b.package_id = c.package_id
            where
              c.element_type_code = 'Q'
              and UPPER(b.opt) <> 'DELETE'
              and UPPER(c.opt) <> 'DELETE'
          ) d on a.product_id = d.product_id
      ) e on a.package_id = e.package_id
    where
      a.element_type_code = 'S'
  ) f on a.service_id = f.element_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_service`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_service_log`
select
  service_id,
  service_name,
  net_type_code,
  intf_mode,
  parent_type_code,
  service_mode,
  service_brand_code,
  service_level,
  service_state,
  service_type,
  start_date,
  end_date,
  remark,
  update_staff_id,
  update_depart_id,
  update_time,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_service`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_service`
select
  service_id,
  service_name,
  net_type_code,
  intf_mode,
  parent_type_code,
  service_mode,
  service_brand_code,
  service_level,
  service_state,
  service_type,
  start_date,
  end_date,
  remark,
  update_staff_id,
  update_depart_id,
  update_time,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_service_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_SERVICE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_discnt` (
  discnt_code string,
  discnt_name string,
  discnt_explain string,
  b_discnt_code string,
  a_discnt_code string,
  obj_type_code string,
  enable_tag string,
  start_date string,
  end_date string,
  tag_set string,
  rsrv_str1 string,
  rsrv_str2 string,
  rsrv_str3 string,
  rsrv_str4 string,
  rsrv_str5 string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  c_discnt_code string,
  bind_svc_set string,
  end_mode string,
  main_discnt_type string,
  trans_type string,
  disnct_salefee string,
  disnct_costfee string,
  update_staff string,
  staff_prov_code string,
  config_orderno string,
  p_prov_code string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_DISCNT',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_discnt_log` (
  discnt_code string,
  discnt_name string,
  discnt_explain string,
  b_discnt_code string,
  a_discnt_code string,
  obj_type_code string,
  enable_tag string,
  start_date string,
  end_date string,
  tag_set string,
  rsrv_str1 string,
  rsrv_str2 string,
  rsrv_str3 string,
  rsrv_str4 string,
  rsrv_str5 string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  c_discnt_code string,
  bind_svc_set string,
  end_mode string,
  main_discnt_type string,
  trans_type string,
  disnct_salefee string,
  disnct_costfee string,
  update_staff string,
  staff_prov_code string,
  config_orderno string,
  p_prov_code string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '2',
  'bucket-key' = 'discnt_code',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_discnt` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_discnt`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join(
    select
      distinct a.element_id,
      a.element_type_code
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      a
      inner join(
        select
          distinct a.package_id
        from
          `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
          /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
          a
          inner join (
            select
              distinct b.product_id
            from
              `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
              /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
              b
              inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element` c on b.package_id = c.package_id
            where
              c.element_type_code = 'Q'
              and UPPER(b.opt) <> 'DELETE'
              and UPPER(c.opt) <> 'DELETE'
          ) d on a.product_id = d.product_id
      ) e on a.package_id = e.package_id
    where
      a.element_type_code = 'D'
  ) f on a.discnt_code = f.element_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_discnt`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_discnt_log`
select
  discnt_code,
  discnt_name,
  discnt_explain,
  b_discnt_code,
  a_discnt_code,
  obj_type_code,
  enable_tag,
  start_date,
  end_date,
  tag_set,
  rsrv_str1,
  rsrv_str2,
  rsrv_str3,
  rsrv_str4,
  rsrv_str5,
  update_staff_id,
  update_depart_id,
  update_time,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  c_discnt_code,
  bind_svc_set,
  end_mode,
  main_discnt_type,
  trans_type,
  disnct_salefee,
  disnct_costfee,
  update_staff,
  staff_prov_code,
  config_orderno,
  p_prov_code,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_discnt`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_discnt`
select
  discnt_code,
  discnt_name,
  discnt_explain,
  b_discnt_code,
  a_discnt_code,
  obj_type_code,
  enable_tag,
  start_date,
  end_date,
  tag_set,
  rsrv_str1,
  rsrv_str2,
  rsrv_str3,
  rsrv_str4,
  rsrv_str5,
  update_staff_id,
  update_depart_id,
  update_time,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  c_discnt_code,
  bind_svc_set,
  end_mode,
  main_discnt_type,
  trans_type,
  disnct_salefee,
  disnct_costfee,
  update_staff,
  staff_prov_code,
  config_orderno,
  p_prov_code,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_discnt_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_DISCNT_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;

CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_package` (
  package_id string,
  package_name string,
  package_type_code string,
  package_desc string,
  start_date string,
  end_date string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  min_number string,
  max_number string,
  need_exp string,
  component_id string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_PACKAGE',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_package_log` (
  package_id string,
  package_name string,
  package_type_code string,
  package_desc string,
  start_date string,
  end_date string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  min_number string,
  max_number string,
  need_exp string,
  component_id string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '2',
  'bucket-key' = 'package_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_package` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join(
    select
      distinct a.package_id
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      a
      inner join (
        select
          distinct b.product_id
        from
          `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
          /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
          b
          inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
          /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
          c on b.package_id = c.package_id
        where
          c.element_type_code = 'Q'
          and UPPER(b.opt) <> 'DELETE'
          and UPPER(c.opt) <> 'DELETE'
      ) d on a.product_id = d.product_id
  ) e on a.package_id = e.package_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_package_log`
select
  package_id,
  package_name,
  package_type_code,
  package_desc,
  start_date,
  end_date,
  update_staff_id,
  update_depart_id,
  update_time,
  min_number,
  max_number,
  need_exp,
  component_id,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_package`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_package`
select
  package_id,
  package_name,
  package_type_code,
  package_desc,
  start_date,
  end_date,
  update_staff_id,
  update_depart_id,
  update_time,
  min_number,
  max_number,
  need_exp,
  component_id,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_package_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PACKAGE_2024092502', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product_item` (
  product_id string,
  attr_code string,
  attr_value string,
  area_code string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_PRODUCT_ITEM',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_item_log` (
  product_id string,
  attr_code string,
  attr_value string,
  area_code string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '4',
  'bucket-key' = 'product_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product_item` as
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product_item`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_ITEM_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join (
    select
      distinct b.product_id
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_ITEM_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      b
      inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_ITEM_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      c on b.package_id = c.package_id
    where
      c.element_type_code = 'Q'
      and UPPER(b.opt) <> 'DELETE'
      and UPPER(c.opt) <> 'DELETE'
  ) d on a.PRODUCT_ID = d.product_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product_item`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_ITEM_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_item_log`
select
  PRODUCT_ID,
  ATTR_CODE,
  ATTR_VALUE,
  AREA_CODE,
  UPDATE_STAFF_ID,
  UPDATE_DEPART_ID,
  UPDATE_TIME,
  opt,
  paimon_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product_item`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product_item`
select
  product_id,
  attr_code,
  attr_value,
  area_code,
  update_staff_id,
  update_depart_id,
  update_time,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_item_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_ITEM_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product_package` (
  product_id string,
  package_id string,
  force_tag string,
  default_tag string,
  min_number string,
  max_number string,
  rsrv_str1 string,
  rsrv_str2 string,
  start_date string,
  end_date string,
  eparchy_code string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  item_index string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_PRODUCT_PACKAGE',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_package_log` (
  product_id string,
  package_id string,
  force_tag string,
  default_tag string,
  min_number string,
  max_number string,
  rsrv_str1 string,
  rsrv_str2 string,
  start_date string,
  end_date string,
  eparchy_code string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  item_index string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '4',
  'bucket-key' = 'product_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product_package` as
select
  distinct a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_PACKAGE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join (
    select
      distinct b.product_id
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_PACKAGE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      b
      inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_PACKAGE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      c on b.package_id = c.package_id
    where
      c.element_type_code = 'Q'
      and UPPER(b.opt) <> 'DELETE'
      and UPPER(c.opt) <> 'DELETE'
  ) d on a.product_id = d.product_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  distinct a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_PACKAGE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_package_log`
select
  product_id,
  package_id,
  force_tag,
  default_tag,
  min_number,
  max_number,
  rsrv_str1,
  rsrv_str2,
  start_date,
  end_date,
  eparchy_code,
  update_staff_id,
  update_depart_id,
  update_time,
  item_index,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product_package`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product_package`
select
  product_id,
  package_id,
  force_tag,
  default_tag,
  min_number,
  max_number,
  rsrv_str1,
  rsrv_str2,
  start_date,
  end_date,
  eparchy_code,
  update_staff_id,
  update_depart_id,
  update_time,
  item_index,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_package_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_PACKAGE_2024092501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka 表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_product_v2` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = 'CB_TD_B_PRODUCT',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_v2` (
  product_id STRING,
  product_name STRING,
  product_explain STRING,
  product_mode STRING,
  net_type_code STRING,
  brand_code STRING,
  group_brand_code STRING,
  service_id STRING,
  product_obj_type STRING,
  res_type_code STRING,
  declared_product_id STRING,
  comp_tag STRING,
  enable_tag STRING,
  start_absolute_date STRING,
  start_offset STRING,
  start_unit STRING,
  end_enable_tag STRING,
  end_absolute_date STRING,
  end_offset STRING,
  end_unit STRING,
  start_date STRING,
  end_date STRING,
  min_number STRING,
  max_number STRING,
  create_date STRING,
  version STRING,
  product_state STRING,
  update_staff_id STRING,
  update_depart_id STRING,
  update_time STRING,
  prepay_tag STRING,
  need_exp STRING,
  product_app_type STRING,
  prod_id STRING,
  rsrv_value1 STRING,
  rsrv_value2 STRING,
  rsrv_value3 STRING,
  rsrv_value4 STRING,
  rsrv_value5 STRING,
  province_code STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING,
  product_btype STRING,
  product_desc STRING,
  product_oldname STRING,
  product_oldexpl STRING,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3) COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (product_id) NOT ENFORCED
) WITH (
  'bucket' = '1',
  'bucket-key' = 'product_id',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'snapshot.time-retained' = '1h',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_v2`
SELECT
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) PRODUCT_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) PRODUCT_NAME,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) PRODUCT_EXPLAIN,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) PRODUCT_MODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) NET_TYPE_CODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) BRAND_CODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) GROUP_BRAND_CODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) SERVICE_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) PRODUCT_OBJ_TYPE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) RES_TYPE_CODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) DECLARED_PRODUCT_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) COMP_TAG,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) ENABLE_TAG,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) START_ABSOLUTE_DATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) START_OFFSET,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) START_UNIT,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) END_ENABLE_TAG,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) END_ABSOLUTE_DATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 18)) END_OFFSET,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 19)) END_UNIT,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 20)) START_DATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 21)) END_DATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 22)) MIN_NUMBER,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 23)) MAX_NUMBER,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 24)) CREATE_DATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 25)) VERSION,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 26)) PRODUCT_STATE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 27)) UPDATE_STAFF_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 28)) UPDATE_DEPART_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 29)) UPDATE_TIME,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 30)) PREPAY_TAG,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 31)) NEED_EXP,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 32)) PRODUCT_APP_TYPE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 33)) PROD_ID,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 34)) RSRV_VALUE1,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 35)) RSRV_VALUE2,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 36)) RSRV_VALUE3,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 37)) RSRV_VALUE4,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 38)) RSRV_VALUE5,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 39)) PROVINCE_CODE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 40)) OPT,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 41)) OPTTIME,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 42)) CDHTIME,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 43)) PRODUCT_BTYPE,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 44)) PRODUCT_DESC,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 45)) PRODUCT_OLDNAME,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 46)) PRODUCT_OLDEXPL,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_product_v2`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_product_v2_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'execution.runtime-mode' = 'streaming';
--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product` (
  product_id string,
  product_name string,
  product_explain string,
  product_mode string,
  net_type_code string,
  brand_code string,
  group_brand_code string,
  service_id string,
  product_obj_type string,
  res_type_code string,
  declared_product_id string,
  comp_tag string,
  enable_tag string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  start_date string,
  end_date string,
  min_number string,
  max_number string,
  create_date string,
  version string,
  product_state string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  prepay_tag string,
  need_exp string,
  product_app_type string,
  prod_id string,
  rsrv_value1 string,
  rsrv_value2 string,
  rsrv_value3 string,
  rsrv_value4 string,
  rsrv_value5 string,
  province_code string,
  product_btype string,
  product_desc string,
  product_oldname string,
  product_oldexpl string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3)
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'SHQY_TD_B_PRODUCT',
  'scan.topic-partition-discovery.interval' = '30000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_log` (
  product_id string,
  product_name string,
  product_explain string,
  product_mode string,
  net_type_code string,
  brand_code string,
  group_brand_code string,
  service_id string,
  product_obj_type string,
  res_type_code string,
  declared_product_id string,
  comp_tag string,
  enable_tag string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  start_date string,
  end_date string,
  min_number string,
  max_number string,
  create_date string,
  version string,
  product_state string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  prepay_tag string,
  need_exp string,
  product_app_type string,
  prod_id string,
  rsrv_value1 string,
  rsrv_value2 string,
  rsrv_value3 string,
  rsrv_value4 string,
  rsrv_value5 string,
  province_code string,
  product_btype string,
  product_desc string,
  product_oldname string,
  product_oldexpl string,
  opt string,
  data_receive_time TIMESTAMP(3),
  data_release_time TIMESTAMP(3),
  process_out_date string
) partitioned by (process_out_date) WITH (
  'bucket' = '4',
  'bucket-key' = 'product_id',
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'write-mode' = 'append-only',
  'partition.expiration-time' = '7d',
  'partition.expiration-check-interval' = '1d',
  'partition.timestamp-formatter' = 'yyyyMMdd'
);
create temporary view if not exists `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product` as
select
  distinct a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_v2`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_2024102501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
  inner join (
    select
      distinct b.product_id
    from
      `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_2024102501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      b
      inner join `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package_element`
      /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_2024102501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
      c on b.package_id = c.package_id
    where
      c.element_type_code = 'Q'
      and UPPER(b.opt) <> 'DELETE'
      and UPPER(c.opt) <> 'DELETE'
  ) d on a.product_id = d.product_id
  and UPPER(a.opt) <> 'DELETE'
union
select
  distinct a.*,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_receive_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_v2`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_2024102501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
  a
where
  UPPER(a.opt) = 'DELETE';
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_log`
select
  product_id,
  product_name,
  product_explain,
  product_mode,
  net_type_code,
  brand_code,
  group_brand_code,
  service_id,
  product_obj_type,
  res_type_code,
  declared_product_id,
  comp_tag,
  enable_tag,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  start_date,
  end_date,
  min_number,
  max_number,
  create_date,
  version,
  product_state,
  update_staff_id,
  update_depart_id,
  update_time,
  prepay_tag,
  need_exp,
  product_app_type,
  prod_id,
  rsrv_value1,
  rsrv_value2,
  rsrv_value3,
  rsrv_value4,
  rsrv_value5,
  province_code,
  product_btype,
  product_desc,
  product_oldname,
  product_oldexpl,
  opt,
  data_receive_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS data_release_time,
  DATE_FORMAT(LOCALTIMESTAMP, 'yyyyMMdd')
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`view_temp_shqy_td_b_product`;
--入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_shqy_td_b_product`
select
  product_id,
  product_name,
  product_explain,
  product_mode,
  net_type_code,
  brand_code,
  group_brand_code,
  service_id,
  product_obj_type,
  res_type_code,
  declared_product_id,
  comp_tag,
  enable_tag,
  start_absolute_date,
  start_offset,
  start_unit,
  end_enable_tag,
  end_absolute_date,
  end_offset,
  end_unit,
  start_date,
  end_date,
  min_number,
  max_number,
  create_date,
  version,
  product_state,
  update_staff_id,
  update_depart_id,
  update_time,
  prepay_tag,
  need_exp,
  product_app_type,
  prod_id,
  rsrv_value1,
  rsrv_value2,
  rsrv_value3,
  rsrv_value4,
  rsrv_value5,
  province_code,
  product_btype,
  product_desc,
  product_oldname,
  product_oldexpl,
  opt,
  data_receive_time,
  data_release_time
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_shqy_td_b_product_log`
  /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'qyzx_SHQY_TD_B_PRODUCT_2024102501', 'consumer.expiration-time' = '1 d', 'consumer.mode' = 'at-least-once') */
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_template_conf` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_TEMPLATE_CONF',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_template_conf` (
  template_id string,
  name string,
  product_category_classification string,
  composition_type string,
  use_range string,
  amount string,
  settlement_purpose string,
  equity_required string,
  is_conversion_increase string,
  is_polling string,
  receive_type string,
  first_receive string,
  receive_frequency string,
  receive_frequency_unit string,
  receive_amount string,
  receive_limit_amount string,
  receive_number string,
  receive_total_count string,
  exchange_type string,
  exchange_start string,
  exchange_end string,
  exchange_end_unit string,
  status string,
  last_status string,
  create_time string,
  create_staff_id string,
  create_staff_code string,
  update_time string,
  update_staff_id string,
  update_staff_code string,
  province_code string,
  start_time string,
  end_time string,
  enable_tag string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  opt string,
  opttime string,
  cdhtime string,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3) COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (template_id) NOT ENFORCED
) WITH (
  'bucket' = '2',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_template_conf`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) template_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) name,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) product_category_classification,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) composition_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) use_range,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) amount,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) settlement_purpose,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) equity_required,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) is_conversion_increase,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) is_polling,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) receive_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) first_receive,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) receive_frequency,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) receive_frequency_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) receive_amount,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) receive_limit_amount,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) receive_number,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) receive_total_count,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 18)) exchange_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 19)) exchange_start,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 20)) exchange_end,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 21)) exchange_end_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 22)) status,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 23)) last_status,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 24)) create_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 25)) create_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 26)) create_staff_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 27)) update_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 28)) update_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 29)) update_staff_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 30)) province_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 31)) start_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 32)) end_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 33)) enable_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 34)) start_absolute_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 35)) start_offset,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 36)) start_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 37)) end_enable_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 38)) end_absolute_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 39)) end_offset,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 40)) end_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 41)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 42)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 43)) cdhtime,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_template_conf`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_template_conf_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_party_product` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_PARTY_PRODUCT',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_party_product` (
  sp_product_id string,
  sp_product_name string,
  state string,
  sp_id string,
  sp_service_id string,
  is_package string,
  is_group string,
  is_gift string,
  confirm_prompt string,
  success_prompt string,
  cancel_prompt string,
  price_describe string,
  pay_mode_code string,
  need_confm string,
  freeuse_flag string,
  freeuse_time string,
  third_party_flag string,
  billing_mode string,
  billing_value string,
  max_use_sum string,
  max_use_times string,
  max_use_octets string,
  start_date string,
  end_date string,
  update_date string,
  product_type_code string,
  billing_id string,
  discount_id string,
  popularize_start string,
  popularize_stop string,
  discount_des string,
  send_num string,
  order_command string,
  order_cmd_match string,
  order_acct string,
  order_acc_match string,
  cancel_command string,
  cancel_cmd_match string,
  cancel_acc string,
  cancel_acc_match string,
  request_cmd string,
  request_cmd_match string,
  request_acc string,
  request_acc_match string,
  vac_sub string,
  notify_type string,
  product_city string,
  confirm string,
  rsrv_str1 string,
  rsrv_str2 string,
  party_id string,
  update_type string,
  sequence_id string,
  orderchannel string,
  cancelchannel string,
  pop_billing_id string,
  pop_discount_id string,
  is_every_month string,
  oper_type string,
  send_times string,
  tag string,
  productid string,
  spec_productid string,
  product_mode string,
  product_unit string,
  prod_period_grade string,
  prod_service_grade string,
  prod_credit string,
  group_service string,
  service_compose string,
  intro_url string,
  sp_order_url string,
  syn_order_func string,
  sp_psedo_flag string,
  net_tag string,
  classify string,
  duerece string,
  productnet string,
  productsubject string,
  parastring string,
  paravalues string,
  province_code string,
  opt string,
  opttime string,
  cdhtime string,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3) COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (sp_product_id) NOT ENFORCED
) WITH (
  'bucket' = '2',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_party_product`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) sp_product_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) sp_product_name,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) state,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) sp_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) sp_service_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) is_package,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) is_group,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) is_gift,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) confirm_prompt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) success_prompt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) cancel_prompt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) price_describe,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) pay_mode_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) need_confm,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) freeuse_flag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) freeuse_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) third_party_flag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) billing_mode,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 18)) billing_value,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 19)) max_use_sum,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 20)) max_use_times,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 21)) max_use_octets,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 22)) start_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 23)) end_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 24)) update_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 25)) product_type_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 26)) billing_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 27)) discount_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 28)) popularize_start,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 29)) popularize_stop,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 30)) discount_des,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 31)) send_num,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 32)) order_command,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 33)) order_cmd_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 34)) order_acct,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 35)) order_acc_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 36)) cancel_command,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 37)) cancel_cmd_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 38)) cancel_acc,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 39)) cancel_acc_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 40)) request_cmd,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 41)) request_cmd_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 42)) request_acc,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 43)) request_acc_match,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 44)) vac_sub,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 45)) notify_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 46)) product_city,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 47)) confirm,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 48)) rsrv_str1,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 49)) rsrv_str2,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 50)) party_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 51)) update_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 52)) sequence_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 53)) orderchannel,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 54)) cancelchannel,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 55)) pop_billing_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 56)) pop_discount_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 57)) is_every_month,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 58)) oper_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 59)) send_times,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 60)) tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 61)) productid,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 62)) spec_productid,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 63)) product_mode,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 64)) product_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 65)) prod_period_grade,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 66)) prod_service_grade,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 67)) prod_credit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 68)) group_service,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 69)) service_compose,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 70)) intro_url,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 71)) sp_order_url,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 72)) syn_order_func,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 73)) sp_psedo_flag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 74)) net_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 75)) classify,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 76)) duerece,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 77)) productnet,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 78)) productsubject,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 79)) parastring,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 80)) paravalues,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 81)) province_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 82)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 83)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 84)) cdhtime,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_party_product`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_party_product_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_service` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_SERVICE',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_service` (
  service_id string,
  service_name string,
  net_type_code string,
  intf_mode string,
  parent_type_code string,
  service_mode string,
  service_brand_code string,
  service_level string,
  service_state string,
  service_type string,
  start_date string,
  end_date string,
  remark string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  opt string,
  opttime string,
  cdhtime string,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3) COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (service_id) NOT ENFORCED
) WITH (
  'bucket' = '1',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_service`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) service_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) service_name,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) net_type_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) intf_mode,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) parent_type_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) service_mode,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) service_brand_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) service_level,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) service_state,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) service_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) start_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) end_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) remark,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) update_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) update_depart_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) update_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 18)) cdhtime,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_service`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_service_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_discnt` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_DISCNT',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_discnt` (
  discnt_code string,
  discnt_name string,
  discnt_explain string,
  b_discnt_code string,
  a_discnt_code string,
  obj_type_code string,
  enable_tag string,
  start_date string,
  end_date string,
  tag_set string,
  rsrv_str1 string,
  rsrv_str2 string,
  rsrv_str3 string,
  rsrv_str4 string,
  rsrv_str5 string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  start_absolute_date string,
  start_offset string,
  start_unit string,
  end_enable_tag string,
  end_absolute_date string,
  end_offset string,
  end_unit string,
  c_discnt_code string,
  bind_svc_set string,
  end_mode string,
  main_discnt_type string,
  trans_type string,
  disnct_salefee string,
  disnct_costfee string,
  update_staff string,
  staff_prov_code string,
  config_orderno string,
  p_prov_code string,
  opt string,
  opttime string,
  cdhtime string,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3)  COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (discnt_code) NOT ENFORCED
) WITH (
  'bucket' = '2',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_discnt`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) discnt_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) discnt_name,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) discnt_explain,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) b_discnt_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) a_discnt_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) obj_type_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) enable_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) start_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) end_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) tag_set,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) rsrv_str1,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) rsrv_str2,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) rsrv_str3,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) rsrv_str4,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) rsrv_str5,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) update_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) update_depart_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) update_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 18)) start_absolute_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 19)) start_offset,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 20)) start_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 21)) end_enable_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 22)) end_absolute_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 23)) end_offset,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 24)) end_unit,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 25)) c_discnt_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 26)) bind_svc_set,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 27)) end_mode,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 28)) main_discnt_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 29)) trans_type,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 30)) disnct_salefee,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 31)) disnct_costfee,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 32)) update_staff,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 33)) staff_prov_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 34)) config_orderno,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 35)) p_prov_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 36)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 37)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 38)) cdhtime,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_discnt`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_discnt_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_package` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_PACKAGE',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package` (
  package_id string,
  package_name string,
  package_type_code string,
  package_desc string,
  start_date string,
  end_date string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  min_number string,
  max_number string,
  need_exp string,
  component_id string,
  opt string,
  opttime string,
  cdhtime string,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (package_id) NOT ENFORCED
) WITH (
  'bucket' = '2',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_package`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) package_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) package_name,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) package_type_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) package_desc,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) start_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) end_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) update_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) update_depart_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) update_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) min_number,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) max_number,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) need_exp,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) component_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) cdhtime,
  DATA_RECEIVE_TIME event_time,
  DATA_RECEIVE_TIME kafka_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_package`
    /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_package_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
  CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';
--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_product_package` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  headers MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = ' CB_TD_B_PRODUCT_PACKAGE',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表-产品属性表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package` (
  product_id string,
  package_id string,
  force_tag string,
  default_tag string,
  min_number string,
  max_number string,
  rsrv_str1 string,
  rsrv_str2 string,
  start_date string,
  end_date string,
  eparchy_code string,
  update_staff_id string,
  update_depart_id string,
  update_time string,
  item_index string,
  opt string,
  opttime string,
  cdhtime string,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (product_id, package_id, start_date, eparchy_code) NOT ENFORCED
) WITH (
  'bucket' = '2',
  'log.scan.remove-normalize' = 'true',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'consumer.expiration-time' = '72h'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_td_b_product_package`
select
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 0)) product_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 1)) package_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 2)) force_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 3)) default_tag,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 4)) min_number,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 5)) max_number,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 6)) rsrv_str1,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 7)) rsrv_str2,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 8)) start_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 9)) end_date,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 10)) eparchy_code,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 11)) update_staff_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 12)) update_depart_id,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 13)) update_time,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 14)) item_index,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 15)) opt,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 16)) opttime,
  (COP_SPLIT_INDEX(RAW_FIELD, '\u0001', 17)) cdhtime,
  DATA_RECEIVE_TIME EVENT_TIME,
  DATA_RECEIVE_TIME KAFKA_TIME,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_td_b_product_package`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_td_b_product_package_20240925','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;


