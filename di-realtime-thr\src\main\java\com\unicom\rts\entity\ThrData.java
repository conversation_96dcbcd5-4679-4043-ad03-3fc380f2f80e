package com.unicom.rts.entity;

import lombok.Data;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> <PERSON>
 * @create 2022-04-28 09:29
 */

@Data
public class ThrData{

    private String deviceNumber;

    private String smsNoticeId;
    private String eparchyCode;
    private String earlyWarningType;
    private String domesticJacketUsedflow;
    private String domesticJacketResidualflow;
    private String domesticOutsetUsedflow;
    private String dealTime;
    private String saturationThreshold;
    private String intime;
    private String datasource;
    private String cdhTime;
    private String outtime;
    private String userId;
    private String cityCode;

    private String provId;

    private UserTagBean userTagBean;

    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap();

    // @Override
    public String toString(long outTime) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String SEPARATOR = "\u0001";
        // 下发字段
        StringBuilder sb = new StringBuilder();
        sb.append(deviceNumber);
        sb.append(SEPARATOR);sb.append(smsNoticeId);
        sb.append(SEPARATOR);sb.append(eparchyCode);
        sb.append(SEPARATOR);sb.append(earlyWarningType);
        sb.append(SEPARATOR);sb.append(domesticJacketUsedflow);
        sb.append(SEPARATOR);sb.append(domesticJacketResidualflow);
        sb.append(SEPARATOR);sb.append(domesticOutsetUsedflow);
        sb.append(SEPARATOR);sb.append(dealTime);
        sb.append(SEPARATOR);sb.append(saturationThreshold);
        sb.append(SEPARATOR);sb.append(intime);
        sb.append(SEPARATOR);sb.append(datasource);
        sb.append(SEPARATOR);sb.append(simpleDateFormat.format(outTime));
        sb.append(SEPARATOR);sb.append(cdhTime);
        sb.append(SEPARATOR);sb.append(provId);


        return sb.toString();
    }

    Set<String> topics;//本条数据下发的topic

    public void parseHeader(ConsumerRecord<String,String> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, Bytes.toString(next.value()));
            }
        }
        headersMap.put("scene_id", "9");
        headersMap.put("root_time", String.valueOf((record.timestamp())));
        headersMap.put("process_time", String.valueOf(System.currentTimeMillis()));
        if (headers.lastHeader("receive_time") != null && headers.lastHeader("receive_time").value() != null) {
            headersMap.put("receive_time",new String(headers.lastHeader("receive_time").value()));
        }
        if (headers.lastHeader("integration_time") != null && headers.lastHeader("integration_time").value() != null) {
            headersMap.put("integration_time",new String(headers.lastHeader("integration_time").value()) );
        }
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    public Iterable<Header> getHeaders(String provId) {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        headersMap.put("owner_province", provId);
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

}
