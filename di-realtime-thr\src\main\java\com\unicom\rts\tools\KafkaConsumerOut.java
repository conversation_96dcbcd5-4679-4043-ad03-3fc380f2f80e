package com.unicom.rts.tools;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.header.Headers;

import java.util.Arrays;
import java.util.Properties;

/**
 *
 * --topicName hal_tp-1675128391047_final-003ac6428
 * --brokerId ************:9092,************:9092,************:9092,************:9092,*************:9092,*************:9092
 * --userName context --password context --sourceSecurity false
 * --groupId jyt_dept_test
 */
public class KafkaConsumerOut {

    public static void main (String[] args) {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String topicName = conf.get("topicName");        // kafka-topic名称
        String brokerId = conf.get("brokerId");         // broker ip地址

        String userName = conf.get("userName");         // 用户名
        String password = conf.get("password");         // 密码
        String groupId = conf.get("groupId");          // group id

        boolean sourceSecurity = Boolean.parseBoolean(conf.get("sourceSecurity"));

        Properties props = new Properties();
        props.put("auto.offset.reset", "earliest");   //读取旧的数据   latest, earliest, none
        props.put("bootstrap.servers", brokerId); // 设置连接kafka集群的seed broker，不需要传入所有的broker
        props.put("group.id", groupId); // 设置group id
        props.put("enable.auto.commit", "true"); // 打开自动提交offset
        props.put("auto.commit.interval.ms", "1000"); // 设置自动提交offset的时间间隔，示例为每1秒提交一次
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer"); // key的反序列化类
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer"); // value的反序列化类
        //=======================添加权限 配置=======================

        if (sourceSecurity) {
            props.put("sasl.jaas.config",
                    "org.apache.kafka.common.security.scram.ScramLoginModule required username=" + "\"" + userName + "\"" + " password=" + "\"" + password + "\"" + ";");
            props.put("security.protocol", "SASL_PLAINTEXT");
            props.put("sasl.mechanism", "SCRAM-SHA-256");
        }
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props); // 创建消费者客户端

        consumer.subscribe(Arrays.asList(topicName));  // 指定offset消费

        while (true) {
            ConsumerRecords<String, String> records = consumer.poll(100); // 拉取数据，等待时长为100毫秒
            for (ConsumerRecord<String, String> record : records) {
                System.out.println("topic:"+record.topic());
                Headers headers = record.headers();
                record.headers().forEach(e->{
                    System.out.println("header:"+e.key()+"="+ new String(e.value()));
                });
                //String s = String.valueOf(11);
            }

        }
    }


}
