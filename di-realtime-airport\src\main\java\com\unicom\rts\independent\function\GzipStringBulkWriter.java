package com.unicom.rts.independent.function;

import org.apache.flink.api.common.serialization.BulkWriter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPOutputStream;

public class GzipStringBulkWriter implements BulkWriter<String> {

	private GZIPOutputStream gzipOutputStream;

	public GzipStringBulkWriter(GZIPOutputStream gzipOutputStream){
		this.gzipOutputStream = gzipOutputStream;
	}

	@Override
	public void addElement(String element) throws IOException {
		this.gzipOutputStream.write(element.getBytes(StandardCharsets.UTF_8));
		this.gzipOutputStream.write("\n".getBytes(StandardCharsets.UTF_8));
	}

	@Override
	public void finish() throws IOException {
		this.flush();
		this.gzipOutputStream.close();
	}
	
	@Override
	public void flush() throws IOException {
		this.gzipOutputStream.flush();
	}
}