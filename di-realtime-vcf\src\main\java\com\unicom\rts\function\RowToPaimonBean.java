package com.unicom.rts.function;

import com.unicom.rts.entity.PaimonBean;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RowToPaimonBean implements FlatMapFunction<Row, PaimonBean> {
    private final static Logger logger = LoggerFactory.getLogger(RowToPaimonBean.class);

    @Override
    public void flatMap(Row row, Collector<PaimonBean> out) throws Exception {
        try {
            PaimonBean paimon = new PaimonBean();
            paimon.setDtsCollectTime(String.valueOf(row.getField("dts_collect_time")));
            paimon.setDtsKafTime(String.valueOf(row.getField("dts_kaf_time")));
            paimon.setDtsKafOffset(String.valueOf(row.getField("dts_kaf_offset")));
            paimon.setDtsKafPart(String.valueOf(row.getField("dts_kaf_part")));
            paimon.setOperateType(String.valueOf(row.getField("operate_type")));
            paimon.setOperateTime(String.valueOf(row.getField("operate_time")));
            paimon.setOperateTypeDesc(String.valueOf(row.getField("operate_type_desc")));
            paimon.setId(String.valueOf(row.getField("Id")));
            paimon.setPhoneNum(String.valueOf(row.getField("phone_num")));
            paimon.setIsDelete(String.valueOf(row.getField("is_delete")));
            paimon.setCreateUser(String.valueOf(row.getField("create_user")));
            paimon.setCreateName(String.valueOf(row.getField("create_name")));
            paimon.setCreateTime(String.valueOf(row.getField("create_time")));
            paimon.setPaimonTime(String.valueOf(row.getField("PAIMON_TIME")));

            out.collect(paimon);
        } catch (Exception e) {
            logger.error("RowToNpBean data:{} Exception:{}", row, e);
        }
    }

}
