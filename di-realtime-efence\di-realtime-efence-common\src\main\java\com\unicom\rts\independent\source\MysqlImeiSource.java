package com.unicom.rts.independent.source;

import com.unicom.rts.independent.beans.mysqlparam.MysqlImeiBean;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import static com.unicom.rts.independent.utils.MysqlUtil.getConnection;

public class MysqlImeiSource extends RichSourceFunction<List<MysqlImeiBean>> {

    private final static Logger logger = LoggerFactory.getLogger(MysqlImeiSource.class);
    private PreparedStatement ps;
    private Connection connection;
    private volatile boolean isRunning = true;
    private ParameterTool mysqlConf;
    private ParameterTool appConf;


    public MysqlImeiSource(ParameterTool appConf, ParameterTool mysqlConf) {
        this.appConf = appConf;
        this.mysqlConf = mysqlConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        String url = mysqlConf.get("jdbcUrl");
        String user = mysqlConf.get("mysql.user");
        String password = mysqlConf.get("mysql.password");
        String provId = appConf.get("prov_id");
        String sql = "select * from xinan_imei_info where is_valid = '1' and param_prov_id=" + provId + ";";
        logger.info("sql :{}", sql);

        connection = getConnection(url, user, password);
        if (connection != null) {
            ps = this.connection.prepareStatement(sql);
        }
    }

    @Override
    public void run(SourceContext<List<MysqlImeiBean>> ctx) throws InterruptedException {
        long checkInterval = appConf.getLong("mysql.source.interval");
        List<MysqlImeiBean> resultList = new ArrayList<>();
        while (isRunning) {
            try(ResultSet resultSet = ps.executeQuery()) {
                if (resultSet != null) {
                    while (resultSet.next()) {
                        String imei = resultSet.getString("imei");
                        String paramProvId = resultSet.getString("param_prov_id");
                        MysqlImeiBean mysqlImeiBean = new MysqlImeiBean(imei, paramProvId);
                        resultList.add(mysqlImeiBean);
                    }
                }
                ctx.collect(resultList);
                resultList.clear();
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
            Thread.sleep(checkInterval);
        }
    }

    @Override
    public void cancel() {
        try {
            if (ps != null) {
                ps.close();
            }
            if (connection != null) {
                connection.close();
            }
        } catch (Exception e) {
            logger.error(e.toString());
        }
        isRunning = false;
    }
}
