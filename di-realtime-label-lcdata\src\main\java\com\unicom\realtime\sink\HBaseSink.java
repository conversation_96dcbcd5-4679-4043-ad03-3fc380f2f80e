package com.unicom.realtime.sink;

import com.alibaba.fastjson.JSON;
import com.unicom.realtime.entity.ZjData;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HBaseSink extends RichSinkFunction<ZjData> {

    private final static Logger logger = LoggerFactory.getLogger(HBaseSink.class);

    private ParameterTool conf;
    private Connection hbaseConnection;
    private HTable table = null;
    private List<Put> putList;
    //private List<String> numberList;

    public HBaseSink(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) {
        hbaseConnection = getHBaseConnection();
        putList = new ArrayList<>();
        //numberList = new ArrayList<>();

        try {
            table = (HTable) hbaseConnection.getTable(TableName.valueOf(conf.get("home.hbase.table")));
        } catch (IOException e) {
            logger.error("hbaseConnection.getTable err", e);
        }
    }

    @Override
    public void close() throws Exception {
        if (table != null) {
            table.close();
            hbaseConnection.close();
        }
    }

    @Override
    public void invoke(ZjData value, Context context) {
        try {
            // 新增序列号校验
            if (StringUtils.isBlank(value.getSerialNumber()) || value.getSerialNumber().length() < 2) {
                logger.warn("非法手机号: {}", value.getSerialNumber());
                return; // 跳过无效数据
            }

            String reversedSerial = new StringBuilder(value.getSerialNumber()).reverse().toString();
            byte[] key = Bytes.toBytes(reversedSerial);

            // 验证RowKey长度
            if (key.length == 0) {
                logger.error("生成空RowKey，原始序列号: {}", value.getSerialNumber());
                return;
            }

            Put put = new Put(key);

            // 记录是否添加了至少一个列
            boolean hasColumns = false;

            if (value.hasLevel()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000000"), Bytes.toBytes(value.getLevel()));
                hasColumns = true;
            }
            if (value.hasInterceptCount()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00001"), Bytes.toBytes(value.getInterceptCount()));
                hasColumns = true;
            }
            if (value.hasPickUpCount()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00003"), Bytes.toBytes(value.getPickUpCount()));
                hasColumns = true;
            }
            if (value.hasOrderStatus()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00005"), Bytes.toBytes(value.getOrderStatus()));
                hasColumns = true;
            }
            if (value.hasExpireRemainingDays()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00004"), Bytes.toBytes(value.getExpireRemainingDays()));
                hasColumns = true;
            }
            if (value.hasCanBindMemberCount()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00000"), Bytes.toBytes(value.getCanBindMemberCount()));
                hasColumns = true;
            }
            if (value.hasAlreadyBindMemberCount()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00002"), Bytes.toBytes(value.getAlreadyBindMemberCount()));
                hasColumns = true;
            }
            if (value.hasBindDevice()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SCP00006"), Bytes.toBytes(value.getBindDevice()));
                hasColumns = true;
            }
            if (value.hasProductId()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000004"), Bytes.toBytes(value.getProductId()));
                hasColumns = true;
            }
            if (value.hasInnetDate()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000005"), Bytes.toBytes(value.getInnetDate()));
                hasColumns = true;
            }
            if (value.hasLackBalanceTime()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000007"), Bytes.toBytes(value.getLackBalanceTime()));
                hasColumns = true;
            }
            if (value.hasTrafficCapsTime()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000006"), Bytes.toBytes(value.getTrafficCapsTime()));
                hasColumns = true;
            }
            if (value.hasCityCode()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000003"), Bytes.toBytes(value.getCityCode()));
                hasColumns = true;
            }
            if (value.hasProvinceCode()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000002"), Bytes.toBytes(value.getProvinceCode()));
                hasColumns = true;
            }
            if (value.hasLabelStLoginTimeReal()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000001"), Bytes.toBytes(value.getLabelStLoginTimeReal()));
                hasColumns = true;
            }
            if (value.hasBROADBAND_NOTINCE_TYPE()) {
                // 直接写入，空字符串会正常存储
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SJT00000"), Bytes.toBytes(value.getBROADBAND_NOTINCE_TYPE()));
                hasColumns = true;
            }
            if (value.hasBROADBAND_NOTINCE_TIME()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SJT00001"), Bytes.toBytes(value.getBROADBAND_NOTINCE_TIME()));
                hasColumns = true;
            }
            if (value.hasAct_ids()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000011"), Bytes.toBytes(value.getAct_ids()));
                hasColumns = true;
            }
            if (value.hasResumptionStatus()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000012"), Bytes.toBytes(value.getResumptionStatus()));
                hasColumns = true;
            }
            if (value.hasTELESCORE()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000013"), Bytes.toBytes(value.getTELESCORE()));
                hasColumns = true;
            }
            if (value.hasAWARDSCORE()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000014"), Bytes.toBytes(value.getAWARDSCORE()));
                hasColumns = true;
            }
            if (value.hasTOTELSCORE()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000015"), Bytes.toBytes(value.getTOTELSCORE()));
                hasColumns = true;
            }

            if (value.hasIS_ARREARAGE()) {
                put.addColumn(Bytes.toBytes("com1"), Bytes.toBytes("SK000016"), Bytes.toBytes(value.getIS_ARREARAGE()));
                hasColumns = true;
            }

            // 仅当有有效列时才加入列表
            if (hasColumns) {
                putList.add(put);
                // logger.info("当前putList数量为:{}", putList.size());

                if (putList.size() == 50) {
                    table.put(putList);
                    putList.clear();
                    //         logger.info("执行批量插入hbase完成,数量为:{}", putList.size());
                }
            } else {
                logger.warn("检测到空数据记录，序列号: {}", value.getSerialNumber());
            }
        } catch (Exception e) {
            logger.error("数据处理异常", e);
        }
    }

    private Connection getHBaseConnection() {
        org.apache.hadoop.conf.Configuration hbaseConfig2 = HBaseConfiguration.create();
        hbaseConfig2.set("hbase.zookeeper.property.clientPort", conf.get("home.hbase.zk.port"));
        hbaseConfig2.set("hbase.zookeeper.quorum", conf.get("home.hbase.zk.addr"));
        hbaseConfig2.set("zookeeper.znode.parent", "/hbase-unsecure");
        //hbaseConfig2.set("zookeeper.znode.parent", "/hbase");
        try {
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(conf.get("home.hbase.user"));
            hbaseConnection = ConnectionFactory.createConnection(hbaseConfig2, User.create(userGroupInformation));
            System.out.println("成功连接Hbase {}" + hbaseConfig2.toString());
        } catch (IOException e) {
            System.out.println("获取Hbase连接异常," + e.getClass() + ":" + e.getMessage());
        }
        return hbaseConnection;
    }

    // private void addColumnIfPresent(Put put, String cf, String qualifier, String value) {
    //     if (StringUtils.isNotBlank(value)) {
    //         put.addColumn(Bytes.toBytes(cf), Bytes.toBytes(qualifier), Bytes.toBytes(value));
    //     }
    // }

}
