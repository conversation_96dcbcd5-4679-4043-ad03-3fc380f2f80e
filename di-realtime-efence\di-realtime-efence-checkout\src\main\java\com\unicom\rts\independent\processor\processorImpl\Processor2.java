package com.unicom.rts.independent.processor.processorImpl;

import com.unicom.rts.independent.enums.TagEnum;
import com.unicom.rts.independent.processor.AbstractProcessor;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import static com.unicom.rts.independent.utils.FilterUtil.getTopicByGroovy;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/

/**
 * 过滤条件，groovy（groovy中仅过滤），过滤产品 90403940
 * 输出 record + "\1" + productName
 * 云南漫游、广东茂名
 */
public class Processor2 extends AbstractProcessor {

    @Override
    public void subProcess() throws Exception {

        String productCode = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTCODE.getCode());
        recordBean.setProductCode(productCode);
        if (null != recordBean && null != ruleBean.getParam()) {
            String sinkTopics = (String) getTopicByGroovy(recordBean, ruleBean.getParam());
            if (!StringUtils.isEmpty(sinkTopics)) {
                String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                        recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTNAME.getCode());

                //保存state并下发数据
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                checkoutState.put(recordBean.getDeviceNum(), sdf.format(new Date()));
                for(String sinkTopic : Arrays.asList(sinkTopics.split(","))) {
                    out.collect(new Tuple2<>(sinkTopic, recordBean.getRecord() + "\1" + productName));
                }
            }
        }
    }
}
