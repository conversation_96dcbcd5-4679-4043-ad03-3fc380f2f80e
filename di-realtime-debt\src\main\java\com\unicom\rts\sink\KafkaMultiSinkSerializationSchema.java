package com.unicom.rts.sink;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.flink.streaming.connectors.kafka.partitioner.FlinkKafkaPartitioner;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;



public class KafkaMultiSinkSerializationSchema implements KafkaSerializationSchema<Tuple3<String, String, Iterable<Header>>> {

    @Nullable
    private final FlinkKafkaPartitioner<Tuple3<String, String, Iterable<Header>>> partitioner;
    @Nullable
    private final SerializationSchema<Tuple3<String, String, Iterable<Header>>> keySerialization;
    @Nullable
    private final SerializationSchema<Tuple3<String, String, Iterable<Header>>> valueSerialization;

    public KafkaMultiSinkSerializationSchema(
            @Nullable FlinkKafkaPartitioner<Tuple3<String, String, Iterable<Header>>> partitioner,
            @Nullable SerializationSchema<Tuple3<String, String, Iterable<Header>>> keySerialization,
            @Nullable SerializationSchema<Tuple3<String, String, Iterable<Header>>> valueSerialization) {
        this.partitioner = partitioner;
        this.keySerialization = keySerialization;
        this.valueSerialization = valueSerialization;
    }

    public KafkaMultiSinkSerializationSchema() {
        this(null, null, null);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(Tuple3<String, String, Iterable<Header>> element, @Nullable Long aLong) {

        final byte[] keySerialized;
        if (keySerialization == null) {
            keySerialized = null;
        } else {
            keySerialized = keySerialization.serialize(element);
        }

        final byte[] valueSerialized;
        if (valueSerialization == null) {
            valueSerialized = element.f1.getBytes(StandardCharsets.UTF_8);
        } else {
            valueSerialized = valueSerialization.serialize(element);
        }
        //String topic, Integer partition, K key, V value, Iterable<Header> headers
        return new ProducerRecord<byte[], byte[]>(element.f0,null,keySerialized,valueSerialized,element.f2);

    }

}
