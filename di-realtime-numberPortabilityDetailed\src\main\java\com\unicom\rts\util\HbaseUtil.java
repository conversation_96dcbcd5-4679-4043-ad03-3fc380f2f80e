package com.unicom.rts.util;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

public class HbaseUtil {

    private final static Logger logger = LoggerFactory.getLogger(HbaseUtil.class);

    private static Map<String, HTable> tablePool = new HashMap<>();

    private  static String hbaseEncode = "utf8";// 编码格式

    public static Result getKeyValue(Connection connection, String hbaseTableName, String rowKey,String columnfamily,String[] column) {

        try {
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            byte[] digest = md.digest(rowKey.getBytes());
//            String hexString = bytesToHex(digest);
            Get get = new Get(rowKey.getBytes(hbaseEncode));
            logger.info("------rowKey-----"+rowKey);

            if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isEmpty(String.valueOf(column))) {
                get.addFamily(columnfamily.getBytes(hbaseEncode));
            }

            if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isNotEmpty(String.valueOf(column))) {
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[0].getBytes(hbaseEncode));
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[1].getBytes(hbaseEncode));
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[2].getBytes(hbaseEncode));
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[3].getBytes(hbaseEncode));
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[4].getBytes(hbaseEncode));
                get.addColumn(columnfamily.getBytes(hbaseEncode), column[5].getBytes(hbaseEncode));
            }
            tablePool.put(hbaseTableName, (HTable) connection.getTable(TableName.valueOf(hbaseTableName)));
            Result result = tablePool.get(hbaseTableName).get(get);
            logger.info("------result.isEmpty()-----"+result.isEmpty());
            return  result;

        } catch (IOException e) {
            logger.error( " 获取 Table : 数据失败 ");
            logger.error(e.getMessage());
            return null;
        }
    }
    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
