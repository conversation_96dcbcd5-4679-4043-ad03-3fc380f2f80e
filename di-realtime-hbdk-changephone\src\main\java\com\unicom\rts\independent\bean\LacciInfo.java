package com.unicom.rts.independent.bean;

import lombok.Data;

@Data
public class LacciInfo {
    String lacCi;
    String lon;
    String lat;
    String area;
    String city;
    String operation;
    // LacciInfo berore;
    // LacciInfo after;

    public LacciInfo(String lacCi, String lon, String lat, String area, String city) {
        this.lacCi = lacCi;
        this.lon = lon;
        this.lat = lat;
        this.area = area;
        this.city = city;
    }

    public LacciInfo(String lacCi, String lon, String lat, String area, String city, String operation) {
        this.lacCi = lacCi;
        this.lon = lon;
        this.lat = lat;
        this.area = area;
        this.city = city;
        this.operation = operation;
    }

    public LacciInfo() {

    }

    // public LacciCDCInfo(LacciInfo berore) {
    //     this.lacCi = berore.lacCi;
    //     this.lon = berore.lon;
    //     this.lat = berore.lat;
    //     this.area = berore.area;
    //     this.city = berore.city;
    // }

    @Override
    public String toString() {
        return "LacCiInfo{" +
                "lacCi='" + lacCi + '\'' +
                ", lon='" + lon + '\'' +
                ", lat='" + lat + '\'' +
                ", area='" + area + '\'' +
                ", city='" + city + '\'' +
                ", operation='" + operation + '\'' +
                // ", tradeId=" + tradeId +
                '}';
    }
}
