package com.unicom.realtime.bean;

import com.unicom.realtime.process.SM4Util;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.types.RowKind;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnionBean {
    private String userId;
    private String custId;
    private String serialNumber;
    private String productId;
    private String psptId;
    private String psptTypeCode;
    private String departId;
    private String age;
    private String tradeTypeCode;
    private String subscribeState;
    private String nextDealTag;
    private String netTypeCode;
    private String finishDate;
    private long offset;
    private String topicAndPart;
    private String opt;
    private RowKind rowKind;
    private String tableName;

    private Map<String, String> headersMap = new HashMap();
    private final static String KEY_STR = "94838df1edee07c50687765ed1cb38d4";

    public void parseHeader(ConsumerRecord<byte[], byte[]> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, new String(next.value()));
            }
        }
        headersMap.put("root_time", Long.toString(record.timestamp()));
        headersMap.put("process_time", Long.toString(System.currentTimeMillis()));
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", Long.toString(System.currentTimeMillis()));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    public String toStringSm4() {
        String s = "\001";
        StringBuilder sb = new StringBuilder();
        sb.append(serialNumber);
        sb.append(s);
        sb.append(userId);
        sb.append(s);
        sb.append(custId);
        sb.append(s);
        sb.append(psptTypeCode);
        sb.append(s);
        sb.append(psptId);
        sb.append(s);
        sb.append(productId);
        sb.append(s);
        sb.append(departId);
        sb.append(s);
        sb.append(age);
        String content = sb.toString();
//        SecretKey key = SecureUtil.generateKey("SM4");
//        System.out.println("sm4 key ==========>>>>>>>>>>>>> " + HexUtil.encodeHexStr(key.getEncoded()));

        String encryptData = SM4Util.encrypt(KEY_STR, content);
        return encryptData;
    }

    @Override
    public String toString() {
        String s = "\001";
        StringBuilder sb = new StringBuilder();
        sb.append(serialNumber);
        sb.append(s);
        sb.append(userId);
        sb.append(s);
        sb.append(custId);
        sb.append(s);
        sb.append(psptTypeCode);
        sb.append(s);
        sb.append(psptId);
        sb.append(s);
        sb.append(productId);
        sb.append(s);
        sb.append(departId);
        sb.append(s);
        sb.append(age);
        return sb.toString();
    }
}
