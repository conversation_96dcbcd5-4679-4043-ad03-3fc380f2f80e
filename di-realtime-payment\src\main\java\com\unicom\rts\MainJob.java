package com.unicom.rts;

import com.unicom.rts.bean.Pay;
import com.unicom.rts.bean.PayInBean;
import com.unicom.rts.common.KafkaStream;
import com.unicom.rts.function.*;
import com.unicom.rts.sink.KafkaProducer;
import com.unicom.rts.source.Sql;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.kafka.common.header.Header;
import java.util.concurrent.TimeUnit;

public class MainJob{
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String payTopic = conf.get("pay.topic");
        ParameterTool payKafkaConf = KafkaStream.mergeConf(conf, "pay.");
        // 创建流式的执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);// 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        // checkpoint
        // 开启Checkpoint，间隔为 3 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 4分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(6));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(5, Time.of(10, TimeUnit.SECONDS)));
        // 消费pay kafka数据
        DataStream<Pay> payStream = env.addSource(KafkaStream.getRootConsumer(payKafkaConf, payTopic)).uid("getRootConsumer").name("getRootConsumer")
                .flatMap(new PayClean()).uid("PayClean").name("PayClean")
                .flatMap(new Distinct(conf)).uid("Distinct").name("Distinct").filter(new FilterFunction<Pay>() {
                    @Override
                    public boolean filter(Pay pay) throws Exception {
                        if(
                                ("16000".equals(pay.getPayment_op()))
                                        & ("0".equals(pay.getPay_fee_mode_code()) || "1".equals(pay.getPay_fee_mode_code()) || "5".equals(pay.getPay_fee_mode_code()))
                                        & (
                                        "15463".equals(pay.getChannel_id()) || "15103".equals(pay.getChannel_id())
                                                || "15467".equals(pay.getChannel_id()) || "15104".equals(pay.getChannel_id())
                                                || "15101".equals(pay.getChannel_id()) || "15103".equals(pay.getChannel_id()) || "15461".equals(pay.getChannel_id())
                                                || "15106".equals(pay.getChannel_id()) || "15009".equals(pay.getChannel_id()) || "15000".equals(pay.getChannel_id())
                                                || "15013".equals(pay.getChannel_id()) || "15102".equals(pay.getChannel_id())
                                                || "15107".equals(pay.getChannel_id()) || "15061".equals(pay.getChannel_id())
                                                || "15205".equals(pay.getChannel_id()) || "15004".equals(pay.getChannel_id())
                                                || "15304".equals(pay.getChannel_id()) || "15101".equals(pay.getChannel_id())
                                                || "15631".equals(pay.getChannel_id()) || "15601".equals(pay.getChannel_id()) || "15505".equals(pay.getChannel_id())
                                )

                                        &("0".equals(pay.getCancel_tag()))
                        ) {
                            return true;
                        }else {
                            return false;
                        }
                    }
                });
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        tabEnv.executeSql(Sql.CREATE_PAYMENT_DATA_INDEPENDENT);
        Table paymentDataTable= tabEnv.sqlQuery(Sql.SELECT_PAYMENT_DATA_INDEPENDENT + options);
        DataStream<PayInBean> paymentPaimonStream = tabEnv.toChangelogStream(paymentDataTable).flatMap(new RowToPayBean()).uid("RowToPayBean").name("RowToPayBean").filter(
            new FilterFunction<PayInBean>() {
                @Override
                public boolean filter(PayInBean data) throws Exception {
                    //0新增，1过滤
                    if ("0".equals(data.getIsDelete())) {
                        return true;
                    }else {
                        return false;
                        }
                    }
                }
        ).keyBy(PayInBean::getPhoneNum);

        DataStream<Tuple3<String, String, Iterable<Header>>> outStream = payStream.keyBy(Pay::getSerial_number)
                .connect(paymentPaimonStream).process(new PayPaimonProcessCo(conf)).uid("PayPaimonProcessCo").name("PayPaimonProcessCo");
        FlinkKafkaProducer<Tuple3<String , String, Iterable<Header>>> flinkKafkaProducer = KafkaProducer.getFlinkKafkaProducer(conf);
        // sink
        outStream.addSink(flinkKafkaProducer).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("kafkaSink");;
        // execute program
        env.execute("dp-independent-payment");
    }
}
