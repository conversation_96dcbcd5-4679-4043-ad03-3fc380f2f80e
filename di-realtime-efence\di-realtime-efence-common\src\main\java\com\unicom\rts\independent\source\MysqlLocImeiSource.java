package com.unicom.rts.independent.source;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import static com.unicom.rts.independent.utils.MysqlUtil.getConnection;

    public class MysqlLocImeiSource extends RichSourceFunction<List<Tuple2<String,String>>> {


    private final static Logger logger = LoggerFactory.getLogger(MysqlLocImeiSource.class);
    private PreparedStatement locPs;
    private PreparedStatement imeiPs;
    private Connection connection;
    private ResultSet locResultSet;
    private ResultSet imeiResultSet;
    private volatile boolean isRunning = true;
    private ParameterTool mysqlConf;
    private ParameterTool appConf;


    public MysqlLocImeiSource(ParameterTool appConf, ParameterTool mysqlConf) {
        this.appConf = appConf;
        this.mysqlConf = mysqlConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        String url = mysqlConf.get("jdbcUrl");
        String user = mysqlConf.get("mysql.user");
        String password = mysqlConf.get("mysql.password");
        String locSql = "select concat_ws('|',lacci,prov_id) as loc,module_name from xinan_location_info where is_valid = '1';";
        String imeiSql = "select imei,module_name from xinan_imei_info where is_valid = '1';";
        logger.info("sql :{}", locSql);
        logger.info("sql :{}", imeiSql);
        connection = getConnection(url, user, password);
        if (connection != null) {
            locPs = this.connection.prepareStatement(locSql);
            imeiPs = this.connection.prepareStatement(imeiSql);
        }
    }

    @Override
    public void run(SourceContext<List<Tuple2<String,String>>> ctx) throws Exception {
        long checkInterval = appConf.getLong("mysql.source.interval"); //检查数据库时间间隔
        List<Tuple2<String,String>> resultList = new ArrayList<>();
        while (isRunning) {
            try {
                //处理lacci
                locResultSet = locPs.executeQuery();
                if (locResultSet != null) {
                    while (locResultSet.next()) {
                        String loc = locResultSet.getString("loc");
                        String moduleName = locResultSet.getString("module_name");
                        Tuple2<String,String> locTuple = new Tuple2<>(loc, moduleName);
                        resultList.add(locTuple);
                    }
                }
                //处理imei
                imeiResultSet = imeiPs.executeQuery();
                if (imeiResultSet != null) {
                    while (imeiResultSet.next()) {
                        String imei = imeiResultSet.getString("imei");
                        String moduleName = imeiResultSet.getString("module_name");
                        Tuple2<String,String> imeiTuple = new Tuple2<>(imei, moduleName);
                        resultList.add(imeiTuple);
                    }
                }
                ctx.collect(resultList);
                resultList.clear();
            } catch (Exception e) {
                logger.error(e.getMessage());
            } finally {
                locResultSet.close();
                imeiResultSet.close();
            }
            Thread.sleep(checkInterval);
        }
    }

    @Override
    public void cancel() {
        try {
            if (locPs != null) {
                locPs.close();
            }
            if (imeiPs != null) {
                imeiPs.close();
            }
            if (connection != null) {
                connection.close();
            }
        } catch (Exception e) {
            logger.error(e.toString());
        }
        isRunning = false;
    }
}
