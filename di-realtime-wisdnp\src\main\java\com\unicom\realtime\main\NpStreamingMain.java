package com.unicom.realtime.main;


import com.google.common.collect.Lists;
import com.unicom.realtime.bean.WisdNpData;
import com.unicom.realtime.function.WisdNpMap;
import com.unicom.realtime.sink.XCloudDruidSink;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.AllWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.unicom.realtime.source.KafkaSource.getFlinkKafkaConsumer;

/**
 * <AUTHOR>
 * 任务描述：
 *      为经分系统提供用户携转实时明细，包括携入联通和从联通携出,将数据入库行云。
 *      数据来源：采集整合
 *      业务场景描述：为经分系统提供用户携转实时明细，包括携入联通和从联通携出。
 *      触发条件：用户携入联通或从联通携出
 *      并发量：无并发量限制
 *      触发到下发时间：秒级
 *
 */
public class NpStreamingMain {
    public static void main(String[] args) throws Exception {
        //1.获取参数
        // 使用flink 内置函数，从命令行读取参数
        ParameterTool conf = ParameterTool.fromArgs(args);
        // 读取配置文件路径，并且从properties文件读取参数
       /* String configPath = parameters.get("config_path");
        ParameterTool conf = ParameterTool.fromPropertiesFile(configPath);*/

        //2.设置flink执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(6));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数1000次,重启间隔10秒
//        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(1000, Time.of(10, TimeUnit.SECONDS)));

        String broker_sever = conf.get("source.bootstrap");
        String topic = conf.get("source.topic");

        // 获取kafka消费者
        FlinkKafkaConsumer provLoc = getFlinkKafkaConsumer(broker_sever, topic, conf);

        /*SingleOutputStreamOperator<WisdNpData> dataStream =
                env.addSource(provLoc).setParallelism(Integer.parseInt(conf.get("source.parallelism"))).name(topic)
                        .map(new WisdNpMap()).name("create WisdNpData").disableChaining().setParallelism(Integer.parseInt(conf.get("map.parallelism")));*/


        DataStreamSource<String> kafkaSource = env.addSource(provLoc).setParallelism(Integer.parseInt(conf.get("source.parallelism")));
        SingleOutputStreamOperator<List<WisdNpData>> streamOperator =
                        kafkaSource.name(topic)
                        .map(new WisdNpMap()).name("create WisdNpData").disableChaining().setParallelism(Integer.parseInt(conf.get("map.parallelism")))
                        .windowAll(TumblingProcessingTimeWindows.of(Time.seconds(Integer.parseInt(conf.get("window.time")))))
                        .apply(
                                new AllWindowFunction<WisdNpData, List<WisdNpData>, TimeWindow>() {
                                    @Override
                                    public void apply(TimeWindow timeWindow, Iterable<WisdNpData> values, Collector<List<WisdNpData>> out) throws Exception {
                                            ArrayList<WisdNpData> wisdNpDatas = Lists.newArrayList(values);
                                            if(wisdNpDatas.size()>0){
                                                out.collect(wisdNpDatas);
                                            }
                                    }
                                }
                        );
//        streamOperator.print();
//        streamOperator.addSink(new XCloudSink(conf)).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("xcloud sink");
        streamOperator.addSink(new XCloudDruidSink(conf)).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("xcloud sink");
        env.execute("wisdNpCompute");
    }
}
