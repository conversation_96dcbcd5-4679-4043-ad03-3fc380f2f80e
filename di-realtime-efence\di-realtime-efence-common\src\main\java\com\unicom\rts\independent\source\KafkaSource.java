package com.unicom.rts.independent.source;

import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;

import java.util.Arrays;
import java.util.Properties;

public class KafkaSource {

    public static FlinkKafkaConsumer<String> getKafkaConsumer(ParameterTool conf) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", conf.get("source.bootstrap"));
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(
                conf.get("source.topic"), new SimpleStringSchema(), properties);
        consumer.setCommitOffsetsOnCheckpoints(true);
        return consumer;
    }


    public static FlinkKafkaConsumer<String> getKafkaConsumerForTopicList(ParameterTool conf) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", conf.get("source.bootstrap"));
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(
                Arrays.asList(conf.get("source.topic").split(",")), new SimpleStringSchema(), properties);
        consumer.setCommitOffsetsOnCheckpoints(true);
        return consumer;
    }




}