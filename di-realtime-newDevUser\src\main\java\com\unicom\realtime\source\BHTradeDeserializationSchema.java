package com.unicom.realtime.source;

import com.unicom.realtime.bean.UnionBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


public class BHTradeDeserializationSchema implements KafkaRecordDeserializationSchema<UnionBean> {
    private final static Logger log = LoggerFactory.getLogger(BHTradeDeserializationSchema.class);

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<UnionBean> out) throws IOException {
        try {
            UnionBean unionBean = new UnionBean();
            unionBean.setOffset(record.offset());
            unionBean.setTopicAndPart(StringUtils.join(record.topic(), record.partition()));
            parse(unionBean, record);
            //增加头
            unionBean.parseHeader(record);

            out.collect(unionBean);
        } catch (Exception e) {
            log.error("deserialize error {}", e);
        }
    }

    @Override
    public TypeInformation<UnionBean> getProducedType() {
        return TypeInformation.of(new TypeHint<UnionBean>() {
        });
    }

    public void parse(UnionBean unionBean, ConsumerRecord<byte[], byte[]> record) {
        String str = new String(record.value(), StandardCharsets.UTF_8);
        String[] columns = StringUtils.splitPreserveAllTokens(str, "\001");
        unionBean.setTradeTypeCode(columns[3]);
        unionBean.setSubscribeState(columns[7]);
        unionBean.setNextDealTag(columns[8]);
        unionBean.setProductId(columns[9]);
        unionBean.setUserId(columns[11]);
        unionBean.setCustId(columns[12]);
        unionBean.setNetTypeCode(columns[16]);
        unionBean.setSerialNumber(columns[17]);
        unionBean.setFinishDate(columns[30]);
        unionBean.setOpt(columns[85]);
        unionBean.setTableName("TF_F_BH_TRADE");
    }
}
