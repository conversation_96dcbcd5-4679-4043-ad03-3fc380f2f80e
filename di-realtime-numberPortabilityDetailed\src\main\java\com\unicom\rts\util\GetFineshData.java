package com.unicom.rts.util;

import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;

/**
 * <AUTHOR>
 * @date 2021-04-19 10:09
 */
public class GetFineshData {

    public static String finshDateFromHtable(Cell[] cells){

        String finsh_date = "";
        for (Cell cell : cells) {
            String cloumn = new String(CellUtil.cloneQualifier(cell));
            if (cloumn.equalsIgnoreCase("q25")) {
                String hbaseDate = new String(CellUtil.cloneValue(cell));
                if (!"".equals(hbaseDate) && hbaseDate != null){
                    finsh_date = hbaseDate;
                }else {
                    finsh_date = "";
                }
            }
        }
        return finsh_date;
    }

}
