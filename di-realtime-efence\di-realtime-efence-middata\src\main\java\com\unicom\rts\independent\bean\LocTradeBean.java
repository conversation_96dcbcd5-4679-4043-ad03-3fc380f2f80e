package com.unicom.rts.independent.bean;

import lombok.Data;

@Data
public class LocTradeBean {
    private String 	deviceNumber;	//手机号
    private String 	time;	//信令发生时间
    private String 	roamType;	//漫游类型
    private String 	hrovId;	//归属省份id
    private String 	hareaId;	//归属地市id
    private String 	imei;	//imei
    private String 	imsi;	//imsi
    private String 	lac;	//lac
    private String 	ci;	//ci
    private String 	longitude;	//经度
    private String 	latitude;	//纬度
    private String 	provId;	//发生省份id
    private String  poweroffInd; //关机标识
}
