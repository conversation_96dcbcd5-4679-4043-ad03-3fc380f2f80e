package com.unicom.rts.independent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MidDataEnum {

    /*
    middata数据枚举信息
     */
    DEVEICENUMBER(0, "手机号"),
    TIME(1, "信令发生时间"),
    HPROVNAME(2, "归属省份名称"),
    HAREANAME(3, "归属地市名称"),
    AREA(4, "归属地市名称"),
    IMEI(5, "IMEI"),
    IMSI(6, "IMSI"),
    CURRENTTIME(7, "当前处理时间"),
    LAC(8, "LAC"),
    CI(9, "CI"),
    LONGITUDE(10, "经度"),
    LATITUDE(11, "纬度"),
    PROVID(12, "发生省份ID"),
    POWEROFFIND(13, "关机标识");

    private final Integer code;

    private final String msg;
}
