package com.unicom.rts.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2022-04-28 09:36
 */


@AllArgsConstructor
@Getter
public enum ProvEnum {

    /**
     * b域省分省会地市映射枚举
     */

    BEIJING("0010", "011"),
    HEBEI("0311", "018"),
    HENAN("0371", "076"),
    SHANDONG("0531", "017"),
    LIAONING("0024", "091"),
    HEILONGJIANG("0451", "097"),
    TIANJIN("0022", "013"),
    NEIMENGGU("0471", "010"),
    SHANXI("0351", "019"),
    ZHEJIANG("0571", "036"),
    SHANGHAI("0021", "031"),
    ANHUI("0551", "030"),
    HUBEI("0027", "071"),
    HAINAN("0898", "050"),
    J<PERSON>NGSU("0025", "034"),
    QINGHAI("0971", "070"),
    GUANGXI("0771", "059"),
    JIANGXI("0791", "075"),
    GUANGDONG("0020", "051"),
    XIZANG("0891", "079"),
    CHONGQING("0023", "083"),
    XINJIANG("0991", "089"),
    SICHUAN("0028", "081"),
    GANSU("0931", "087"),
    SHANXI3("0029", "084"),
    YUNNAN("0871", "086"),
    JILIN("0431", "090"),
    NINGXIA("0951", "088"),
    GUIZHOU("0851", "085"),
    FUJIAN("0591", "038"),
    HUNAN("0731", "074");



    private final String code;

    private final String msg;

    /**
     *  获取枚举类
     */
    public static ProvEnum getByCode(String code) {
        return Arrays.stream(ProvEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .orElse(null);

    }

    /**
     * 根据描述获取枚举类
     */
    public static ProvEnum getByMsg(String msg) {
        if(msg!=null && !"".equals(msg)) {
            return Arrays.stream(ProvEnum.values())
                    .filter(item -> item.getMsg().equals(msg))
                    .findAny()
                    .orElse(null);
        }
        else {
            return null;
        }
    }
}
