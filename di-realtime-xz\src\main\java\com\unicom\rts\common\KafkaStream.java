package com.unicom.rts.common;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;

public class KafkaStream {
    private static final String SOURCE_SUFFIX = "_Source";
    private static final String SINK_SUFFIX = "_Sink";
    private static final String SOURCE_PREFIX = "source.kafka.properties.";
    private static final String SINK_PREFIX = "sink.kafka.properties.";

    public static <T> DataStream<T> addSource(StreamExecutionEnvironment env, ParameterTool conf, String topic, KafkaRecordDeserializationSchema<T> deserializationSchema) {
        int parallelism = conf.getInt("source.parallelism", env.getParallelism());
        Properties properties = toProperties(conf, SOURCE_PREFIX);
        String startFrom = conf.get("kafkaConsumerStartFrom", "GroupOffsets");
        Long startFromTimeStamp = conf.getLong("kafkaConsumerStartFromTimeStamp", 0L);
        OffsetsInitializer initializer = OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST);
        if (startFromTimeStamp > 0L) {
            initializer = OffsetsInitializer.timestamp(startFromTimeStamp);
        } else if ("earliest".equals(startFrom)) {
            initializer = OffsetsInitializer.earliest();
        } else if ("latest".equals(startFrom)) {
            initializer = OffsetsInitializer.latest();
        }

        KafkaSource<T> source = KafkaSource.<T>builder()
                .setBootstrapServers(conf.get(SOURCE_PREFIX + "bootstrap"))
                .setGroupId(conf.get("group.id"))
                .setTopics(Arrays.asList(StringUtils.splitPreserveAllTokens(topic, ",")))
                .setProperties(properties)
                .setProperty("partition.discovery.interval.ms", "60000")
                .setStartingOffsets(initializer)
                .setDeserializer(deserializationSchema)
                .build();

        return env.fromSource(source, WatermarkStrategy.noWatermarks(), topic + SOURCE_SUFFIX).uid(topic + SOURCE_SUFFIX).setParallelism(parallelism);
    }

    public static FlinkKafkaConsumer<ConsumerRecord<String, String>> getRootConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String kafkaUser = conf.get("xz.kafka.user");
        String kafkaPasswordtmp = conf.get("xz.kafka.password");

        Base64.Decoder decoder = Base64.getDecoder();
        String kafkaPassword = new String(decoder.decode(kafkaPasswordtmp));

        //kafka配置
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, conf.get("xz.source.broker"));
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, conf.get("xz.group.id"));
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, conf.get("xz.enable.auto.commit"));
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,"1000");
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, conf.get("xz.offset.reset"));
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,"30000");
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        boolean sourceSecurity = conf.getBoolean("xz.source.kafka.security");

        if (sourceSecurity) {
            //天宫kafka安全设置
            properties.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", "SASL_PLAINTEXT");
            properties.setProperty("sasl.mechanism", "SCRAM-SHA-256");
        }

        FlinkKafkaConsumer<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer<>(topics, new CustomDeSerializationSchema(), properties);

        consumer.setCommitOffsetsOnCheckpoints(true);

        return consumer;

    }

    public static DataStream<String> addSource(StreamExecutionEnvironment env, ParameterTool conf, String topic) {
        return addSource(env, conf, topic, KafkaRecordDeserializationSchema.valueOnly(StringDeserializer.class));
    }

//    public static void addSink(DataStream<Tuple3<String, String, Iterable<Header>>> stream, ParameterTool conf) {
//        Properties properties = toProperties(conf, SINK_PREFIX);
//
//        final TopicSelector<Tuple3<String, String, Iterable<Header>>> topicSelector =
//                (tp) -> {
//                    return tp.f0;
//                };
//
//        final HeaderSelector<Tuple3<String, String, Iterable<Header>>> headerSelector =
//                (tp) -> {
//                    return tp.f2;
//                };
//
//        KafkaSink<Tuple3<String, String, Iterable<Header>>> sink = KafkaSink.<Tuple3<String, String, Iterable<Header>>>builder()
//                .setBootstrapServers(conf.get(SINK_PREFIX + "bootstrap"))
//                .setKafkaProducerConfig(properties)
//                .setRecordSerializer(new MultiTopicSerializationSchema()
//                        /*KafkaRecordSerializationSchema.builder()
//                        .setTopicSelector(topicSelector)
//                        .setHeaderSelector(headerSelector)
//                        .setValueSerializationSchema(new MultiTopicSchema())
//                        .build()*/
//                )
//                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
//                .build();
//
//        stream.sinkTo(sink).uid("KafkaMulti" + SINK_SUFFIX).setParallelism(Integer.parseInt(conf.get("sink.parallelism")));
//    }

//    public static void addSink(DataStream<Tuple3<String, String, Iterable<Header>>> stream, ParameterTool conf) {
//        // sink: sink kafka配置
//        Properties producerProp = new Properties();
//        producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
//        producerProp.put("acks", conf.get("acks", "0")); //Must set acks to all in order to use the idempotent producer
//        producerProp.put("retries", conf.get("retries", "2"));
//        producerProp.put("batch.size", conf.get("batch.size", "16384"));
//        producerProp.put("linger.ms", conf.get("linger.ms", "50"));
//        producerProp.put("buffer.memory", conf.get("buffer.memory", "33554432"));
//        producerProp.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));
//
//        // 下发kafka开启认证
//        boolean sinkSecurity = conf.getBoolean("sink.kafka.security");
//        if (sinkSecurity) {
//            String mechanism = conf.get("sasl.mechanism");
//            String protocol = conf.get("security.protocol");
//            String jaasConfig = conf.get("sasl.jaas.config");
//            producerProp.setProperty("sasl.jaas.config", jaasConfig + " required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
//            producerProp.setProperty("security.protocol", protocol);
//            producerProp.setProperty("sasl.mechanism", mechanism);
//        }
//
//
//        KafkaSink<Tuple3<String, String, Iterable<Header>>> sink = KafkaSink.<Tuple3<String, String, Iterable<Header>>>builder()
//                .setBootstrapServers(conf.get("sink.kafka.bootstrap"))
//                .setKafkaProducerConfig(producerProp)
//                .setRecordSerializer(new MultiTopicSerializationSchema())
//                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
//                .build();
//
//        stream.sinkTo(sink).uid("KafkaMulti" + SINK_SUFFIX).setParallelism(Integer.parseInt(conf.get("sink.parallelism")));
//    }

    public static Properties toProperties(ParameterTool conf, String prefix) {
        Properties properties = new Properties();
        // source Default Configuration
        properties.put("enable.auto.commit", conf.get("enable.auto.commit", "true"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset", "latest"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        // sink Default Configuration
        properties.put("acks", conf.get("acks", "0"));
        properties.put("retries", conf.get("retries", "3"));
        properties.put("batch.size", conf.get("batch.size", "16384"));
        properties.put("linger.ms", conf.get("linger.ms", "50"));
        properties.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        properties.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));

        conf.toMap().keySet().stream().filter(key -> key.startsWith(prefix)).forEach(key -> properties.put(key.replaceFirst(prefix, ""), conf.get(key)));

        Boolean security = conf.getBoolean(prefix + "security", false);
        if (security) {
            //kafka安全设置
            String kafkaUser = conf.get(prefix + "user");
            String kafkaPassword = conf.get(prefix + "password","tianGong#123");
            properties.setProperty("sasl.jaas.config", conf.get(prefix + "sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule") + " required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", conf.get(prefix + "security.protocol", "SASL_PLAINTEXT"));
            properties.setProperty("sasl.mechanism", conf.get(prefix + "sasl.mechanism", "SCRAM-SHA-256"));
        }

        return properties;
    }

    /**
     * 解析配置
     *
     * @param conf
     * @param prefix
     * @return
     */
    public static ParameterTool mergeConf(ParameterTool conf, String prefix) {
        Map<String, String> tmpMap = new HashMap<>();
        conf.toMap().keySet().stream().filter(key -> key.startsWith(prefix)).forEach(key -> tmpMap.put(key.replaceFirst(prefix, ""), conf.get(key)));
        ParameterTool tmpConf = ParameterTool.fromMap(tmpMap);
        return conf.mergeWith(tmpConf);
    }
}
