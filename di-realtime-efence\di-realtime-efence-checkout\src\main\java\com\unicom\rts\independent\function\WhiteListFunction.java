package com.unicom.rts.independent.function;

import com.unicom.rts.independent.beans.RecordBean;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/
public class WhiteListFunction extends BroadcastProcessFunction<RecordBean, List<String>, RecordBean> {

    private List<String> whiteList;

    @Override
    public void processElement(RecordBean recordBean, ReadOnlyContext ctx, Collector<RecordBean> out) throws Exception {
        if (!whiteList.contains(recordBean.getDeviceNum())){
            out.collect(recordBean);
        }
    }

    @Override
    public void processBroadcastElement(List<String> inputList, Context ctx, Collector<RecordBean> out) throws Exception {
        whiteList = inputList;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        whiteList = new ArrayList<>();
    }
}
