package com.unicom.rts.function;

import com.unicom.rts.bean.Pay;
import com.unicom.rts.util.HbaseUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class Distinct extends RichFlatMapFunction<Pay, Pay> {
    ParameterTool conf;
    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private static byte[] FAMILYNAME = "f".getBytes();

    public Distinct(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        //初始化hbase链接
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));

    }

    @Override
    public void flatMap(Pay pay, Collector<Pay> collector) throws Exception {
        //如果没处理过
        if (notSendOut(pay.getCharge_id() + "_" + pay.getRecv_time() + "_" + pay.getSerial_number())) {
            collector.collect(pay);
        }
    }

    @Override
    public void close() throws Exception {
        if (table != null) {
            table.close();
        }
    }

    public boolean notSendOut(String key) throws IOException {
        String md5HexKey = DigestUtils.md5Hex(key);
        //先查询，是否下发过
        Result result = hbaseUtil.selectDataByRowkey(null, table, md5HexKey);
        //未下发过，需要下发，且向hbase中存储
        if (result.isEmpty()) {
            //负载均衡
            Put put = new Put(hbaseUtil.getRowKey(md5HexKey));
            put.addColumn(FAMILYNAME, "key".getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8));
            if (!put.isEmpty()) {
                table.put(put);
            }
        }
        return result.isEmpty();
    }
}
