package com.unicom.utils;

import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPubSub;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021-06-11 19:58
 */

public class RedisUtil implements Serializable {

	private static final long serialVersionUID = -6058519227235257780L;

	private Logger logger = LoggerFactory.getLogger(RedisUtil.class);
	private JedisCluster jedisCluster = null;

	public JedisCluster getConn(ParameterTool conf) {
		try {
			if (jedisCluster == null) {
				jedisCluster = RedisPool.getPool(conf);
			}
		} catch (Exception ex) {
			logger.error(ex.getMessage(), ex);
			logger.error("##### redis失去连接..........");
			jedisCluster = null;
		}
		return jedisCluster;
	}

	/**
	 * 入key value 设置过期时间
	 */
	public void addKeyValueEx(JedisCluster redis, String key, String value, Integer extime) {
		redis.setex(key, extime, value);
	}
	/**
	 * 获取到key的信息
	 *
	 * @return
	 */
	public String getMapKey(JedisCluster redis, String key) {
		String res = redis.get(key);
		return res;
	}

	public List<String> getList(JedisCluster redis, String key, int start, int end) {
		//List<String> arr = redis.lrange(key, 0, redis.llen(key));
		List<String> arr = redis.lrange(key, start, end);
		return arr;
	}
	public Long getListSize(JedisCluster redis, String key) {
		return redis.llen(key);
	}

	/**
	 * 获取到key的信息,这个value对应的是list
	 *
	 * @return
	 */
	public List<String> getListKey(JedisCluster redis, String key) {
		List<String> arr = redis.lrange(key, 0, redis.llen(key));
		return arr;
	}

	/**
	 * 将数据入到value中，val的值是list
	 * @param redis
	 * @param key
	 * @param value
	 */
	public void setRpush(JedisCluster redis, String key, String... value) {
		redis.rpush(key, value);
	}

	public List<String> getListByKey(JedisCluster redis, String key) {
		Set<String> keys = redis.keys(key);
		List<String> list = new ArrayList<String>();
		for(String k : keys) {
			list.addAll(redis.lrange(k, 0, -1));
		}

		return list;
	}
	/**
	 * 获取到key的信息
	 *
	 * @return
	 */
	public Boolean isExistKey(JedisCluster redis, String key) {
		return redis.exists(key);
	}

	/**
	 * 获取到key的信息,这个key是支持pattern的
	 *
	 * @return
	 */
	public Integer getKeysNumber(JedisCluster redis, String key) {
		return redis.keys(key).size();
	}

	/**
	 * 获取所有key的信息
	 *
	 * @return
	 */
	public Set<String> getPatternKeys(JedisCluster redis, String key) {
		return redis.keys(key);
	}

	/**
	 * redis设置set
	 * @param redis
	 * @param key
	 * @param values
	 */
	public void setSet(JedisCluster redis, String key, List<String> values){
		redis.sadd(key, values.toArray(new String[0]));
	}

	/**
	 * redis设置set
	 * @param redis
	 * @param key
	 */
	public Set<String> getSet(JedisCluster redis, String key){
		return redis.smembers(key);
	}

	/**
	 * 设置map信息(支持多个的)
	 * @param redis
	 * @param key
	 * @param values
	 */
	public void setHMap(JedisCluster redis, String key, Map<String,String> values){
		redis.hmset(key, values);
	}

	/**
	 * 获取map信息(支持多个的)
	 */
	public List<String> getHMap(JedisCluster redis, String key, String... value){
		return redis.hmget(key, value);
	}


	/**
	 * 设置map信息(支持多个的)
	 * @param redis
	 * @param key
	 */
	public void setMap(JedisCluster redis, String key, String field, String value){
		redis.hset(key, field, value);
	}

	/**
	 * 获取map信息(支持多个的)
	 */
	public String getMap(JedisCluster redis, String key, String field){
		return redis.hget(key, field);
	}

	/**
	 * 增加值
	 */
	public Long addNum(JedisCluster redis, String key){
	      return redis.incr(key);
	}

	/**
	 * 发布消息
	 * @param jedis
	 * @param channel
	 * @param message
	 */
	public void publish(JedisCluster jedis, String channel, String message){
		jedis.publish(channel, message);
	}

	/**
	 * 订阅某个频道
	 * @param jedis
	 * @param sub
	 * @param channel
	 */
	public void subscribe(JedisCluster jedis, JedisPubSub sub, String channel){
		jedis.subscribe(sub, channel);
	}

	/**
	 * 删除某个key
	 * @param redis
	 * @param key
	 */
	public void delKey(Jedis redis, String key){
		 redis.del(key);
	}

}
