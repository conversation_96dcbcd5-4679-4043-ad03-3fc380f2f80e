package com.unicom.rts.independent.util;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.util.List;

public class HbaseUtil {
    private final static Logger logger = Logger.getLogger(HbaseUtil.class);

    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private Configuration conf = null;
    private UserGroupInformation ugi = null;
    private Connection connection = null;

    private static String hbaseEncode = "utf8";// 编码格式

    public Connection init(String zkIp, String port, String user) {
        Configuration hbaseConfig = new Configuration();
        hbaseConfig.set("hbase.zookeeper.quorum", zkIp);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", port);// zookeeper端口
        hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
        conf = HBaseConfiguration.create(hbaseConfig);
        try {
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(user);
            connection = ConnectionFactory.createConnection(conf, User.create(userGroupInformation));
            logger.info("成功连接Hbase {}" + conf.toString());
            return connection;
        } catch (IOException e) {
            logger.error("获取Hbase连接异常，" + e.getClass() + "：" + e.getMessage());
            return null;
        }
    }

    public Result getDataFromHbase(String rowKeys, HTable table) throws IOException {
        Get get = new Get(getRowKeySimple(rowKeys));
        return table.get(get);
    }

    public byte[] getRowKey(String rowkeys) {
        byte[] rowkeysByte = Bytes.toBytes(rowkeys);
        byte[] rowkeysHash = Bytes.toBytes((short) (rowkeys.hashCode() & 0x7fff));
        return Bytes.add(rowkeysHash, rowkeysByte);
    }

    public byte[] getRowKeySimple(String rowkeys) {
        byte[] rowkeysByte = Bytes.toBytes(rowkeys);
        return rowkeysByte;
    }

    public Result getValuesWithColumns2(HTable hTable, String rowKey, String columnfamily, List<String> columns) throws IOException, InterruptedException {
        try {
            if (StringUtils.isEmpty(hTable.getName().toString()) || StringUtils.isEmpty(rowKey)) {
                return null;
            }

            Get get = new Get(Bytes.toBytes(rowKey));
            Result result = hTable.get(get);
            for (String column : columns) {
                if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isNotEmpty(column)) {
                    get.addColumn(Bytes.toBytes(columnfamily), Bytes.toBytes(column));
                }
            }
            boolean resultExists = result.isEmpty();
            if (resultExists) {
                return null;
            }

            return result;

        } catch (IOException e) {
            logger.error("Table :" + hTable.getName() + " get" + rowKey + "exception: " + e);
            // e.printStackTrace();
            return null;
        }
    }
}

