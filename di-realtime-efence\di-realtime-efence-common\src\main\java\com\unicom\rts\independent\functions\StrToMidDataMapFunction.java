package com.unicom.rts.independent.functions;

import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;

public class StrToMidDataMapFunction implements MapFunction<String, MidDataBean> {
    @Override
    public MidDataBean map(String value) throws Exception {
        String[] cols = StringUtils.splitPreserveAllTokens(value, "\1");
        MidDataBean midDataBean = new MidDataBean();
        midDataBean.setDeviceNumber(cols[0]);
        midDataBean.setTime(cols[1]);
        midDataBean.setHprovName(cols[2]);
        midDataBean.setHcityName(cols[3]);
        midDataBean.setImei(cols[4]);
        midDataBean.setImsi(cols[5]);
        midDataBean.setCurrentTime(cols[6]);
        midDataBean.setLac(cols[7]);
        midDataBean.setCi(cols[8]);
        midDataBean.setLatitude(cols[9]);
        midDataBean.setLatitude(cols[10]);
        midDataBean.setProvId(cols[11]);
        midDataBean.setPoweroffInd(cols[12]);
        return midDataBean;

    }
}
