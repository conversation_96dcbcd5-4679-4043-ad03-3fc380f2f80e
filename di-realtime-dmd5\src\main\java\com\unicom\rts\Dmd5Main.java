package com.unicom.rts;

import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.Rsp;
import com.unicom.rts.bean.TConnect;
import com.unicom.rts.constant.CommonConstant;
import com.unicom.rts.function.DataProcessFunction;
import com.unicom.rts.function.InitConfiguration;
import com.unicom.rts.sink.TencentEKafkaSink;
import com.unicom.rts.source.TencentEKafkaSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.concurrent.TimeUnit;

public class Dmd5Main {

    public static void main(String[] args) throws Exception {

        ParameterTool parameters = ParameterTool.fromArgs(args);
        //获取flink 环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        String flinkId = "dmd5";
        String sourceParallelism = parameters.get(CommonConstant.SOURCE_PARALLELISM, "1");
        String sinkParallelism = parameters.get(CommonConstant.SINK_PARALLELISM, "1");
        String processParallelism = parameters.get(CommonConstant.PROCESS_PARALLELISM, "1");
        env.setParallelism(1);

        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(parameters.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        //checkpoint
        int enableCheckpoint = parameters.getInt("enableCheckpoint", 2);
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(enableCheckpoint));

        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        int minPauseBetween = parameters.getInt("minPauseBetween", 2);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetween));

        int checkpointTimeout = parameters.getInt("checkpointTimeout", 10);
        checkpointConfig.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10,
                Time.of(10, TimeUnit.SECONDS)));

        TConnect hbaseTConnect = InitConfiguration.initFunc(parameters);
        Configuration hbaseConfiguration = new Configuration();
        hbaseConfiguration.setString("hbase.zookeeper",hbaseTConnect.getIpCode() );
        JSONObject connectMessage = JSONObject.parseObject(hbaseTConnect.getConnectMessage());
        String hbaseUser = connectMessage.getString("hbase_user");
        if (StringUtils.isBlank(hbaseUser)) {
            hbaseUser =parameters.get("hbaseUser", "hh_slfn2_sschj_gray");
        }
        hbaseConfiguration.setString("hbase.zookeeper.port", "2181");
        hbaseConfiguration.setString("hbase.user",hbaseUser );
        hbaseConfiguration.setString("hbase.table",hbaseUser+":rts_user_tag" );
        hbaseConfiguration.setString("terminated",parameters.get("terminated", "\u0001"));
        hbaseConfiguration.setString("zookeeper.znode.parent",connectMessage.getString("hbase_zookeeper_znode_parent"));

        hbaseConfiguration.setString("dst_topic_name",parameters.get("dst_topic_name", "new_buy_terminal"));

        hbaseConfiguration.setString("db.url",parameters.get("db.url", CommonConstant.DB_URL));
        hbaseConfiguration.setString("db.user",parameters.get("db.user", CommonConstant.DB_USER));
        hbaseConfiguration.setString("db.passwd",parameters.get("db.passwd", CommonConstant.DB_PASSWD));

        env.getConfig().setGlobalJobParameters(hbaseConfiguration);

        FlinkKafkaConsumer<ConsumerRecord<Object, Object>> source = TencentEKafkaSource.getFlinkKafkaConsumer(parameters);


        SingleOutputStreamOperator<ConsumerRecord<Object, Object>> inputStream = env.addSource(source)
                .uid(flinkId+"-Source-Kafka")
                .name(flinkId+"-Source-Kafka")
                .setParallelism(Integer.valueOf(sourceParallelism));

        KeyedStream<Tuple2<Rsp, String>,String> proStream = inputStream.process(new DataProcessFunction())
                .uid(flinkId+"-process")
                .name(flinkId+"-process")
                .setParallelism(Integer.valueOf(processParallelism))
                .keyBy(new KeySelector<Tuple2<Rsp, String>,String>() {

                    @Override
                    public String getKey(Tuple2<Rsp, String> value) throws Exception {
                        return value.f0.getKeyBy();
                    }

                });

        FlinkKafkaProducer<Tuple2<Rsp, String>> sink = TencentEKafkaSink.getFlinkKafkaProducer(parameters);

        proStream.addSink(sink)
                .uid(flinkId+"-sinkUid")
                .name(flinkId+"-sink")
                .setParallelism(Integer.valueOf(sinkParallelism));;


        env.execute(flinkId+"-job");
    }
}
