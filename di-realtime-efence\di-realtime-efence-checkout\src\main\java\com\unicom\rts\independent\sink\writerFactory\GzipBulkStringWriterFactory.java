package com.unicom.rts.independent.sink.writerFactory;

import com.unicom.rts.independent.sink.writer.GzipStringBulkWriter;
import org.apache.flink.api.common.serialization.BulkWriter;
import org.apache.flink.core.fs.FSDataOutputStream;

import java.io.IOException;
import java.util.zip.GZIPOutputStream;

public class GzipBulkStringWriterFactory implements BulkWriter.Factory<String> {

	@Override
	public BulkWriter<String> create(FSDataOutputStream out) throws IOException {
		// TODO Auto-generated method stub
		GZIPOutputStream gzipOutputStream = new GZIPOutputStream(out,true);
		// GZIPOutputStream gzipOutputStream = new GZIPOutputStream(out);
		return new GzipStringBulkWriter(gzipOutputStream);
	}

}