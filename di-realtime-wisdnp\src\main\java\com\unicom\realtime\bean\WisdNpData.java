package com.unicom.realtime.bean;

import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * <AUTHOR>
 * 携转信息映射类，下发字段
 */
@Data
public class WisdNpData {
    /**
     * 日期格式化字符串 （秒级别）
     */
    private static final String DATETIME_FORMAT_S = "yyyy-MM-dd HH:mm:ss";

    /**
     * 分隔符
     */
    private static final String SEPARATOR = "\u0001";



    /**
     * (共15个字段)
     *  省分名称（VARCHAR(30)）
     *      DEMO：黑龙江
     *      kafka数据源：省分编码转换省分名称
     */
    private String province_code;

    /**
     * 地市名称（VARCHAR(30)）
     *      DEMO：哈尔滨市
     *      kafka数据：地市编码转换地市名称
     */
    private String eparchy_code;

    /**
     * 用户标识（NUMBER(16)）
     *      TF_F_USER.user_id 关联：TF_F_USER.serial_number=电话号码
     */
    private Long user_id;

    /**
     * 电话号码（VARCHAR(11)）
     *      kafka数据：服务号码
     */
    private String serial_number;

    /**
     * 渠道大类型名称（VARCHAR(10)）
     *      DEMO：实体
     *      1) 取渠道类型ID TF_CHL_CHANNEL.chnl_kind_id 关联 TF_F_USER.open_depart_id=TF_CHL_CHANNEL.chnl_id
     *      2) 取渠道大类型名 TD_CHL_KINDDEF.parent_chnl_kind_name 关联 TF_CHL_CHANNEL.chnl_kind_id = TD_CHL_KINDDEF.chnl_kind_id
     *      3） 实体/电子/政企/其他，只体现这四种类型
     */
    private String parent_chnl_kind_name;

    /**
     * 渠道编码（VARCHAR(10)）
     *      TF_CHL_CHANNEL.chnl_code
     */
    private String chnl_code;

    /**
     * 渠道名称（VARCHAR（200））
     *      TF_CHL_CHANNEL.chnl_name
     */
    private String chnl_name;

    /**
     * 原始运营商（VARCHAR(10)）
     *      DEMO：电信
     *      kafka数据：号码拥有方网络ID
     *      前三位为运营商代码。
     *          001电信
     *          002移动
     *          003联通
     */
    private String home_net;

    /**
     * 携出运营商（VARCHAR(10)）
     *      DEMO：电信
     *      kafka数据：携出方网络ID
     *      前三位为运营商代码。
     *          001电信
     *          002移动
     *          003联通
     */
    private String port_out_net;

    /**
     * 携入运营商（VARCHAR(10)）
     *      DEMO：联通
     *      kafka数据：携入方网络ID
     *      前三位为运营商代码。
     *      001电信
     *      002移动
     *      003联通
     */
    private String port_in_net;

    /**
     * 携转时间（TIMESTAMP）
     *      kafka数据：开始时间
     */
    private Long port_time;

    /**
     * 开户时间（DATE）
     *      TF_F_USER.open_date
     */
    private Long open_date;

    /**
     * 产品编码（NUMBER(8)）
     *      TF_F_USER.product_id
     */
    private Long product_id;

    /**
     * 产品名称（VARCHAR(100)）
     *      TD_B_PRODUCT.PRODUCT_NAME 关联 TF_F_USER.product_id=TD_B_PRODUCT.PRODUCT_ID
     */
    private String product_name;

    /**
     * 网别（VARCHAR(2)）
     *      DEMO：3G
     *      关联TF_F_USER表的net_type_code和brand_code，根据表1转换后为3G/4G/5G，其他网别置为空
     */
    private String net_type;

    /**
     * 携转日期（DATE）
     *      DEMO：20220519
     *      port_time 取日期
     */
    private String port_date;

    @Override
    public String toString() {
        //下发字段
        StringBuilder sb = new StringBuilder();
        sb.append(province_code);
        sb.append(SEPARATOR);
        sb.append(eparchy_code);
        sb.append(SEPARATOR);
        sb.append(user_id);
        sb.append(SEPARATOR);
        sb.append(serial_number);
        sb.append(SEPARATOR);
        sb.append(parent_chnl_kind_name);
        sb.append(SEPARATOR);
        sb.append(chnl_code);
        sb.append(SEPARATOR);
        sb.append(chnl_name);
        sb.append(SEPARATOR);
        sb.append(home_net);
        sb.append(SEPARATOR);
        sb.append(port_out_net);
        sb.append(SEPARATOR);
        sb.append(port_in_net);
        sb.append(SEPARATOR);
        sb.append(port_time);
        sb.append(SEPARATOR);
        sb.append(open_date);
        sb.append(SEPARATOR);
        sb.append(product_id);
        sb.append(SEPARATOR);
        sb.append(product_name);
        sb.append(SEPARATOR);
        sb.append(net_type);
        sb.append(SEPARATOR);
        sb.append(port_date);
        sb.append(SEPARATOR);
        sb.append(DateFormatUtils.format(System.currentTimeMillis(), DATETIME_FORMAT_S));
        return sb.toString();
    }

    public static void main(String[] args){
        System.out.println(System.currentTimeMillis());
    }
}
