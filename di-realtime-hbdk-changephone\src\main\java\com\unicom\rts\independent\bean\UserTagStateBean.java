package com.unicom.rts.independent.bean;

import lombok.Data;

@Data
public class UserTagStateBean {
    private String deviceNumber;    //手机号
    private String k000046;          //号码归属省份
    private String k003681;          //号码归属地市（O域）
    private String k002436;          //号码归属地市（B域）
    private String k003589;          //入网时间（日）
    private String userId;                     //用户标识
    private String eparchyCode;                //归属地州
    private String finishDate;                 //完成时间
    private String provId;                     //归属省分id
    private String eparchyCodeFinal;                     //优先取实时标签的归属地市
    private String inDate;
    private String inDateMonth;      //入网月份
    private String provIdFinal;

}
