package com.unicom.rts.bean;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Xz {

    private String opt;
    private String netTypeCode;
    private String tradeTypeCode;
    private String subscribeState;
    private String nextDealTag;
    private String cancelTag;

    private String provinceCode;//省分编码
    private String eparchyCode; //地市编码
    private String serialNumber; //用户号码
    private String outTag; //携转状态  //0可以携转 1不能携转 --需调用接口
    private String limitRemark; //不可携转原因 --需调用接口


    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap();

    public void parseHeader(ConsumerRecord<String,String> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, Bytes.toString(next.value()));
            }
        }
        headersMap.put("scene_id", "");
        headersMap.put("root_time", String.valueOf((record.timestamp())));
        headersMap.put("process_time", String.valueOf(System.currentTimeMillis()));
        if (headers.lastHeader("receive_time") != null && headers.lastHeader("receive_time").value() != null) {
            headersMap.put("receive_time",new String(headers.lastHeader("receive_time").value()));
        }
        if (headers.lastHeader("integration_time") != null && headers.lastHeader("integration_time").value() != null) {
            headersMap.put("integration_time",new String(headers.lastHeader("integration_time").value()) );
        }
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    public String toString(String topic) {
        String s = "\001";
        StringBuilder sb = new StringBuilder();
        sb.append(provinceCode);
        sb.append(s);
        sb.append(eparchyCode);
        sb.append(s);
        sb.append(serialNumber);
        sb.append(s);
        sb.append(outTag);
        sb.append(s);
        sb.append(limitRemark);

        return sb.toString();
    }
}
