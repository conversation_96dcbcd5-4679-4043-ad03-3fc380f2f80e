package com.unicom.rts.independent.schema;

import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/11
 **/
public class CustomDeserializationSchema implements KafkaDeserializationSchema<ConsumerRecord<String, String>> {

    private static  String encoding = "UTF8";

    @Override
    public boolean isEndOfStream(ConsumerRecord<String, String> nextElement) {
        return false;
    }

    @Override
    public ConsumerRecord<String, String> deserialize(ConsumerRecord<byte[], byte[]> record) throws Exception {
        String key;
        String value;
        if (null == record.key()) {
            key = "";
        } else {
            key = new String(record.key(), encoding);
        }

        if (null == record.value()) {
            value = "";
        } else {
            value = new String(record.value(), encoding);
        }

        return new ConsumerRecord(record.topic(),
                record.partition(),
                record.offset(),
                record.timestamp(),
                record.timestampType(),
                record.checksum(),
                record.serializedKeySize(),
                record.serializedValueSize(),
                key,
                value);
    }

    @Override
    public TypeInformation<ConsumerRecord<String, String>> getProducedType() {
        return TypeInformation.of(new TypeHint<ConsumerRecord<String, String>>(){});
    }
}
