package com.unicom.source;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Arrays;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/11
 **/
public class KafkaSource {

    public static FlinkKafkaConsumer<ConsumerRecord<String, String>> getKafkaConsumer(ParameterTool conf) {

        String kafkaUser = conf.get("source.kafka.user");
        String kafkaPassword = conf.get("source.kafka.password");

        Properties properties = new Properties();
        properties.put("bootstrap.servers", conf.get("source.bootstrap"));
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "100000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "300000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        //sasl认证
        properties.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
        properties.setProperty("security.protocol", "SASL_PLAINTEXT");
        properties.setProperty("sasl.mechanism", "PLAIN");

        FlinkKafkaConsumer<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer<ConsumerRecord<String, String>>(
                Arrays.asList(conf.get("source.topic").split(",")), new CustomDeserializationSchema(), properties);
        consumer.setCommitOffsetsOnCheckpoints(true);
        return consumer;
    }
}
