package com.unicom.utils;


import java.io.*;
import java.util.Enumeration;
import java.util.Properties;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2021-06-11 19:58
 */
public class Utilties {

	
	/**
	 * 获取外部配置文件
	 * @param file
	 * @return
	 * @throws IOException
	 */
	public static Properties loadOutParams(String file) throws IOException {

		Properties prop = new Properties();
		String proFilePath = System.getProperty("user.dir")+File.separator + file;  
		InputStream in = new BufferedInputStream(new FileInputStream(proFilePath));  
		ResourceBundle bundle = new PropertyResourceBundle(in);

		String key = null;
		for (Enumeration<String> enuma = bundle.getKeys(); enuma
				.hasMoreElements();) {
			key = enuma.nextElement();
			prop.put(key, bundle.getObject(key));
		}
		return prop;
	}

}
