package com.unicom.rts.function;

import com.google.gson.Gson;
import com.unicom.rts.entity.ThrData;
import com.unicom.rts.enums.ThrEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;


public class ThrClean extends RichFlatMapFunction<ConsumerRecord<String, String>, ThrData> {
    private final static Logger logger = LoggerFactory.getLogger(ThrClean.class);
    private transient long delay = 0L;
    private transient long filter = 0L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record,Collector<ThrData> out) throws Exception {
        ThrData vcf = new ThrData();
        parse(vcf, record);
        if(vcf!=null && !"".equals(vcf) && vcf.getDeviceNumber()!=null && !"".equals(vcf.getDeviceNumber())) {
            //增加头
            vcf.parseHeader(record);
            out.collect(vcf);
        }
    }

    public void parse(ThrData thrData, ConsumerRecord<String,String> record) {
        String[] line = StringUtils.splitPreserveAllTokens(record.value(), "\001");
//        if (11 != line[ThrEnum.deviceNumber.ordinal()].length()) {
//            return;
//        }
        thrData.setDeviceNumber(line[ThrEnum.deviceNumber.ordinal()]);
        thrData.setSmsNoticeId(line[ThrEnum.smsNoticeId.ordinal()]);
        thrData.setEparchyCode(line[ThrEnum.eparchyCode.ordinal()]);
        thrData.setEarlyWarningType(line[ThrEnum.earlyWarningType.ordinal()]);
        thrData.setDomesticJacketUsedflow(line[ThrEnum.domesticJacketUsedflow.ordinal()]);
        thrData.setDomesticJacketResidualflow(line[ThrEnum.domesticJacketResidualflow.ordinal()]);
        thrData.setDomesticOutsetUsedflow(line[ThrEnum.domesticOutsetUsedflow.ordinal()]);
        thrData.setDealTime(line[ThrEnum.dealTime.ordinal()]);
        thrData.setSaturationThreshold(line[ThrEnum.saturationThreshold.ordinal()]);
        thrData.setIntime(line[ThrEnum.intime.ordinal()]);
        thrData.setDatasource(line[ThrEnum.datasource.ordinal()]);
        thrData.setCdhTime(line[ThrEnum.cdhTime.ordinal()]);

    }
}
