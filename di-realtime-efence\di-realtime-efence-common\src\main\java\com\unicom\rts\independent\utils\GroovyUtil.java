package com.unicom.rts.independent.utils;

import lombok.extern.slf4j.Slf4j;
import org.codehaus.groovy.jsr223.GroovyScriptEngineFactory;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import java.util.Map;

@Slf4j
public class GroovyUtil {

    private static ScriptEngine engine;

    static {
        GroovyScriptEngineFactory scriptEngineFactory = new GroovyScriptEngineFactory();
        engine = scriptEngineFactory.getScriptEngine();
    }

    /**
     * @desc 执行groovy脚本(不指定方法)
     * @param script
     *            要执行的脚本 通过字符串传入，应用场景 如从数据库中读取出来的脚本等
     * @param params
     *            执行grovvy需要传入的参数
     * @return 脚本执行结果
     */
    public static Object runGroovyScript(String script, Map<String, Object> params) {
        if (script == null || "".equals(script)) {
            throw new RuntimeException("方法runGroovyScript无法执行，传入的脚本为空");
        }

        try {
            if(engine == null){
                GroovyScriptEngineFactory scriptEngineFactory = new GroovyScriptEngineFactory();
                engine = scriptEngineFactory.getScriptEngine();
            }
            Bindings bindings = engine.createBindings();
            bindings.putAll(params);
            return engine.eval(script, bindings);
        } catch (Exception e) {
            log.info("groovy处理异常，异常信息为" + e.getMessage());
            return null;
        }
    }

//    public static void main(String[] args) {
//
//        String groovyScript = "age>26 && age1>29 && name.contains('个人') ";
//        Map<String,Object> param = new HashMap<>();
//        param.put("name","一个人的");
//        param.put("age",30);
//        param.put("age1",30);
//        param.put("pro","qwer");
//        Object result = runGroovyScript(groovyScript,param);
//        System.out.println(result);
//    }


}
