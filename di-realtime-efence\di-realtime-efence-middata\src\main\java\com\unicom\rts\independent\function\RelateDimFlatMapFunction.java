package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocTradeBean;
import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.regex.Pattern;

import static com.unicom.rts.independent.utils.MapUtil.fileToMap;

public class RelateDimFlatMapFunction extends RichFlatMapFunction<LocTradeBean, MidDataBean> {

    private HashMap<String,String> provAreaMap = new HashMap<>();
    private HashMap<String,String> provNameMap = new HashMap<>();
    private HashMap<String,String> areaNameMap = new HashMap<>();
    private ArrayList<String> roamTypeList = new ArrayList<>();
    private SimpleDateFormat formatter;

    public RelateDimFlatMapFunction() {
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        //加载分发文件，创建map结构
        File dimProvArea = getRuntimeContext().getDistributedCache().getFile("dim_provarea");
        provAreaMap = fileToMap(dimProvArea,",",0,2);
        provNameMap = fileToMap(dimProvArea,",",2,3);
        areaNameMap = fileToMap(dimProvArea,",",0,1);
        System.out.println(provNameMap);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        roamTypeList.add("0201");
        roamTypeList.add("0202");
        roamTypeList.add("0203");
        roamTypeList.add("0204");
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void flatMap(LocTradeBean locTradeBean, Collector<MidDataBean> out) throws Exception {
        if(locTradeBean.getHrovId().equals(provAreaMap.getOrDefault(locTradeBean.getHareaId(),""))){
            if(filter(locTradeBean)){
                out.collect(createMidData(locTradeBean));
            }
        }
    }

    private boolean filter(LocTradeBean locTradeBean){
        return (roamTypeList.contains(locTradeBean.getRoamType()) && !locTradeBean.getHrovId().equals("051")
                && !locTradeBean.getHrovId().equals("075") && !locTradeBean.getHrovId().equals("083"))
                || (locTradeBean.getHrovId().equals("051"))
                || (locTradeBean.getHrovId().equals("083"))
                || (roamTypeList.contains(locTradeBean.getRoamType()) && locTradeBean.getHrovId().equals("075")
                    && !Pattern.matches("^145.*", locTradeBean.getDeviceNumber()));

    }

    private MidDataBean createMidData(LocTradeBean locTradeBean){
        MidDataBean midDataBean = new MidDataBean();
        midDataBean.setDeviceNumber(locTradeBean.getDeviceNumber());
        midDataBean.setTime(locTradeBean.getTime());
        midDataBean.setHprovName(provNameMap.get(locTradeBean.getHrovId()));
        midDataBean.setHcityName(areaNameMap.get(locTradeBean.getHareaId()));
        midDataBean.setImei(locTradeBean.getImei());
        midDataBean.setImsi(locTradeBean.getImsi());
        midDataBean.setCurrentTime(formatter.format(new Date()));
        midDataBean.setLac(locTradeBean.getLac());
        midDataBean.setCi(locTradeBean.getCi());
        midDataBean.setLongitude(locTradeBean.getLongitude());
        midDataBean.setLatitude(locTradeBean.getLatitude());
        midDataBean.setProvId(locTradeBean.getProvId());
        midDataBean.setPoweroffInd(locTradeBean.getPoweroffInd());
        return midDataBean;
    }
}
