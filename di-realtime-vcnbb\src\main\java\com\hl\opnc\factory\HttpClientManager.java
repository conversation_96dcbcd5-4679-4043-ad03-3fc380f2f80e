package com.hl.opnc.factory;


import com.hl.opnc.AbilityResponse;
import com.hl.opnc.config.ConfigUtils;
import com.hl.opnc.invoke.AbilityInvokeManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * @desc:
 * @fileName: HttpClientManager
 * @author: tangjiaxiang
 * @createTime: 2020/12/2 9:13:13
 * @modifier:
 */
public class HttpClientManager {

    private final static Logger log = LoggerFactory.getLogger(HttpClientManager.class);

    /**
     * HttpClient实现类类名 property
     */
    private final static String HTTP_CLIENT_CLASS_NAME = "sdk.HttpClientClassName";

    public static HttpClientFactory getHttpClient() {

        String httpClientClassName = ConfigUtils.INSTANCE.getProperty(HTTP_CLIENT_CLASS_NAME);
        log.debug("httpClientClassName: " + httpClientClassName);

        Object o;
        try {
            Class<?> defaultHttpClientImpl = Class.forName(httpClientClassName);
            o = defaultHttpClientImpl.newInstance();
        } catch (ClassNotFoundException | IllegalAccessException | InstantiationException e) {
            log.error("HttpClientManager Exception {}",e);
            throw new RuntimeException(e);
        }

        return (HttpClientFactory) o;
    }

    public static void main(String[] args) throws IOException {
        AbilityInvokeManager abilityInvokeManager = new AbilityInvokeManager("123", "123");
        AbilityResponse response = abilityInvokeManager.post("https://47.94.15.32/api/user/login");
        String respStr = response.getRespStr();
        System.out.println(respStr);
    }
}
