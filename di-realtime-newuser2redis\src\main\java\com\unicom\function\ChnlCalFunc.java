package com.unicom.function;

import com.unicom.beans.ChnlInfoBean;
import com.unicom.beans.NewUserBean;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.util.Map;

import static com.unicom.utils.ReadTxtUtil.readTxtsFromHdfs;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/23
 **/
public class ChnlCalFunc extends ProcessFunction<NewUserBean, NewUserBean> {

    private ParameterTool conf;

    private FileSystem fs;

    private Map<String, ChnlInfoBean> chnlAreaMap;

    public ChnlCalFunc(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        fs = getFileSystem(conf.get("fs.defaultFS"));
        Path chnlPath = new Path(conf.get("chnlPath"));
        if (fs.exists(chnlPath)) {
            FileStatus[] lists=fs.listStatus(chnlPath);
            chnlAreaMap = readTxtsFromHdfs(lists, fs);
        }

        //File dimArea = getRuntimeContext().getDistributedCache().getFile("dwd_d_mrt_e_chnl_area_info");
        //chnlAreaMap = readTxts(dimArea);
        //chnlAreaMap = fileToMap(dimArea,"|");
    }

    @Override
    public void processElement(NewUserBean newUserBean, Context context, Collector<NewUserBean> out) throws Exception {
        ChnlInfoBean currChnlInfo = chnlAreaMap.get(newUserBean.getTradeDepartId());
        if (currChnlInfo != null) {
            newUserBean.setChnlName(currChnlInfo.getChnlName());
            newUserBean.setLevelYsCode(currChnlInfo.getLevelYsCode());
            newUserBean.setLevel3(currChnlInfo.getLevel3());
            newUserBean.setLevel3Code(currChnlInfo.getLevel3Code());
            out.collect(newUserBean);
        } else {
            out.collect(newUserBean);
        }
    }

    public FileSystem getFileSystem(String defaultFS) throws Exception {
        org.apache.hadoop.conf.Configuration config = new org.apache.hadoop.conf.Configuration();
        config.set("fs.defaultFS", defaultFS);
        return FileSystem.get(config);
    }
}
