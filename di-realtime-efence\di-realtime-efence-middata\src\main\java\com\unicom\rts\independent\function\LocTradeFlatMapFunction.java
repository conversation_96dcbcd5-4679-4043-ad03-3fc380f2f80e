package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocTradeBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;

public class LocTradeFlatMapFunction implements FlatMapFunction<String, LocTradeBean> {
    @Override
    public void flatMap(String value, Collector<LocTradeBean> out) throws Exception {
        String[] cols = StringUtils.splitPreserveAllTokens(value, "\1");
        if (cols.length>=13) {
            LocTradeBean locTradeBean = new LocTradeBean();
            locTradeBean.setDeviceNumber(cols[0]);
            locTradeBean.setTime(cols[1]);
            locTradeBean.setRoamType(cols[2]);
            locTradeBean.setHrovId(cols[3]);
            locTradeBean.setHareaId(cols[4]);
            locTradeBean.setImei(cols[5]);
            locTradeBean.setImsi(cols[6]);
            locTradeBean.setLac(cols[7]);
            locTradeBean.setCi(cols[8]);
            locTradeBean.setLongitude(cols[9]);
            locTradeBean.setLatitude(cols[10]);
            locTradeBean.setProvId(cols[11]);
            locTradeBean.setPoweroffInd(cols[12]);
            out.collect(locTradeBean);
        }
    }
}
