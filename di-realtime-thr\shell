
--thr.source.broker 10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092
--thr.enable.auto.commit true
--thr.offset.reset latest
--thr.source.kafka.security false
--thr.kafka.user sscjzx-kfk-wzgj-cluster
--thr.kafka.password dGlhbkdvbmcjMTIz

--thr.group.id thr_inde_test

--thr.topic thr
--thr.source.parallelism 50

--checkpointDataUri hdfs://slfn2/user/hh_slfn2_sschj_gray/flink/checkpoints/dp-independent-thr/

--batch.size 16384
--request.timeout.ms 120000
--sink.kafka.bootstrap 10.162.235.6:9092,10.162.235.7:9092,10.162.235.8:9092
--sink.kafka.security false
--sasl.mechanism PLAIN
--security.protocol SASL_PLAINTEXT
--sasl.jaas.config org.apache.kafka.common.security.plain.PlainLoginModule
--sink.kafka.user context
--sink.kafka.password context
--sink.parallelism 30

--outPut.topic LLYJ_GZH_XKF_TEST
--kafkaConsumerStartFrom latest

--warehouse hdfs:///user/hh_slfn2_sschj/paimon
--default-database ubd_sscj_prod_flink
--paimon.options 'properties.group.id'='thr_test','scan.startup.mode'='latest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000'

--hudi.table dwa_r_hudi_user_tag
--hudi.table.path hdfs://slfn2/warehouse/tablespace/external/hive/ubd_sscj_gray_flink.db/dwa_r_hudi_user_tag



prod

--thr.source.broker 10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092
--thr.enable.auto.commit true
--thr.offset.reset latest
--thr.source.kafka.security false
--thr.kafka.user sscjzx-kfk-wzgj-cluster
--thr.kafka.password dGlhbkdvbmcjMTIz

--thr.group.id thr_inde_prod

--thr.topic thr
--thr.source.parallelism 50

--checkpointDataUri hdfs://slfn2/user/hh_slfn2_sschj/flink/checkpoints/dp-independent-thr/

--batch.size 16384
--request.timeout.ms 120000
--sink.kafka.bootstrap 10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092
--sink.kafka.security true
--sasl.mechanism PLAIN
--security.protocol SASL_PLAINTEXT
--sasl.jaas.config org.apache.kafka.common.security.plain.PlainLoginModule
--sink.kafka.user context
--sink.kafka.password context
--sink.parallelism 30

--outPut.topic LLYJ_GZH_XKF
--kafkaConsumerStartFrom latest

--warehouse hdfs:///user/hh_slfn2_sschj/paimon
--default-database ubd_sscj_prod_flink
--paimon.options 'properties.group.id'='thr_test','scan.startup.mode'='latest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000'

--hudi.table dwa_r_hudi_user_tag
--hudi.table.path hdfs://slfn2/warehouse/tablespace/external/hive/ubd_sscj_gray_flink.db/dwa_r_hudi_user_tag


/home/<USER>/TDH-Client/kafka/bin/kafka-topics.sh --zookeeper 10.162.235.6:2181,10.162.235.7:2181,10.162.235.8:2181 --create --topic LLYJ_GZH_XKF_TEST --if-not-exists --replication-factor 2 --partitions 10