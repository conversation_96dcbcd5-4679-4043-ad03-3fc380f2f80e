package com.unicom.realtime.sink;


import com.unicom.realtime.bean.FttrOrderBean;

import com.unicom.realtime.bean.Rsp;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 *
 */
public class KafkaMultiSinkSerializationSchema implements KafkaSerializationSchema<Tuple2<Rsp, FttrOrderBean>> {

    @Override
    public ProducerRecord<byte[], byte[]> serialize(Tuple2<Rsp, FttrOrderBean> value, @Nullable Long aLong) {
        List<Header> headers = new ArrayList<>();

        Header receiveTime = new RecordHeader("receive_time", value.f0.getReceiveTime().getBytes());
        headers.add(receiveTime);

        Header integrationTime = new RecordHeader("integration_time",value.f0.getIntegrationTime().getBytes());
        headers.add(integrationTime);

        //        ProducerRecord<String, String> record = new ProducerRecord<>("topic", null, "key", "value", headers);
        return new ProducerRecord(value.f0.getDestTopic(),null, value.f0.getKeyBy(), value.f1.sendString(),headers);

    }
}
