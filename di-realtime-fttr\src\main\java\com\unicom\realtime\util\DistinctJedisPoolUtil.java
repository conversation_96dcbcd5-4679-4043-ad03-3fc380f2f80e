package com.unicom.realtime.util;


import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * 国产化 redis cluster 工具类
 */
public class DistinctJedisPoolUtil {

    private final static Logger logger = LoggerFactory.getLogger(DistinctJedisPoolUtil.class);

    private static JedisCluster jedisCluster = null;
    private static JedisPoolConfig poolConfig = null;

    public static synchronized JedisCluster getJedisCluster(ParameterTool conf) {

        if (jedisCluster == null) {
            poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(10000);
            poolConfig.setMaxIdle(200);
            poolConfig.setMinIdle(10);
            poolConfig.setMaxWaitMillis(10000);
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestWhileIdle(true);
            poolConfig.setTestOnReturn(true);

            Set<HostAndPort> jedisClusterNode = new HashSet<HostAndPort>();

            String hostAndPorts = conf.get("distinct.redis.ip");

            String[] split = hostAndPorts.split(",");
            for (String s : split) {
                String[] s2 = s.split(":");
                jedisClusterNode.add(new HostAndPort(s2[0], Integer.parseInt(s2[1])));
            }
            String prefix = conf.get("distinct.redis.passwordprefix");
            String password = conf.get("distinct.redis.password");

//            logger.info("===================================conf.get(distinct.redis.passwordprefix):{}", prefix);
//            logger.info("===================================conf.get(distinct.redis.password):{}", password);
            String retpassword = "";
            if (!StringUtils.isBlank(prefix)) {
                retpassword = prefix + "#" + password;
            } else {
                retpassword = password;
            }
//            logger.info("===================================conf.get(distinct.redis.password):{}", retpassword);
            jedisCluster = new JedisCluster(jedisClusterNode, 5000, 3000, 5, retpassword, poolConfig);
        }
        return jedisCluster;
    }


    public static String get(String key) throws Exception {
        String res = null;
        try {
            // 从连接池中设置key
            res = jedisCluster.get(key);

        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return res;
    }


    public static void set(String key) throws Exception {
        try {
            // 设置key的过期时间为48小时
            jedisCluster.setex(key, 172800, "1");

        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    /**
     * <p>
     * 通过key 和 field 获取指定的 value
     * </p>
     *
     * @param key
     * @param field
     * @return 没有返回null
     */
    public static String hget(String key, String field) throws Exception {

        String res = null;
        try {
            res = jedisCluster.hget(key, field);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return res;
    }


    public static void main(String[] args) throws IOException {
        ParameterTool conf = ParameterTool.fromPropertiesFile("/Users/<USER>/Downloads/config.properties");
        JedisCluster jedisCluster = getJedisCluster(conf);

        System.out.println(jedisCluster.get("A64test_A49_3123041032719879_4_90964854_1631445188000_-1"));


    }


}
