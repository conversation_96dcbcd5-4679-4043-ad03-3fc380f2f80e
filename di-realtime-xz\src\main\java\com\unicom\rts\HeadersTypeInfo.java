package com.unicom.rts;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.kafka.common.header.Headers;

import java.util.HashMap;
import java.util.Map;

public class HeadersTypeInfo extends TypeInformation<Headers> {
    private TypeInformation headers;
    private TypeInformation isReadOnly;

    public TypeInformation getHeaders() {
        return headers;
    }

    public TypeInformation getIsReadOnly() {
        return isReadOnly;
    }


    public HeadersTypeInfo(TypeInformation headers, TypeInformation isReadOnly) {
        this.headers = headers;
        this.isReadOnly = isReadOnly;
    }

    @Override
    public boolean isBasicType() {
        return false;
    }

    @Override
    public boolean isTupleType() {
        return false;
    }

    @Override
    public int getArity() {
        return 0;
    }

    @Override
    public int getTotalFields() {
        return 0;
    }

    @Override
    public Class<Headers> getTypeClass() {
        return null;
    }

    @Override
    public boolean isKeyType() {
        return false;
    }

    @Override
    public TypeSerializer<Headers> createSerializer(ExecutionConfig config) {
        return null;
    }

    @Override
    public String toString() {
        return "";
    }


    @Override
    public boolean equals(Object obj) {
        return false;
    }

    @Override
    public int hashCode() {
        return 0;
    }

    @Override
    public boolean canEqual(Object obj) {
        return false;
    }

    @Override
    public Map<String, TypeInformation<?>> getGenericParameters() {
        Map<String, TypeInformation<?>> map = new HashMap<>(2);
        map.put("headers", headers);
        map.put("isReadOnly", isReadOnly);
        return map;
    }
}
