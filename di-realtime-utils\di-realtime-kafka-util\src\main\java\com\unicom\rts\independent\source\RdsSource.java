package com.unicom.rts.independent.source;

import java.sql.*;
import java.util.*;

public class RdsSource {

    public static final String RULESQL = " select scene_id ,scene_type,topic as id,del_flag ,create_user ,end_time from v_scene_rule ";
    public static final String CONNSQL = " select id,name,ip_code,connect_message,send_topic from t_connect tc  where tc.is_send  = 1 ";
    public static final String ALONETOPICSQL = "select topic as id, connect_name ,del_flag ,service_type  from v_topic_info;";
    /**
     * 查询rds数据方法
     * @param connection
     * @param sql
     * @return
     */
    public static Map<String,Map<String,Object>> getRdsData(Connection connection, String sql) throws Exception{
        Map<String,Map<String,Object>> rdsData = new LinkedHashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        ResultSetMetaData rsmd = null;
        try {
            stmt = connection.createStatement();
            rs = stmt.executeQuery(sql);
            rsmd = rs.getMetaData();
            int colLength = rsmd.getColumnCount();
            while(rs.next()){
                Map<String,Object> row = new HashMap<>();
                for(int i = 1 ; i<= colLength ; i++){
                    row.put(rsmd.getColumnName(i).toUpperCase(),rs.getObject(i));
                }
                rdsData.put(rs.getString("id"),row);
            }
        } catch (SQLException e) {
            throw new RuntimeException("获取rds数据异常:",e);
        } finally {
            try{
                if (rs != null){
                    rs.close();
                }
            }catch (Exception e1){
                System.out.println("Exception e1 = " + e1.getMessage());
            }
            try{
               if (stmt != null){
                   stmt.close();
               }
            }catch (Exception e2){
                System.out.println("Exception e2 = " + e2.getMessage());
            }

        }
        return rdsData;
    }
    // 获取数据库连接
    public static Connection getConnection(Properties properties){
        Connection conn = null;
        try {
            Class.forName(properties.getProperty("jdbc.driver"));
            //注意，改成配置参数 数据库地址和用户名、密码
            conn = DriverManager.getConnection(properties.getProperty("jdbc.url"),
                    properties.getProperty("username"), properties.getProperty("password"));
        } catch (Exception e) {
            throw new RuntimeException("-----------mysql get connection has exception , msg = " + e);
        }
        return conn;
    }
    public static void release(Connection connection){
        if (connection != null){
            try {
                connection.close();
            } catch (SQLException e) {
                throw new RuntimeException("close connectiont exception",e);
            }
        }
    }
}
