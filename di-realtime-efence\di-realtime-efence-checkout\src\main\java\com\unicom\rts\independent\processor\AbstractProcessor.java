package com.unicom.rts.independent.processor;

import com.unicom.rts.independent.bean.RuleBean;
import com.unicom.rts.independent.beans.RecordBean;
import com.unicom.rts.independent.enums.TagEnum;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.HTable;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/
public abstract class AbstractProcessor implements Processor {
    protected RecordBean recordBean;
    protected RuleBean ruleBean;
    protected HTable hTableRtsUserTag;
    protected String userTagColumnFamily;
    protected MapState<String, String> checkoutState;
    protected Collector<Tuple2<String, String>> out;
    protected HashMap<String,String> provMap;
    protected HashMap<String,String> areaMap;

    @Override
    public void process(RecordBean recordBean, RuleBean ruleBean, HTable hTableRtsUserTag, String userTagColumnFamily,
                        MapState<String, String> checkoutState, HashMap<String,String> provMap, HashMap<String,String> areaMap,
                        Collector<Tuple2<String, String>> out) throws Exception {
        this.recordBean = recordBean;
        this.ruleBean = ruleBean;
        this.hTableRtsUserTag = hTableRtsUserTag;
        this.userTagColumnFamily = userTagColumnFamily;
        this.checkoutState = checkoutState;
        this.out = out;
        this.provMap = provMap;
        this.areaMap = areaMap;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //重复检出校验
        //if (!sdf.format(new Date()).equals(checkoutState.get(recordBean.getDeviceNum()))) {
        //
        //}
        //查询hbase的标签表，补全相关信息
        //入网时长
        String stayTime = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.STAYTIME.getCode());
        if (StringUtils.isEmpty(stayTime) || "\\N".equals(stayTime)) {
            stayTime = "0";
        }
        recordBean.setStayTime(stayTime);
        //是否靓号
        recordBean.setPrettyNumber(
                HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                        recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRETTYNUMBER.getCode()));

        String hrovId = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.HROVID.getCode());

        String hareaId = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.HAREAID.getCode());

        String[] str = recordBean.getRecord().split("\\u0001",-1);

        //如果从hbase查得的归属省份和归属地市不为空，则替换topic数据中的值
        if (!"null".equals(hrovId) && !"".equals(hrovId) && !"\\N".equals(hrovId)
                && !"null".equals(hareaId) && !"".equals(hareaId) && !"\\N".equals(hareaId)) {
            //str[2] = hrovId;
            //str[3] = hareaId;
            str[2] = provMap.get(hrovId);
            str[3] = areaMap.get(hareaId);
        }
        recordBean.setRecord(String.join("\1",str));

        this.subProcess();
    }

    public abstract void subProcess() throws Exception;
}
