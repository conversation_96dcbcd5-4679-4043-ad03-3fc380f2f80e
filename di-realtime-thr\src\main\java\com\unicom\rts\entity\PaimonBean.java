package com.unicom.rts.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Data
public class PaimonBean {

    String dtsCollectTime;
    String dtsKafTime;
    String dtsKafOffset;
    String dtsKafPart;
    String operateType;
    String operateTime;
    String operateTypeDesc;
    String id;
    String phoneNum;
    String isDelete;
    String createUser;
    String createName;
    String createTime;
    String paimonTime;

//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        PayInBean payBean = (PayInBean) o;
//        return psptTypeCode.equals(payBean.psptTypeCode) && psptId.equals(payBean.psptId) && userId.equals(payBean.userId);
//    }

//    @Override
//    public int hashCode() {
//        return Objects.hash(psptTypeCode, psptId, userId);
//    }
}
