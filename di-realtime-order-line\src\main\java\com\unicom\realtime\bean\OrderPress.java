package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderPress {
    private String orderId;
    private String orderLineId;
    private String updateTime;
    private String orderState;
    private String tradeId;
    private String subscribeId;
    private String opt;
    private String opttime;
    private String cdhtime;
    private String dataReceiveTime;

    @Override
    public String toString() {
        return (orderId != null ? orderId : "") + "\u0001" +
                (orderLineId != null ? orderLineId : "") + "\u0001" +
                (updateTime != null ? updateTime : "") + "\u0001" +
                (orderState != null ? orderState : "") + "\u0001" +
                (tradeId != null ? tradeId : "") + "\u0001" +
                (subscribeId != null ? subscribeId : "") + "\u0001" +
                (opt != null ? opt : "") + "\u0001" +
                (dataReceiveTime != null ? dataReceiveTime : "") + "\u0001" +
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }
}
