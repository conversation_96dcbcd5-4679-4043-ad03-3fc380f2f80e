package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.*;
import com.unicom.rts.independent.util.DistanceUtil;
import com.unicom.rts.independent.util.HbaseUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 */
public class LacciJoinFlatMap extends RichFlatMapFunction<LocData, LocData> {
    private final static Logger logger = LoggerFactory.getLogger(TagJoinFlatMap.class);
    private ValueState<LacciInfo> lacciState;
    ParameterTool conf;
    MapState<String, LocStateBean> locState;
    String topic;
    private final transient long valueToExpose = 0L;
    int locDataSecondDiff = 0;

    public LacciJoinFlatMap(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        //时延统计metric
        getRuntimeContext()
                .getMetricGroup()
                .gauge("proDelay", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return valueToExpose;
                    }
                });


        ValueStateDescriptor<LacciInfo> lacciStateDes = new ValueStateDescriptor<>("lacciState", LacciInfo.class);
        lacciState = getRuntimeContext().getState(lacciStateDes);
    }


    @Override
    public void flatMap(LocData locData, Collector<LocData> collector) throws Exception {
        try {
            if ("lacci".equals(locData.getDatasource())) {
                lacciState.update(locData.getLacciInfo());
                return;
            }
            // logger.info("locData >>>>>>>>>>>>> " + locData.toString());
            LacciInfo lacciInfo = lacciState.value();

            String deviceNumber = locData.getDeviceNumber();

            double lon = 0L;
            double lat = 0L;
            if (StringUtils.isNotBlank(locData.getLat()) && StringUtils.isNotBlank(locData.getLon())) {
                // lon = Double.parseDouble(locData.getLon());
                // lat = Double.parseDouble(locData.getLat());
                collector.collect(locData);
            } else if (lacciInfo != null && StringUtils.isNotBlank(lacciInfo.getLat()) && StringUtils.isNotBlank(lacciInfo.getLon())){
                // lon = Double.parseDouble(lacciInfo.getLon());
                // lat = Double.parseDouble(lacciInfo.getLat());
                locData.setLon(lacciInfo.getLon());
                locData.setLat(lacciInfo.getLat());
                collector.collect(locData);
            } else {
                return;
            }
        } catch (Exception e) {
            logger.error(e.toString());
            // throwables.printStackTrace();
            logger.error(locData.toString());
        }
    }
}
