package com.unicom.rts.independent.processor.processorFactory;

import com.unicom.rts.independent.processor.Processor;
import com.unicom.rts.independent.processor.processorImpl.*;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/
public class ProcessorFactory {

    private static Map<String, Processor> provProcessorMap = new HashMap<>();

    static {
        register("1",new Processor1());
        register("2",new Processor2());
        register("3",new Processor3());
        register("4",new Processor4());
        register("5",new Processor5());
    }

    public static void register(String inputTopic, Processor processor){
        if(StringUtils.isNotBlank(inputTopic)){
            provProcessorMap.put(inputTopic, processor);
        }
    }

    public static Processor getProcessor(String inputTopic){
        return provProcessorMap.get(inputTopic);
    }
}
