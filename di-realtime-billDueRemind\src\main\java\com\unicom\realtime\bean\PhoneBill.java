package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PhoneBill {
    private String id;
    private String userNumber;
    private String rightsType;
    private String state;
    private String rightsPrice;
    private String endTime;

    @Override
    public String toString() {
        return "{" +
                "\"serial_number\":\"" + userNumber + '\"' +
                ",\"COUPON_AMOUNT\":\"" + rightsPrice + '\"' +
                ",\"COUPON_FINISH_TIME\":\"" + endTime + '\"' +
                "}";
    }
}
