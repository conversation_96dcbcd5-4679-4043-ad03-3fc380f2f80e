package com.unicom.realtime.source;

import com.unicom.realtime.bean.UnionBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class UserSource {

    public DataStream<UnionBean> build(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(TF_F_USER + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, UnionBean>) (row, collector) -> {
            UnionBean unionBean = new UnionBean();
            unionBean.setSerialNumber((String)row.getField("SERIAL_NUMBER"));
            unionBean.setUserId((String)row.getField("USER_ID"));
            unionBean.setCustId((String)row.getField("CUST_ID"));
            unionBean.setDepartId((String)row.getField("DEVELOP_DEPART_ID"));
            unionBean.setOpt((String)row.getField("OPT"));
            unionBean.setTableName("TF_F_USER");
            unionBean.setRowKind(row.getKind());
            collector.collect(unionBean);
        }).returns(UnionBean.class).uid("UserFlatMap").name("UserFlatMap")
                .setParallelism(conf.getInt("UserFlatMap.parallelism", env.getParallelism()));
    }

    private static final String TF_F_USER = "SELECT\n" +
            "    OPT,\n" +
            "    USER_ID USER_ID,\n" +
            "    CUST_ID CUST_ID,\n" +
            "    SERIAL_NUMBER SERIAL_NUMBER,\n" +
            "    DEVELOP_DEPART_ID DEVELOP_DEPART_ID\n" +
            "    FROM ods_r_paimon_tf_f_user";
}
