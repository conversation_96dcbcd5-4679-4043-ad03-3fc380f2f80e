package com.unicom.rts.function;

import com.unicom.rts.bean.PayInBean;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RowToPayBean implements FlatMapFunction<Row, PayInBean> {
    private final static Logger logger = LoggerFactory.getLogger(RowToPayBean.class);

    @Override
    public void flatMap(Row row, Collector<PayInBean> out) throws Exception {
        try {
            PayInBean pay = new PayInBean();
            pay.setDtsCollectTime(String.valueOf(row.getField("dts_collect_time")));
            pay.setDtsKafTime(String.valueOf(row.getField("dts_kaf_time")));
            pay.setDtsKafOffset(String.valueOf(row.getField("dts_kaf_offset")));
            pay.setDtsKafPart(String.valueOf(row.getField("dts_kaf_part")));
            pay.setOperateType(String.valueOf(row.getField("operate_type")));
            pay.setOperateTime(String.valueOf(row.getField("operate_time")));
            pay.setOperateTypeDesc(String.valueOf(row.getField("operate_type_desc")));
            pay.setId(String.valueOf(row.getField("Id")));
            pay.setPhoneNum(String.valueOf(row.getField("phone_num")));
            pay.setIsDelete(String.valueOf(row.getField("is_delete")));
            pay.setCreateUser(String.valueOf(row.getField("create_user")));
            pay.setCreateName(String.valueOf(row.getField("create_name")));
            pay.setCreateTime(String.valueOf(row.getField("create_time")));
            pay.setPaimonTime(String.valueOf(row.getField("PAIMON_TIME")));

            out.collect(pay);
        } catch (Exception e) {
            logger.error("RowToNpBean data:{} Exception:{}", row, e);
        }
    }

}
