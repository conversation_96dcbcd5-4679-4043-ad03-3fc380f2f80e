package com.unicom.rts.independent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/19
 **/

@AllArgsConstructor
@Getter
public enum TagEnum {

    /*
    hbase标签枚举信息
     */
    STAYTIME("off_k000004", "在网时长"),
    PRETTYNUMBER("off_k002169", "是否靓号"),
    HROVID("off_k000046","归属省份"),
    HAREAID("off_k003681", "归属地市"),
    PRODUCTNAME("off_k002996", "产品名称"),
    PRODUCTCODE("off_k002668", "产品ID"),
    INNETTIME("off_k003589", "入网时间");

    private final String code;

    private final String msg;

}
