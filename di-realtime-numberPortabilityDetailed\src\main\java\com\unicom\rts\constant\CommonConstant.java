package com.unicom.rts.constant;

public class CommonConstant {

    public final static String SEND_ID = "send_id";

    public final static String SOURCE_ID = "source_id";
    public final static String GROUP_ID = "group.id";

    public final static String SINK_PARALLELISM = "sinkPara";

    public final static String SOURCE_PARALLELISM = "soucePara";

    public final static String PROCESS_PARALLELISM = "processParallelism";

    public final static String MYSQL_URL = "MYSQL_URL";
    public final static String MYSQL_USER = "MYSQL_USER";
    public final static String MYSQL_PASSWD = "MYSQL_PASSWD";
    public final static String CONSUMER_SQL = "SELECT " +
            "  c.name, " +
            "  c.IP_CODE, " +
            "  c.CONNECT_TYPE," +
            "  c.CONNECT_MESSAGE," +
            "  d.topic " +
            "FROM" +
            "  t_datasource d," +
            "  t_connect c " +
            "WHERE d.CONNECT_ID = c.id " +
            "  AND d.id = \'#DatasourceId\'";


    public final static String SEND_SQL = "SELECT " +
            "  c.name, " +
            "  c.IP_CODE, " +
            "  c.CONNECT_TYPE," +
            "  c.CONNECT_MESSAGE" +
            "  FROM" +
            "  t_connect c " +
            "WHERE c.id = \'#ConnectId\' " ;

    public final static String HBASE_SQL = "SELECT ID,NAME,IP_CODE,CONNECT_TYPE,CONNECT_MESSAGE,CREATE_USER,CREATE_DATE,UPDATE_USER,UPDATE_DATE,DEL_FLAG from t_connect where name = \'#NAME\'";

}
