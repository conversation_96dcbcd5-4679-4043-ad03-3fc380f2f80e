#!/bin/bash

function alarm_new () {
  curl -H "Content-Type:application/json" -X POST -d '{"UNI_BSS_HEAD":{"APP_ID":"6YEacacCkA","TIMESTAMP":"2016-04-12 15:06:06 100","TRANS_ID":"20160412150606100335423","TOKEN":"9841fa54c0f2aedfee9ba8bf89cd9f6d"},"UNI
_BSS_BODY":{"MSGFORWARDING_REQ":{"system":"SJZTSSCJZX","module":"SJZTSSCJZX","level":"4","target_scope":"3","send_channel":["1","2"],"monitor_name":"实时场景告警验证","monitor_message":"'"${monitor_message}"'","licen
se":"/EzRCMRNkmWD7X0pOslCPNyOn9acjr8kyjzABsRknIY=","receivers":["<EMAIL>","<EMAIL>","<EMAIL>"],"labels"
:{"host":""}}},"UNI_BSS_ATTACHED":{"MEDIA_INFO":""}}' 'http://**************:8000/api/tianyan/outer/msgforwarding/v1'
}
#获取当前时间
echo $(date +'%Y-%m-%d %H:%M:%S')

scriptHome_path=$(cd $(dirname $0);pwd)  #/data/disk01/hh_slfn2_sschj/shell/Monitoringscript/script_folder/
echo "=================================================================="${scriptHome_path} >> ${scriptHome_path}/curl.log

file_name="DIM_D_SQZ_WGGM_WIFI_CODE_$(date -d "-1 day" +'%Y%m%d')"
echo $(date +'%Y-%m-%d %H:%M:%S') >> ${scriptHome_path}/curl.log
echo ${file_name}  >> ${scriptHome_path}/curl.log
#用于初始化，所有的连接mysql数据库的函数
function sql_connect_info(){
        #mysql数据库连接信息
        mysql_path="/data/disk01/hh_slfn2_sschj_gray/shell/Monitoringscript/mysql_client/bin/"
        sql_host="*************"
        sql_user="gray_rts_cop"
        sql_pd="GrayRTS@2022"
        sql_port="3306"
        sql_db="rts_cop_gray"
        #local sel_user_id=2
}
function sql_connect_info(){
        #mysql数据库连接信息
        mysql_path="/data/disk01/hh_slfn2_sschj_gray/shell/Monitoringscript/mysql_client/bin/"
        sql_host="*************"
        sql_user="gray_rts_cop"
        sql_pd="GrayRTS@2022"
        sql_port="3306"
        sql_db="rts_cop_gray"
        #local sel_user_id=2
}
function sftp_connect_info(){
        Port="22"
        User="hh_per_prod_013"
        Host="************"
        Pass="tT@g_g5f"
}
#Are you sure you want to continue connecting (yes/no/[fingerprint])?
function get_file(){
echo "==========开始获取文件========================================================" >> ${scriptHome_path}/curl.log
echo "==========sftp连接=========Connecting to ...============================="${Host} >> ${scriptHome_path}/curl.log
expect <<EOF
spawn sftp -P 22 ${User}@${Host}
expect {
        "*continue connecting (yes/no*"
        {send "yes\r";exp_continue}
        "*Password:"
        {send "tT@g_g5f\r"}
}
expect "sftp>"
send "cd /data/disk02/hh_per_prod_013/hh_per_prod_013_wg/TOOUT/ZB_realtime\n"
expect "sftp>"
send "get ${file_name} ${scriptHome_path}\n"
expect "sftp>"
send "exit\n"
EOF
echo "==========结束获取文件========================================================" >> ${scriptHome_path}/curl.log

rowCnt=$(wc -l <${scriptHome_path}/${file_name})
echo ${rowCnt}  >> ${scriptHome_path}/curl.log

}

function read_file(){
echo "==========开始读取=========================================================" >> ${scriptHome_path}/curl.log
rowCnt=$(wc -l <${scriptHome_path}/${file_name})

echo ${rowCnt}  >> ${scriptHome_path}/curl.log
echo "=================================================================="${rowCnt} >> ${scriptHome_path}/curl.log

 if (( $rowCnt < 1 )); then
     echo "==========文件行数为0========================================================" >> ${scriptHome_path}/curl.log
     monitor_message='Unable to retrieve the correct file, please check the SFTP server!'
	 alarm_new
    return
 fi
echo "=================================================================="${mysql_path} >> ${scriptHome_path}/curl.log
echo "=================================================================="${sql_host} >> ${scriptHome_path}/curl.log

sql_all="";
        for line in $(cat ${scriptHome_path}/${file_name}|tr -d ' ')
        do
          echo $line >> ${scriptHome_path}/curl.log
          IFS="|" read -ra array <<< "$line"
          sql_insert="insert into t_fttr_code (PRODUCT_ID, PRODUCT_NAME, FJ_NUM, SVC_TYPE, FLAG, FLAG2, IN_TIME) values('${array[0]}','${array[1]}','${array[2]}','${array[3]}','${array[4]}','${array[5]}',now());"
sql_all=${sql_insert}${sql_all}
       done
  echo "==================================================================="${sql_all} >> ${scriptHome_path}/curl.log
${mysql_path}mysql -h${sql_host} -u${sql_user} -p${sql_pd} -P${sql_port} --default-character-set=utf8 ${sql_db} <<EOF
TRUNCATE TABLE t_fttr_code;
${sql_all}
EOF
sql_count="select count(*) from t_fttr_code;"
sql_num=($(${mysql_path}mysql -h${sql_host} -u${sql_user} -p${sql_pd} -P${sql_port} --default-character-set=utf8 ${sql_db}  -e "${sql_count}"))
echo "========================数据库中数据条数==========================${sql_num[1]}" >> ${scriptHome_path}/curl.log
if (($rowCnt == ${sql_num[1]})); then
   echo "==========数据库和文件中数据条数相等=========================================================" >> ${scriptHome_path}/curl.log
else
   monitor_message='Unequal number of data entries in database and file'
   alarm_new
   echo "==========数据库和文件中数据条数不相等，已发送告警=========================================================" >> ${scriptHome_path}/curl.log
fi
echo "==========结束读取=========================================================" >> ${scriptHome_path}/curl.log
}


sql_connect_info
sftp_connect_info
get_file
read_file