package com.unicom.realtime.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.unicom.realtime.entity.ZjData;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSONException;


public class ZjFlatMap extends RichFlatMapFunction<ConsumerRecord<String, String>, ZjData> {

    private final static Logger logger = LoggerFactory.getLogger(ZjFlatMap.class);
    private Gson gson;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        gson = new Gson();
    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record, Collector<ZjData> collector) throws Exception {
        try {
            // 新增空值校验
            if (record == null || record.value() == null || record.value().trim().isEmpty()) {
                logger.warn("收到空消息记录，已过滤");
                return;
            }

            // 新增JSON格式校验
            if (!isValidJson(record.value())) {
                logger.warn("非法JSON格式数据：{}", record.value());
                return;
            }

//            logger.info("===record===" + record.value());
            JSONObject jsonObject = JSON.parseObject(record.value());

//        Object productIn = jsonObject.get("PRODUCT_IN");
//        logger.info("===productIn===" + JSON.toJSONString(productIn));
//        if (productIn != null) {
//            ZjData zjData = new ZjData();
//            zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
//            zjData.setIsContractUser(String.valueOf(jsonObject.get("IS_CONTRACT_USER")));
//            zjData.setMainProductMonthlyFee(String.valueOf(jsonObject.get("MAIN_PRODUCT_MONTHLY_FEE")));
//            zjData.setProductIn(String.valueOf(jsonObject.get("PRODUCT_IN")));
//            zjData.setProductIs2i2c(String.valueOf(jsonObject.get("PRODUCT_IS_2I2C")));
//            zjData.setRProductIn(String.valueOf(jsonObject.get("R_PRODUCT_IN")));
//            zjData.setRProductIs2i2c(String.valueOf(jsonObject.get("R_PRODUCT_IS_2I2C")));
//            zjData.setValidMainProduct(String.valueOf(jsonObject.get("VALID_MAIN_PRODUCT")));
//            logger.info("===ZjData===" + zjData.toString());
//
//            collector.collect(zjData);
//        }

            //云盘标签
            Object level = jsonObject.get("level");
            if (level != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("phone")));
                zjData.setLevel(String.valueOf(jsonObject.get("level")));
//            logger.info("===ZjData=level==" + zjData.toString());
                collector.collect(zjData);
            }
            Object pickUpCount = jsonObject.get("pickUpCount");
            if (pickUpCount != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(getStringSafe(jsonObject, "phone"));
                zjData.setInterceptCount(getStringSafe(jsonObject, "interceptCount"));
                zjData.setPickUpCount(getStringSafe(jsonObject, "pickUpCount"));
                zjData.setOrderStatus(getStringSafe(jsonObject, "orderStatus"));
                zjData.setExpireRemainingDays(getStringSafe(jsonObject, "expireRemainingDays"));
                zjData.setCanBindMemberCount(getStringSafe(jsonObject, "canBindMemberCount"));
                zjData.setAlreadyBindMemberCount(getStringSafe(jsonObject, "alreadyBindMemberCount"));
                zjData.setBindDevice(getStringSafe(jsonObject, "bindDevice"));
//            logger.info("===ZjData=pickUpCount==" + zjData.toString());
                collector.collect(zjData);
            }
                //联通APP登录标签
                Object labelStLoginTimeReal = jsonObject.get("LABEL_ST_LOGIN_TIME_REAL");
                if (labelStLoginTimeReal != null) {
                    ZjData zjData = new ZjData();
                    zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                    zjData.setLabelStLoginTimeReal(String.valueOf(jsonObject.get("LABEL_ST_LOGIN_TIME_REAL")));
//            logger.info("===ZjData=level==" + zjData.toString());
                collector.collect(zjData);
            }

            //CB用户数据流策
            Object innetDate = jsonObject.get("innet_date");
            if (innetDate != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                zjData.setInnetDate(String.valueOf(jsonObject.get("innet_date")));
                zjData.setProductId(String.valueOf(jsonObject.get("product_id")));
                zjData.setCityCode(String.valueOf(jsonObject.get("city_code")));
                zjData.setProvinceCode(String.valueOf(jsonObject.get("province_code")));
                collector.collect(zjData);
            }


            //CB最近一次接收到CB达量封顶短信消息的时间
            Object lackBalanceTime = jsonObject.get("LACK_BALANCE_TIME");
            if (lackBalanceTime != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                zjData.setLackBalanceTime(String.valueOf(jsonObject.get("LACK_BALANCE_TIME")));
                collector.collect(zjData);
            }


            //CB最近一次接收到CB达量封顶短信消息的时间
            Object trafficCapsTime = jsonObject.get("TRAFFIC_CAPS_TIME");
            if (trafficCapsTime != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                zjData.setTrafficCapsTime(String.valueOf(jsonObject.get("TRAFFIC_CAPS_TIME")));
                collector.collect(zjData);
            }

            //小余额标签
            Object BROADBAND_NOTINCE_TYPE = jsonObject.get("BROADBAND_NOTINCE_TYPE");
            if (BROADBAND_NOTINCE_TYPE != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(getStringSafe(jsonObject, "serial_number"));
                // 处理空值和"null"字符串的情况
                String noticeType = getStringSafe(jsonObject, "BROADBAND_NOTINCE_TYPE");
                if("null".equalsIgnoreCase(noticeType)){
                    noticeType = "";
                }
                zjData.setBROADBAND_NOTINCE_TYPE(noticeType);

                String noticeTime = getStringSafe(jsonObject, "BROADBAND_NOTINCE_TIME");
                if("null".equalsIgnoreCase(noticeTime)){
                    noticeTime = "";
                }
                zjData.setBROADBAND_NOTINCE_TIME(noticeTime);

                collector.collect(zjData);
            }
             //权益标签
            Object act_ids = jsonObject.get("act_ids");
            if (act_ids != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("phone_number")));
                zjData.setAct_ids(String.valueOf(jsonObject.get("act_ids")));
//            logger.info("===ZjData=level==" + zjData.toString());
                collector.collect(zjData);
            }

            //待复机信安标签
            Object resumptionPhone = jsonObject.get("resumptionStatus");
            if (resumptionPhone != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("resumptionPhone")));
                zjData.setResumptionStatus(String.valueOf(jsonObject.get("resumptionStatus")));
                collector.collect(zjData);
            }

            Object TELESCORE = jsonObject.get("TELESCORE");
            if (TELESCORE != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                zjData.setTELESCORE(String.valueOf(jsonObject.get("TELESCORE")));
                zjData.setAWARDSCORE(String.valueOf(jsonObject.get("AWARDSCORE")));
                zjData.setTOTELSCORE(String.valueOf(jsonObject.get("TOTELSCORE")));
//            logger.info("===ZjData=level==" + zjData.toString());
                collector.collect(zjData);
            }

            Object IS_ARREARAGE = jsonObject.get("IS_ARREARAGE");
            if (IS_ARREARAGE != null) {
                ZjData zjData = new ZjData();
                zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
                zjData.setIS_ARREARAGE(String.valueOf(jsonObject.get("IS_ARREARAGE")));
                collector.collect(zjData);
            }

        } catch (JSONException e) {
            logger.error("JSON解析异常，数据内容：{}", record.value(), e);
        } catch (Exception e) {
            logger.error("数据处理异常", e);
        }
    }

    private String getStringSafe(JSONObject json, String key) {
        Object value = json.get(key);
        return value != null ? String.valueOf(value) : "";
    }

    // 新增JSON有效性校验方法
    private boolean isValidJson(String jsonStr) {
        try {
            JSON.parseObject(jsonStr);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
}
