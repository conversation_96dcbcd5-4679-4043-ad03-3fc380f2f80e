package com.unicom.rts.function;

import com.unicom.rts.bean.SourceData;
import com.unicom.rts.enums.ProvEnum;
import com.unicom.rts.utils.HbaseUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class DataFlatMap extends RichFlatMapFunction<ConsumerRecord<String, String>, SourceData> {

    private final static Logger logger = LoggerFactory.getLogger(DataFlatMap.class);
    ParameterTool conf;
    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private String channel;
    private String busiType;
    private String intentText;
    private String intentText13;
    public DataFlatMap(ParameterTool conf) {
        this.conf = conf;
    }
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.tag")));
        channel = conf.get("channel" ,"SSCJ");
        busiType = conf.get("busiType","4016");
        intentText = conf.get("intentText","咨询宽带安装办理方法");
        intentText13 = conf.get("intentText13","咨询宽带和智慧沃家套餐");
    }
    @Override
    public void flatMap(ConsumerRecord<String, String> record, Collector<SourceData> collector) {
        try {

            String[] dataAry = StringUtils.splitPreserveAllTokens(record.value(), "\u0001");
            if (dataAry.length >= 13) {
                String provId = dataAry[12];
                String intentName = dataAry[8];
                String query = dataAry[3];
                String reply = dataAry[4];
                String phone = dataAry[1];
                String sessionId = dataAry[0];
                if (StringUtils.isEmpty(intentName) || sessionId.isEmpty()
                        || StringUtils.isEmpty(phone) || StringUtils.isEmpty(provId)
                        || !(StringUtils.isNotEmpty(query) && query.contains("宽带") ) ) {//
                    return;
                }
//                logger.info("原始数据:{}",record.value());
                provId = "0" + provId;// 这里不补0,场景的时候补0
                if ("013".equals(provId)) {
                    if (!intentText13.equals(intentName)) {
                        return;
                    }
                } else {
                    if (!intentText.equals(intentName)) {
                        return;
                    }
                }
                // 根据手机号查标签
                SourceData data = new SourceData();
                Map<String, String> label = getHbaseInfo(phone);
                String netId = null;
                String busiId = null;
                String cityCode = null;
                if (label != null && "0,1,3,8,Y,W,S,F,N".contains(label.get("rt_b_remove_tag"))) { // 本网
                    String net = label.get("rt_b_net_type_code");
                    // 本网号码 网别不是30,33,50 或者 归属省和数据中的省份不一致,舍弃该条数据
                    if (!"|30|33|50|".contains(net) || !provId.equals(label.get("rt_b_province_code"))) {
                        return;
                    }
                    netId = "1";
                    if ("30".equals(net)) {
                        //本网固话
                        busiId = "2";
                    } else if ("33".equals(net) || "50".equals(net)) {
                        //本网移网
                        busiId = "1";
                    }
                    // 省份地市
                    cityCode = label.get("rt_b_eparchy_code");
                } else {
                    if (phone.startsWith("1") && phone.length() > 11) {//异网非异网和固话
                        return;
                    }
                    netId = "2";
                    if (phone.startsWith("1") && phone.length() == 11) {
                        // 异网移网
                        busiId = "1";
                    } else {
                        // 异网固话
                        busiId = "2";
                    }
                    // 异网号码,取省会地市编码
                    if (ProvEnum.getByMsg(provId) != null) {
                        String code = ProvEnum.getByMsg(provId).getCode();
                        if (code != null && !code.isEmpty()) {
                            cityCode = String.valueOf(ProvEnum.getByMsg(provId).getCode());
                        }
                    }
                }
                if (busiId == null || netId == null || cityCode == null) {
                    return;
                }
                data.setTelephoneNo(phone);
                data.setSessionId(sessionId);
                data.setIntentName(intentName);
                data.setNetType(netId);
                data.setProCode(provId);
                data.setReply(reply);
                data.setReplyTime(dataAry[7]);
                data.setOrderChannel(channel);
                data.setCityCode(cityCode);
                data.setBusinessType(busiType);
                data.setPhoneType(busiId);
                collector.collect(data);
            } else {
                //logger.error("数据长度有误：{}", record.value());
            }
        }catch (Exception e){
            logger.error("程序处理异常:{}",e.getMessage());
        }
    }
    public Map<String,String> getHbaseInfo(String rowkey){
        Get get = new Get(HbaseUtil.getRowKey(rowkey));
        get.addColumn(Bytes.toBytes("f"), Bytes.toBytes("rt_b_remove_tag"));
        get.addColumn(Bytes.toBytes("f"), Bytes.toBytes("rt_b_net_type_code"));
        get.addColumn(Bytes.toBytes("f"), Bytes.toBytes("rt_b_province_code"));
        get.addColumn(Bytes.toBytes("f"), Bytes.toBytes("rt_b_eparchy_code"));
        Result result = null;
        Map<String,String> label = null;
        try {
            result = table.get(get);
            if(!result.isEmpty()){
                label = new HashMap<>();
                for (Cell ce : result.rawCells()) {
                    String colValue = new String(CellUtil.cloneValue(ce), StandardCharsets.UTF_8);
                    String colName = new String(CellUtil.cloneQualifier(ce), StandardCharsets.UTF_8);
                    label.put(colName,colValue);
                }
            }
        } catch (Exception e) {
            logger.error("查询标签报错：{}",e.getMessage());
        }
        return label;
    }
}
