package com.unicom.realtime.bean;

import lombok.Data;

@Data
public class TfBhTradeBean {

    private String tradeId;
    private String subscribeId;
    private String bpmId;
    private String tradeTypeCode;
    private String inModeCode;
    private String eInModeCode;
    private String priority;
    private String subscribeState;
    private String nextDealTag;
    private String productId;
    private String brandCode;
    private String userId;
    private String custId;
    private String usecustId;
    private String acctId;
    private String userDiffCode;
    private String netTypeCode;
    private String serialNumber;
    private String custName;
    private String acceptDate;
    private String acceptMonth;
    private String tradeStaffId;
    private String tradeDepartId;
    private String tradeCityCode;
    private String tradeEparchyCode;
    private String termIp;
    private String eparchyCode;
    private String cityCode;
    private String olcomTag;
    private String execTime;
    private String finishDate;
    private String operFee;
    private String foregift;
    private String advancePay;
    private String invoiceNo;
    private String feeState;
    private String feeTime;
    private String feeStaffId;
    private String cancelTag;
    private String cancelDate;
    private String cancelStaffId;
    private String cancelDepartId;
    private String cancelCityCode;
    private String cancelEparchyCode;
    private String checkTypeCode;
    private String chkTag;
    private String auditTag;
    private String channelPolicy;
    private String auditBatchNo;
    private String actorName;
    private String actorCerttypeid;
    private String actorPhone;
    private String actorCertnum;
    private String contact;
    private String contactPhone;
    private String contactAddress;
    private String actorAddress;
    private String oldActorName;
    private String oldActorCerttypeid;
    private String oldActorPhone;
    private String oldActorCertnum;
    private String oldActorAddress;
    private String remark;
    private String ifMaintenance;
    private String orderId;
    private String subOrderId;
    private String mainDiscntCode;
    private String productSpec;
    private String standardKindCode;
    private String provinceCode;
    private String deleteTag;
    private String userGroupType;
    private String grantStaffId;
    private String grantDepartId;
    private String grantCityCode;
    private String grantEparchyCode;
    private String ownnerStaffId;
    private String ownnerDepartId;
    private String ownnerCityCode;
    private String ownnerEparchyCode;
    private String actPHkmt;
    private String oldActPHkmt;
    private String rsrvStr1;
    private String rsrvStr2;
    private String rsrvStr3;
    private String opt;
    private String optTime;
    private String inTime;
    private String dataSource;
    private String cdhtime;
    private String databaseTag;

}
