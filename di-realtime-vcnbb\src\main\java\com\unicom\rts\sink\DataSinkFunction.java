package com.unicom.rts.sink;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.*;
import com.unicom.rts.utils.TokenUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataSinkFunction extends RichSinkFunction<SourceData> {

    private final static Logger logger = LoggerFactory.getLogger(DataSinkFunction.class);

    private final static String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss SSS";
    private final static String TRANSID_FORMAT = "yyyyMMddHHmmssSSS";

    ParameterTool conf;
    private String appId;
    private String appSecret;
    private String url;

    public DataSinkFunction(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        appId = conf.get("api.cellAppId");
        appSecret = conf.get("api.cellAppSecret");
        url = conf.get("api.url");
    }

    @Override
    public void invoke(SourceData value, Context context) throws Exception {
        String response = null;
        JSONObject ubody = null;
        JSONObject rsp = null;
        int reTry = 0;
        boolean flag = false;
        while ( reTry < 3 && flag == false) {
            try {
                reTry++;
                response = sinkKefu(value);
                if(response != null && !"".equals(response)) {
                    JSONObject result = JSONObject.parseObject(response);
                    if (result.getJSONObject("UNI_BSS_BODY") != null && !result.getJSONObject("UNI_BSS_BODY").isEmpty()) {
                        ubody = result.getJSONObject("UNI_BSS_BODY");
                        rsp = ubody.getJSONObject("SEND_BOOKING_ORDER_FOR_IVR_RSP");
                    }
                }
                if(rsp != null && ("400".equals(rsp.getString("CODE")) || "200".equals(rsp.getString("CODE")) )){
                    flag = true;
                }
            } catch (Exception e) {
                logger.error("调能力平台失败,次数:{},请求数据:{},异常:{}",reTry,value,e.getMessage());
            }
        }
    }
    public String sinkKefu(SourceData value) throws Exception{
        // 生成transId;
        long now = System.currentTimeMillis();
        String timeStr = DateFormatUtils.format(now, DATETIME_FORMAT);
        String random = String.valueOf((int)(Math.random()*900)+100);
        String transId = DateFormatUtils.format(now, TRANSID_FORMAT) + "060"+random;
        // 生成token
        String token = TokenUtil.generateToken(appId,appSecret,timeStr,transId);
        RequestHeader header = RequestHeader.builder().timestamp(timeStr).token(token).appId(appId).transId(transId).build();
        RequestAttanch ath = RequestAttanch.builder().mediaInfo("").build();
        RequestParams params = RequestParams.builder()
                .phoneNum(value.getTelephoneNo())
                .cityCode(value.getCityCode())
                .businessType(value.getBusinessType())
                .callTime(value.getReplyTime())
                .netType(value.getNetType())
                .orderChannel(value.getOrderChannel())
                .phoneType(value.getPhoneType())
                .proCode(value.getProCode().substring(1))
                .touchId(value.getSessionId())
                .routeValue("")
                .build();
        RequestBody body = RequestBody.builder().params(params).build();
        RequestData rData = RequestData.builder().header(header).body(body).ath(ath).build();
        // 发起请求
        String response = HttpRequest.post(url)
                .header(cn.hutool.http.Header.ACCEPT_ENCODING,"")
                .header(cn.hutool.http.Header.CONTENT_TYPE,"application/json;charset=UTF-8")
                .header(cn.hutool.http.Header.ACCEPT,"application/json")
                .body(JSON.toJSONString(rData)).execute().body();
        return response;
    }
}
