package com.unicom.rts.function;

import lombok.Data;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;

@Data
public class CustomDeSerializationSchema implements KafkaDeserializationSchema<ConsumerRecord<Object, Object>> {

    @Override
    public boolean isEndOfStream(ConsumerRecord<Object, Object> nextElement) {
        return false;
    }

    //这里返回一个ConsumerRecord<String,String>类型的数据，除了原数据还包括topic,offset,partition等信息
    @Override
    public ConsumerRecord<Object, Object> deserialize(ConsumerRecord<byte[], byte[]> record) throws Exception {


        return new ConsumerRecord<Object, Object>(
                record.topic(),
                record.partition(),
                record.offset(),
                record.timestamp(),
                TimestampType.NO_TIMESTAMP_TYPE,
                (long) -1,
                -1,
                -1,
                record.key(),
                record.value(),
                record.headers());
    }

    //指定数据的输入类型
    @Override
    public TypeInformation<ConsumerRecord<Object, Object>> getProducedType() {
        return TypeInformation.of(new TypeHint<ConsumerRecord<Object, Object>>() {
        });
    }
}
