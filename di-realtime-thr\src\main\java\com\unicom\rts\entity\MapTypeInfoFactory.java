package com.unicom.rts.entity;

import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.MapTypeInfo;

import java.lang.reflect.Type;
import java.util.Map;

public class MapTypeInfoFactory extends TypeInfoFactory<Map<String, String>> {
  @Override
  @SuppressWarnings("unchecked")
  public TypeInformation<Map<String, String>> createTypeInfo(Type t, Map<String, TypeInformation<?>> genericParameters) {
    return new MapTypeInfo<>(BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO);
  }
}