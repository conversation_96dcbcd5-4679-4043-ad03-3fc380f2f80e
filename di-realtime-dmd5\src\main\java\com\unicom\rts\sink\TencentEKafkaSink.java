package com.unicom.rts.sink;

import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.Rsp;
import com.unicom.rts.bean.TConnect;
import com.unicom.rts.constant.CommonConstant;
import com.unicom.rts.util.JdbcUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @create 2022-04-28 09:42
 */
public class TencentEKafkaSink {

    public static FlinkKafkaProducer<Tuple2<Rsp, String>> getFlinkKafkaProducer(ParameterTool parameters) throws Exception {

        String sendId = parameters.get(CommonConstant.SEND_ID);
        String sendSql = CommonConstant.SEND_SQL.replace("#ConnectId", sendId);

        JdbcUtil jdbcUtil = new JdbcUtil();
        jdbcUtil.openMySqlConnection(parameters.get(CommonConstant.MYSQL_URL), parameters.get(CommonConstant.MYSQL_USER), parameters.get(CommonConstant.MYSQL_PASSWD));
        List<TConnect> tableArr = jdbcUtil.select(sendSql, TConnect.class);
        if (tableArr.size() == 0) {
            throw new Exception("No producer connect data obtained!");
        }

        TConnect tc = tableArr.get(0);
        String broker = tc.getIpCode();

        JSONObject connectMessage = JSONObject.parseObject(tc.getConnectMessage());
        String kafkaUser = connectMessage.getString("kafka_username");
        String kafkaPassword = connectMessage.getString("kafka_password");
        String isEncrpyt = connectMessage.getString("kafka_encrypt_type");
        String SASL_MECHANISM = connectMessage.getString("kafka_sasl_mechanism");
        String KAFKA_SECURITY = connectMessage.getString("kafka_security");
        String isTiangong = connectMessage.getString("kafka_tiangong");



        Properties producerProp = new Properties();
        producerProp.setProperty("bootstrap.servers", broker);
//            producerProp.put("auto.commit.interval.ms", "100");
//            producerProp.put("session.timeout.ms", "30000");
        boolean sourceSecurity = false;
        if (!StringUtils.isBlank(isTiangong)) {
            sourceSecurity = Boolean.valueOf(isTiangong);
        }
        if ("1".equals(isEncrpyt)) {
            if (sourceSecurity) {
                //天宫kafka安全设置
                producerProp.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
                producerProp.setProperty("security.protocol", "SASL_PLAINTEXT");
                producerProp.setProperty("sasl.mechanism", "SCRAM-SHA-256");
            } else if ("1".equals(SASL_MECHANISM)) {
                if(StringUtils.isBlank(KAFKA_SECURITY)){
                    KAFKA_SECURITY = "SASL_PLAINTEXT";
                }
                producerProp.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY);
                producerProp.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
                producerProp.put("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            } else if ("0".equals(SASL_MECHANISM)) {
                if(StringUtils.isBlank(KAFKA_SECURITY)){
                    KAFKA_SECURITY = "SASL_PLAINTEXT";
                }
                producerProp.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY);
                producerProp.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
                producerProp.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            }
        }

        producerProp.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        producerProp.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,StringSerializer.class.getName());
        //借鉴的
        producerProp.put(ProducerConfig.ACKS_CONFIG, "all"); //Must set acks to all in order to use the idempotent producer

        int retriesConfig = parameters.getInt("retriesConfig", 3);
        producerProp.put(ProducerConfig.RETRIES_CONFIG, retriesConfig);

        long retryBackoffMsConfig = parameters.getLong("retryBackoffMsConfig",500);
        producerProp.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG,retryBackoffMsConfig);

        int batchSizeConfig = parameters.getInt("batchSizeConfig", 16384);
        producerProp.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSizeConfig);

        int lingerMsConfig = parameters.getInt("lingerMsConfig", 50);
        producerProp.put(ProducerConfig.LINGER_MS_CONFIG,lingerMsConfig);

        long bufferMemoryConfig = parameters.getLong("bufferMemoryConfig", 33554432);
        producerProp.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemoryConfig);

        int requestTimeoutMsConfig = parameters.getInt("requestTimeoutMsConfig", 120000);
        producerProp.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, requestTimeoutMsConfig);

        int deliveryTimeoutMsConfig = parameters.getInt("deliveryTimeoutMsConfig", 120050);
        producerProp.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,deliveryTimeoutMsConfig);

        int transactionTimeoutConfig = parameters.getInt("transactionTimeoutConfig",5);
        producerProp.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG,1000 * 60 * transactionTimeoutConfig+"");

        producerProp.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION,"1");
        producerProp.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");

        FlinkKafkaProducer<Tuple2<Rsp, String>> ret = new FlinkKafkaProducer(
                "all",      // target topic
                new KafkaMultiSinkSerializationSchema(),    // serialization schema
                producerProp,                // producer config
                FlinkKafkaProducer.Semantic.EXACTLY_ONCE,5);
        return ret;

    }

}
