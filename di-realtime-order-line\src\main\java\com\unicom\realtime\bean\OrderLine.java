package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderLine {
    private String orderId;
    private String orderStatus;
    private String orderStatusValue;
    private String serialNumber;
    private String tagValue = "";
    private String datasource;
    private String attrCode;
    private String attrValue;
    private String sendExpCom;
    private String opt;
    private String orderNodeCode;
}
