package com.unicom.realtime.entity;


import lombok.Data;
import org.apache.commons.lang3.StringUtils;
@Data
public class ZjData {

    private static final String SEPARATOR = "\u0001";


    //    private String productIn;
//    private String validMainProduct;
   private String provinceCode;
//    private String userId;
   private String system;
   private String cityCode;
   private String productId;
   private String innetDate;
   private String lackBalanceTime;
   private String trafficCapsTime;
   private String labelStLoginTimeReal;
//    private String productIs2i2c;
//    private String rProductIn;
//    private String rProductIs2i2c;
//    private String isContractUser;
//    private String mainProductMonthlyFee;



// 云盘数据
    private String phone;
    private String level;

    private String interceptCount;
    private String pickUpCount;
    private String orderStatus;
    private String expireRemainingDays;
    private String canBindMemberCount;
    private String alreadyBindMemberCount;
    private String bindDevice;

    //小余额标签
    private String user_id;
    private String serialNumber;
    private String BROADBAND_NOTINCE_TYPE;
    private String province_code;
    private String BROADBAND_NOTINCE_TIME;
    private String ACCT_ID;

    private String phone_number;
    private String act_ids;

    private String resumptionPhone;
    private String resumptionStatus;

    private String TELESCORE;
    private String AWARDSCORE;
    private String TOTELSCORE;
    private String IS_ARREARAGE;


    // 联通APP登录标签
    public boolean hasLevel() {
        return StringUtils.isNotBlank(this.level);
    }
    public boolean hasPickUpCount() {
        return StringUtils.isNotBlank(this.pickUpCount);
    }
    public boolean hasOrderStatus() {
        return StringUtils.isNotBlank(this.orderStatus);
    }
    public boolean hasExpireRemainingDays() {
        return StringUtils.isNotBlank(this.expireRemainingDays);
    }
    public boolean hasCanBindMemberCount() {
        return StringUtils.isNotBlank(this.canBindMemberCount);
    }
    public boolean hasAlreadyBindMemberCount() {
        return StringUtils.isNotBlank(this.alreadyBindMemberCount);
    }
    public boolean hasBindDevice() {
        return StringUtils.isNotBlank(this.bindDevice);
    }
    public boolean hasInterceptCount() {
        return StringUtils.isNotBlank(this.interceptCount);
    }
    public boolean hasLackBalanceTime() {
        return StringUtils.isNotBlank(this.lackBalanceTime);
    }
    public boolean hasTrafficCapsTime() {
        return StringUtils.isNotBlank(this.trafficCapsTime);
    }
    public boolean hasProductId() {
        return StringUtils.isNotBlank(this.productId);
    }
    public boolean hasInnetDate() {
        return StringUtils.isNotBlank(this.innetDate);
    }
    public boolean hasCityCode() {
        return StringUtils.isNotBlank(this.cityCode);
    }
    public boolean hasProvinceCode() {
        return StringUtils.isNotBlank(this.provinceCode);
    }
    public boolean hasSystem() {
        return StringUtils.isNotBlank(this.system);
    }

    public boolean hasPhone() {
        return StringUtils.isNotBlank(this.phone);
    }
    public boolean hasLabelStLoginTimeReal() {
        return StringUtils.isNotBlank(this.labelStLoginTimeReal);
    }

    public boolean hasBROADBAND_NOTINCE_TYPE() {
        return StringUtils.isNotBlank(this.BROADBAND_NOTINCE_TYPE);
    }
    public boolean hasBROADBAND_NOTINCE_TIME() {
        return StringUtils.isNotBlank(this.BROADBAND_NOTINCE_TIME);
    }
    public boolean hasPhone_number() {
        return StringUtils.isNotBlank(this.phone_number);
    }
    public boolean hasAct_ids() {
        return StringUtils.isNotBlank(this.act_ids);
    }
    public boolean hasResumptionPhone() {
        return StringUtils.isNotBlank(this.resumptionPhone);
    }
    public boolean hasResumptionStatus() {
        return StringUtils.isNotBlank(this.resumptionStatus);
    }
    public boolean hasTELESCORE() {
        return StringUtils.isNotBlank(this.TELESCORE);
    }
    public boolean hasAWARDSCORE() {
        return StringUtils.isNotBlank(this.AWARDSCORE);
    }
    public boolean hasTOTELSCORE() {
        return StringUtils.isNotBlank(this.TOTELSCORE);
    }

    public boolean hasIS_ARREARAGE() {
        return StringUtils.isNotBlank(this.IS_ARREARAGE);
    }

}
