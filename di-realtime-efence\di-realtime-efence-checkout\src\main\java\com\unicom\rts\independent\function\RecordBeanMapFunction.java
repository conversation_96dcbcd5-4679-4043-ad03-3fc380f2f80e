package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.MidDataBean;
import com.unicom.rts.independent.beans.RecordBean;
import com.unicom.rts.independent.enums.MidDataEnum;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/
public class RecordBeanMapFunction extends ProcessFunction<ConsumerRecord<String, String>, RecordBean> {

    private OutputTag<MidDataBean> midBak;

    public RecordBeanMapFunction(OutputTag<MidDataBean> midBak) {
        this.midBak = midBak;
    }

    @Override
    public void processElement(ConsumerRecord<String, String> recordStr, Context ctx, Collector<RecordBean> out) throws Exception {
        RecordBean recordBean = new RecordBean();
        MidDataBean midDataBean = new MidDataBean();
        recordBean.setTopic(recordStr.topic());
        midDataBean.setSourceTopic(recordBean.getTopic());
        String record = recordStr.value();

        if (null != record && !"".equals(record)) {
            String[] strTmp = record.split("\\u0001");
            String[] str = new String[14];
            System.arraycopy(strTmp, 0, str, 0, strTmp.length);
            recordBean.setRecord(record);
            recordBean.setDeviceNum(str[MidDataEnum.DEVEICENUMBER.getCode()]);
            recordBean.setHprovName(str[MidDataEnum.HPROVNAME.getCode()]);
            recordBean.setArea(str[MidDataEnum.AREA.getCode()]);

            midDataBean.setDeviceNumber(str[MidDataEnum.DEVEICENUMBER.getCode()]);
            midDataBean.setTime(str[MidDataEnum.TIME.getCode()]);
            midDataBean.setHprovName(str[MidDataEnum.HPROVNAME.getCode()]);
            midDataBean.setHareaName(str[MidDataEnum.HAREANAME.getCode()]);
            midDataBean.setArea(str[MidDataEnum.AREA.getCode()]);
            midDataBean.setImei(str[MidDataEnum.IMEI.getCode()]);
            midDataBean.setImsi(str[MidDataEnum.IMSI.getCode()]);
            midDataBean.setCurrentTime(str[MidDataEnum.CURRENTTIME.getCode()]);
            midDataBean.setLac(str[MidDataEnum.LAC.getCode()]);
            midDataBean.setCi(str[MidDataEnum.CI.getCode()]);
            midDataBean.setLongitude(str[MidDataEnum.LONGITUDE.getCode()]);
            midDataBean.setLatitude(str[MidDataEnum.LATITUDE.getCode()]);
            midDataBean.setProvId(str[MidDataEnum.PROVID.getCode()]);
            midDataBean.setPoweroffInd(str[MidDataEnum.POWEROFFIND.getCode()]);
        }
        out.collect(recordBean);
        ctx.output(midBak, midDataBean);
    }
}
