package com.unicom.rts;

import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.kafka.common.header.Headers;

import java.lang.reflect.Type;
import java.util.Map;

public class HeadersTypeInfoFactory extends TypeInfoFactory<Headers> {

    @Override
    public TypeInformation<Headers> createTypeInfo(Type t, Map<String, TypeInformation<?>> genericParameters) {
        return new HeadersTypeInfo(genericParameters.get("headers"),genericParameters.get("isReadOnly"));
    }

}

