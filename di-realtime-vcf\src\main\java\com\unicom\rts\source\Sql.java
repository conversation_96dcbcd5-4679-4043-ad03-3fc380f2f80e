package com.unicom.rts.source;

public class Sql {
    public static final String CREATE_DATA_INDEPENDENT = "CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon` (\n" +
            "  dts_collect_time STRING,\n" +
            "  dts_kaf_time STRING,\n" +
            "  dts_kaf_offset STRING,\n" +
            "  dts_kaf_part STRING,\n" +
            "  operate_type STRING,\n" +
            "  operate_time STRING,\n" +
            "  operate_type_desc STRING,\n" +
            "  Id STRING,\n" +
            "  phone_num STRING,\n" +
            "  is_delete STRING,\n" +
            "  create_user STRING,\n" +
            "  create_name STRING,\n" +
            "  create_time STRING,\n" +
            "  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',\n" +
            "  PRIMARY KEY (phone_num) NOT ENFORCED\n" +
            ") WITH (\n" +
            "  'bucket' = '50',\n" +
            "  'bucket-key' = 'phone_num',\n" +
            "  'write-only' = 'true',\n" +
            "  'file.format' = 'avro',\n" +
            "  'metadata.stats-mode' = 'none',\n" +
            "  'consumer.expiration-time' = '48h',\n" +
            "  'snapshot.time-retained' = '1h',\n" +
            "  'num-sorted-run.stop-trigger'='2147483647',\n" +
            "  'sort-spill-threshold'='10'\n" +
            ");";

    public static final String SELECT_DATA_INDEPENDENT = "SELECT\n" +
            "  dts_collect_time,\n" +
            "  dts_kaf_time,\n" +
            "  dts_kaf_offset,\n" +
            "  dts_kaf_part,\n" +
            "  operate_type,\n" +
            "  operate_time,\n" +
            "  operate_type_desc,\n" +
            "  Id,\n" +
            "  phone_num,\n" +
            "  is_delete,\n" +
            "  create_user,\n" +
            "  create_name,\n" +
            "  create_time,\n" +
            "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME\n" +
            "  from `paimon_catalog`.`ubd_sscj_prod_flink`.`payment_data_independent_paimon`";

    public static void main(String[] args) {
        System.out.println(SELECT_DATA_INDEPENDENT);
    }

}


