package com.hl.opnc;//package com.hl.opnc;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//
//import net.sf.json.JSONArray;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.sitech.dss.util.DESCoder;
//import com.sitech.dss.util.DateUtil;
//import com.sitech.dss.vo.task.service.BjltParamsWebServiceVo;
//
//public class CommdbHttpRestClient extends HttpRestClient{
//
//  private static final Logger logger = LoggerFactory.getLogger(CommdbHttpRestClient.class);
//
//  public static void main(String arg[]) throws Exception {
//
//	//租户ID|租户账号|平台ID|服务标识|当前系统访问日期
//	//String source = "20160804083927YsrwUipdsZ2QiO40qCs|dev|20170623112249TfIS7nif1OGSjyDYVqP|tj_commdb|" + DateUtil.getDate(new Date(), null);
//	String source = "20160804083927YsrwUipdsZ2QiO40qCs|dev|201608051714404aAXmY8CxUBxdqDe1EP|commDBMysql|" + HttpRestClient.getDate(new Date(), null);
//	System.out.println("原文--->"+source);
//	@SuppressWarnings("static-access")
//	String hexCrypt = new DESCoder().encoder(source);
//	System.out.println("生成的密文--->"+hexCrypt);
//
//    //2019-04-19 千万级别---测试不同的Impala表设计方案的实时访问性能情况。 http://eip.teamshub.com/t/3957966
//    BjltParamsWebServiceVo paramsVo = new BjltParamsWebServiceVo();
//    paramsVo.setEncryptType("tocken");//必填：加密方式
//    paramsVo.setEncryptHex(hexCrypt);// 必填：加密
//    paramsVo.setRentId("20160804083927YsrwUipdsZ2QiO40qCs");//暂时必填：登录系统对接平台对应的租户ID
//    paramsVo.setRentEName("dev");//暂时必填：登录系统对接平台对应的租户账号
//    paramsVo.setPlatId("201608051714404aAXmY8CxUBxdqDe1EP");//暂时必填：对接平台ID
//	paramsVo.setServiceEname("commDBMysql");	//暂时必填：配置服务的英文标识
//    paramsVo.setResultType("2");//期望的返回值类型：1-XML（默认）；2-JSON
//
//    //主动传递用户自定义参数
//    HashMap<String,Object> userparams = new HashMap<String,Object>();
//    userparams.put("othercondition", " "); //没有条件，则传递空串以便替换空串
//    paramsVo.setUserparams(userparams);
//
//	List<BjltParamsWebServiceVo> voList = new ArrayList<BjltParamsWebServiceVo>();
//	voList.add(paramsVo);
//
//	JSONArray arraySource = JSONArray.fromObject(voList);
//	String jsonStr = arraySource.toString();
//	logger.info("输入的json串--->"+jsonStr);
//	System.out.println("输入的json串--->"+jsonStr);
//
////	//1.1 实时查询commdb信息(字节)
////	String syncbyte = "http://172.18.231.146:8088/bdessweb/rest/commdbws/syncbyte/queryUserPoint";
////	logger.info("访问地址--->"+syncbyte);
////    String syncbyteResut = String.valueOf(doPostRequest(syncbyte, jsonStr));
////	logger.info("访问结果--->"+syncbyteResut);
//
//	//1.2 实时查询commdb信息(字符串)
//	String syncstr = "http://172.18.231.146:8088/bdessweb/rest/commdbws/syncstr/queryUserPoint";
//	logger.info("访问地址--->"+syncstr);
//	System.out.println("访问地址--->"+syncstr);
//    String syncstrResult = String.valueOf(doPostRequest(syncstr, jsonStr));
//	logger.info("访问结果--->"+syncstrResult);
//	System.out.println("访问结果--->"+syncstrResult);
//
//
////	//2.1、异步响应方式(字节)
////	String asyncbyte = "http://172.18.231.146:8088/bdessweb/rest/commdbws/asyncbyte/queryUserPoint";
////	logger.info("访问地址--->"+asyncbyte);
////    String asyncbyteResut = String.valueOf(doPostRequest(asyncbyte, jsonStr));
////	logger.info("访问结果--->"+asyncbyteResut);
//
//	//2.2、异步响应方式(字符串)
////	String asyncstr = "http://172.18.231.146:8088/bdessweb/rest/commdbws/asyncstr/queryUserPoint";
////	logger.info("访问地址--->"+asyncstr);
////    String asyncstrResut = String.valueOf(doPostRequest(asyncstr, jsonStr));
////	logger.info("访问结果--->"+asyncstrResut);
//
//	//3.1、查询异步离线请求发起的离线状态查询是否已完成；
////	String querybyte = " http://172.18.231.146:8088/bdessweb/rest/commdbws/querylogbyte/201904191617446FxcddxmrE6MQVyrvj5_dev_20190914144200570";
////	logger.info("访问地址--->"+querybyte);
////    String querybyteResut = String.valueOf(doGet(querybyte,HttpRestClient.UTF8));
////	logger.info("访问结果--->"+querybyteResut);
//
//	//3.1、查询异步离线请求发起的离线状态查询是否已完成；
////	String querystr = " http://172.18.231.146:8088/bdessweb/rest/commdbws/querylogstr/201904191617446FxcddxmrE6MQVyrvj5_dev_20190914144200570";
////	logger.info("访问地址--->"+querystr);
////    String querystrResut = String.valueOf(doGet(querystr,HttpRestClient.UTF8));
////	logger.info("访问结果--->"+querystrResut);
//  }
//
//}