package com.unicom;

import com.unicom.beans.NewUserBean;
import com.unicom.function.ChnlCalFunc;
import com.unicom.function.NewUserMapFunc;
import com.unicom.sink.RedisSink;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.OutputTag;

import java.util.concurrent.TimeUnit;

import static com.unicom.source.KafkaSource.getKafkaConsumer;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/23
 **/
public class NewUser2RedisMain {

    public static void main(String[] args) throws Exception {

        ParameterTool conf = ParameterTool.fromArgs(args);
        //ParameterTool conf = ParameterTool.fromPropertiesFile(parameters.get("app_config_path"));
        //ParameterTool conf = ParameterTool.fromPropertiesFile("D:\\shuke\\newCode\\kafka-bus8-to-redis-flink\\src\\main\\config\\config.properties");

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"),
                true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);  //设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        //checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));

        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        // 最小间隔 4分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(4));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);


        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10,
                Time.of(10, TimeUnit.SECONDS)));

        //env.registerCachedFile(conf.get("path.dwd_d_mrt_e_chnl_area_info"), "dwd_d_mrt_e_chnl_area_info");

        env.disableOperatorChaining();

        OutputTag<NewUserBean> errorDataStream = new OutputTag<>("errorData", TypeInformation.of(NewUserBean.class));

        DataStream<NewUserBean> newUserStream = env.addSource(getKafkaConsumer(conf))
                .name("kafkaSource")
                .uid("kafkaSource")
                .setParallelism(Integer.parseInt(conf.get("parallelism.kafkaSource")))
                .process(new NewUserMapFunc(errorDataStream))
                .name("mapFunc")
                .uid("mapFunc")
                .setParallelism(Integer.parseInt(conf.get("parallelism.mapFunc")));

        DataStream<NewUserBean> resultStream = newUserStream.process(new ChnlCalFunc(conf))
                .name("chnlCalFunc")
                .uid("chnlCalFunc")
                .setParallelism(Integer.parseInt(conf.get("parallelism.chnlCalFunc")));

        resultStream.addSink(new RedisSink(conf))
                .name("sink")
                .uid("sink")
                .setParallelism(Integer.parseInt(conf.get("parallelism.sink")));

        env.execute("NewUser2Redis");
    }
}
