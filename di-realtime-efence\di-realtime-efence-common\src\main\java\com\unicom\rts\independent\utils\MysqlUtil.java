package com.unicom.rts.independent.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;

public class MysqlUtil {

    private final static Logger logger = LoggerFactory.getLogger(MysqlUtil.class);

    public static Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.jdbc.Driver");
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            logger.error("-----------mysql get connection has exception , msg = {}", e.toString());
        }
        return con;
    }

}
