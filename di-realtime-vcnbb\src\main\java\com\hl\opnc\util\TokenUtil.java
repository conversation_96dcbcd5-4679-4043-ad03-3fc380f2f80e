package com.hl.opnc.util;

import com.alibaba.fastjson.JSON;
import com.hl.opnc.Authentication;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @desc:
 * @fileName: TokenUtil
 * @author: tangjiaxiang
 * @createTime: 2020/12/1 11:40:40
 * @modifier:
 */
public class TokenUtil {

    private final static Logger log = LoggerFactory.getLogger(TokenUtil.class);




    private static int JVM_SEQ = 9000;

    private final static String APP_ID = "APP_ID";
    private final static String TIMESTAMP = "TIMESTAMP";
    private final static String TRANS_ID = "TRANS_ID";

    /**
     * 生成认证实体
     *
     * @param appId  应用ID
     * @param secret 应用密钥
     * @return
     */
    public static Authentication generateAuth(String appId, String secret) {
        return generateAuth(appId, secret, null);
    }


    /**
     * 生成认证实体
     *
     * @param appId  应用ID
     * @param secret 应用密钥
     * @param body   请求体
     * @return
     */
    public static Authentication generateAuth(String appId, String secret, String body) {
        log.debug("appId: " + appId + ",secret: " + secret + ",body: " + body);
        DateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        String timestamp = FORMAT.format(new Date());
        String transId = random();
        String token = generateToken(appId, secret, timestamp, transId, body);
        Authentication authentication = new Authentication();
        authentication.setAPP_ID(appId);
        authentication.setTIMESTAMP(timestamp);
        authentication.setTRANS_ID(transId);
        authentication.setTOKEN(token);

        log.debug("authenticationJson: " + JSON.toJSONString(authentication));
        return authentication;
    }


    /**
     * 生成Token
     *
     * @param appId     应用ID
     * @param secret    应用密钥
     * @param timestamp 时间戳
     * @param transId   业务ID
     * @return
     */
    public static String generateToken(String appId, String secret, String timestamp, String transId) {
        String concatStr = APP_ID + appId + TIMESTAMP + timestamp + TRANS_ID + transId + secret;
        String md5Hex = DigestUtils.md5Hex(concatStr);
        log.debug("concatStr: " + concatStr + ",md5Hex: " + md5Hex);
        return md5Hex;
    }


    /**
     * 生成Token
     *
     * @param appId     应用ID
     * @param secret    应用密钥
     * @param timestamp 时间戳
     * @param transId   业务ID
     * @param body      请求体
     * @return
     */
    public static String generateToken(String appId, String secret, String timestamp, String transId, String body) {
        log.debug("appId: " + appId + ",secret: " + secret + ",timestamp: " + timestamp + ",transId: " + transId + ",body: " + body);

        if (body != null) {
            String concatStr = APP_ID + appId + TIMESTAMP + timestamp + TRANS_ID + transId + body + secret;
            String md5Hex = DigestUtils.md5Hex(concatStr);
            log.debug("concatStr: " + concatStr + ",md5Hex: " + md5Hex);
            return md5Hex;
        }
        return generateToken(appId, secret, timestamp, transId);
    }


    private static String random() {
        DateFormat RANDOM_FORMAT = new SimpleDateFormat("yyMMddHHmmssSSS");
        StringBuilder sb = new StringBuilder();
        sb.append(RANDOM_FORMAT.format(new Date()));
        ++JVM_SEQ;
        if (JVM_SEQ >= 10000) {
            JVM_SEQ = 0;
        }
        if (JVM_SEQ < 10) {
            sb.append("000");
        } else if (JVM_SEQ < 100) {
            sb.append("00");
        } else if (JVM_SEQ < 1000) {
            sb.append("0");
        }
        sb.append(JVM_SEQ);
        return sb.toString();
    }

}
