package com.unicom.realtime.source;

import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;

import java.util.Properties;

public class KafkaSource {
    public static FlinkKafkaConsumer getFlinkKafkaConsumer(String brokers, String topic, ParameterTool conf) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", brokers);
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        Boolean sourceSecurity = conf.getBoolean("source.kafka.security");
        if (sourceSecurity) {
            String kafkaUser = conf.get("kafka.user");
            String kafkaPassword = conf.get("kafka.password");
            //天宫kafka安全设置
            properties.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", "SASL_PLAINTEXT");
            properties.setProperty("sasl.mechanism", "SCRAM-SHA-256");
        }

        FlinkKafkaConsumer consumer = new FlinkKafkaConsumer(
                topic, new SimpleStringSchema(), properties);
        consumer.setCommitOffsetsOnCheckpoints(true);

        return consumer;
    }
}
