package com.unicom.utils;

import com.unicom.beans.ChnlInfoBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/23
 **/
@Slf4j
public class ReadTxtUtil {

    public static Map<String, ChnlInfoBean> readTxts(File dimArea) throws IOException {
        InputStreamReader isr = new InputStreamReader(new FileInputStream(dimArea), "UTF-8");
        BufferedReader br = new BufferedReader(isr);
        String line = null;
        int count = 0;
        Map<String, ChnlInfoBean> map = new HashMap<>();
        //List<ChnlInfoBean> list = new ArrayList<ChnlInfoBean>();
        try{
            while ((line = br.readLine()) != null) {
                ChnlInfoBean txt = new ChnlInfoBean();
                String[] arr = line.split("\\|",-1);
                if (arr.length > 6) {
                    if (null != arr[1]) {
                        txt.setChnlCode(arr[1].trim());
                    }
                    if (null != arr[2]) {
                        txt.setLevelACode(arr[2].trim());
                    }
                    if (null != arr[3]) {
                        txt.setChnlName(arr[3].trim());
                    }
                    if (null != arr[4]) {
                        txt.setLevel3(arr[4].trim());
                    }
                    if (null != arr[5]) {
                        txt.setLevel3Code(arr[5].trim());
                    }
                    if (null != arr[6]) {
                        txt.setLevelYsCode(arr[6].trim());
                    }
                    //list.add(txt);
                    map.put(txt.getChnlCode(), txt);
                }
                count++;
            }
            log.info("读取总条数：" + count + "||读取的list的长度" + map.size());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            br.close();
        }
        return map;
    }

    public static Map<String, ChnlInfoBean> readTxtsFromHdfs(FileStatus[] files, FileSystem fs) throws IOException {
        Map<String, ChnlInfoBean> map = new HashMap<>();
        int count = 0;
        for (int i = 0; i < files.length; i++) {
            if (!files[i].isDirectory()) {
                FSDataInputStream fsDataInputStream = fs.open(files[i].getPath());
                BufferedReader br = new BufferedReader(new InputStreamReader(fsDataInputStream));

                try {
                    String strTmp;
                    while ((strTmp = br.readLine()) != null) {
                        ChnlInfoBean txt = new ChnlInfoBean();
                        String[] arr = strTmp.split("\\|",-1);
                        if (arr.length > 6) {
                            if (null != arr[1]) {
                                txt.setChnlCode(arr[1].trim());
                            }
                            if (null != arr[2]) {
                                txt.setLevelACode(arr[2].trim());
                            }
                            if (null != arr[3]) {
                                txt.setChnlName(arr[3].trim());
                            }
                            if (null != arr[4]) {
                                txt.setLevel3(arr[4].trim());
                            }
                            if (null != arr[5]) {
                                txt.setLevel3Code(arr[5].trim());
                            }
                            if (null != arr[6]) {
                                txt.setLevelYsCode(arr[6].trim());
                            }
                            map.put(txt.getChnlCode(), txt);
                            count++;
                        }
                    }
                    log.info("读取总条数：" + count + "||读取的list的长度" + map.size());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    br.close();
                }
            }
        }
        return map;
    }
}
