package com.unicom.rts.function;

import com.google.gson.Gson;
import com.unicom.rts.entity.DebtData;
import com.unicom.rts.enums.DebtSourceEnum;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class DebtClean extends RichFlatMapFunction<ConsumerRecord<String, String>, DebtData> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record,Collector<DebtData> out) throws Exception {
        DebtData debtData = new DebtData();
        parse(debtData, record);
        //增加头
        debtData.parseHeader(record);
        out.collect(debtData);
    }

    public void parse(DebtData debt, ConsumerRecord<String,String> record) {
        String s = record.value();
        String[] columns = StringUtils.splitPreserveAllTokens(s, "\001");
        if (columns.length >= 14) {
            debt.setDeviceNumber(columns[DebtSourceEnum.DEVICENUMBER.ordinal()]);
            debt.setPartitionId(columns[DebtSourceEnum.PARTITIONID.ordinal()]);
            debt.setUserId(columns[DebtSourceEnum.USERID.ordinal()]);
            debt.setServiceId(columns[DebtSourceEnum.SERVICEID.ordinal()]);
            debt.setStartDate(columns[DebtSourceEnum.STARTDATE.ordinal()]);
            debt.setEndDate(columns[DebtSourceEnum.ENDDATE.ordinal()]);
            debt.setMainTag(columns[DebtSourceEnum.MAINTAG.ordinal()]);
            debt.setStateCode(columns[DebtSourceEnum.STATECODE.ordinal()]);
            debt.setProvId(columns[DebtSourceEnum.PROVID.ordinal()]);
            debt.setCityId(columns[DebtSourceEnum.CITYID.ordinal()]);
            debt.setDataSource(columns[DebtSourceEnum.DATASOURCE.ordinal()]);
            debt.setInTime(columns[DebtSourceEnum.INTIME.ordinal()]);
            debt.setUpdateTime(columns[DebtSourceEnum.UPDATETIME.ordinal()]);
            debt.setDataReceiveTime(columns[DebtSourceEnum.CDHTIME.ordinal()]);
        }
    }
}
