package com.hl.opnc;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @desc:
 * @fileName: Authentication
 * @author: tangjiaxiang
 * @createTime: 2020/12/1 14:43:43
 * @modifier:
 */
public class Authentication implements Serializable {

    /**
     * 应用ID
     */
    @JSO<PERSON>ield(name = "APP_ID")
    private String APP_ID;

    /**
     * 时间戳 2016-04-12 15:06:06 100
     */
    @J<PERSON><PERSON>ield(name = "TIMESTAMP")
    private String TIMESTAMP;

    /**
     * 业务ID
     */
    @JSONField(name = "TRANS_ID")
    private String TRANS_ID;

    /**
     * 签名
     */
    @JSONField(name = "TOKEN")
    private String TOKEN;

    public String getAPP_ID() {
        return APP_ID;
    }

    public void setAPP_ID(String APP_ID) {
        this.APP_ID = APP_ID;
    }

    public String getTIMESTAMP() {
        return TIMESTAMP;
    }

    public void setTIMESTAMP(String TIMESTAMP) {
        this.TIMESTAMP = TIMESTAMP;
    }

    public String getTRANS_ID() {
        return TRANS_ID;
    }

    public void setTRANS_ID(String TRANS_ID) {
        this.TRANS_ID = TRANS_ID;
    }

    public String getTOKEN() {
        return TOKEN;
    }

    public void setTOKEN(String TOKEN) {
        this.TOKEN = TOKEN;
    }

}
