package com.unicom.rts.fun;

import com.unicom.rts.bean.Xz;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Xz<PERSON>lean extends RichFlatMapFunction<ConsumerRecord<String, String>, Xz> {
    private final static Logger logger = LoggerFactory.getLogger(XzClean.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record,Collector<Xz> out) throws Exception {
        Xz xz = new Xz();
        parse(xz, record);
        //增加头
        xz.parseHeader(record);
        try {
            out.collect(xz);
        } catch (Exception e) {
            logger.error("{} XzClean {} ", xz.toString(), e);
        }
    }

    public void parse(Xz xz, ConsumerRecord<String,String> record) {
        String str = record.value();
        String[] columns = StringUtils.splitPreserveAllTokens(str, "\001");
        xz.setOpt(columns[85]);
        xz.setTradeTypeCode(columns[3]);
        xz.setProvinceCode(columns[69]);
        xz.setEparchyCode(columns[26]);
        xz.setNetTypeCode(columns[16]);
        xz.setSerialNumber(columns[17]);
        xz.setSubscribeState(columns[7]);
        xz.setNextDealTag(columns[8]);
        xz.setCancelTag(columns[38]);
//        System.out.println("xz====>"+xz);
    }
}
