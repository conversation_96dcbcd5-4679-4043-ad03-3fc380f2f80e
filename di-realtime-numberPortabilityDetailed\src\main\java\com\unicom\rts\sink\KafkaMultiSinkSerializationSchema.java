package com.unicom.rts.sink;

import com.unicom.rts.bean.Rsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/20 22:32
 */
public class KafkaMultiSinkSerializationSchema implements KafkaSerializationSchema<Tuple2<Rsp, String>> {
    private final static Logger logger = LoggerFactory.getLogger(KafkaMultiSinkSerializationSchema.class);
    @Override
    public ProducerRecord<byte[], byte[]> serialize(Tuple2<Rsp, String> value, @Nullable Long aLong) {
        List<Header> headers = new ArrayList<>();

        Header receiveTime = new RecordHeader("receive_time", value.f0.getReceiveTime().getBytes());
        headers.add(receiveTime);

        if(!StringUtils.isBlank(value.f0.getIntegrationTime())){
            Header integrationTime = new RecordHeader("integration_time",value.f0.getIntegrationTime().getBytes());
            headers.add(integrationTime);
        }

        logger.info("------------------------------------下发了Topic--------------------------------"+value.f0.getDestTopic());
        logger.info("------------------------------------下发了Topic--------------------------------"+value.f1);
//        ProducerRecord<String, String> record = new ProducerRecord<>("topic", null, "key", "value", headers);
        return new ProducerRecord(value.f0.getDestTopic(), null, value.f0.getKeyBy(), value.f1, headers);

    }
}
