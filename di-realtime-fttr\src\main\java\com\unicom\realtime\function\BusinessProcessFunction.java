package com.unicom.realtime.function;

import com.alibaba.fastjson.JSONObject;
import com.unicom.realtime.bean.*;
import com.unicom.realtime.constant.CommonConstant;
import com.unicom.realtime.util.HbaseUtil;
import com.unicom.realtime.util.JdbcUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class BusinessProcessFunction extends KeyedBroadcastProcessFunction<String, Tuple2<Rsp, TfBhTradeBean>, HashMap, Tuple2<Rsp, FttrOrderBean>> {

    private final static Logger logger = LoggerFactory.getLogger(BusinessProcessFunction.class);
    private HashMap<String, String> productIdMap = new HashMap<>();

    private Connection connection = null;

    private String hbase_rts_user_tag = "";

    private String hbase_trade_product = "";

    private String dst_topic_name="";

    private ParameterTool conf;

    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public BusinessProcessFunction(ParameterTool parameters) {
        this.conf = parameters;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ExecutionConfig.GlobalJobParameters globalJobParameters = getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        Map<String, String> confMap = globalJobParameters.toMap();
        this.dst_topic_name = confMap.get("dst_topic_name");
        if (connection == null || connection.isClosed()) {


            this.hbase_trade_product = confMap.get("hbase_trade_product");
            this.hbase_rts_user_tag = confMap.get("hbase.table");

            org.apache.hadoop.conf.Configuration hbaseConfig = HBaseConfiguration.create();
            hbaseConfig.set("hbase.zookeeper.quorum", confMap.get("hbase.zookeeper"));
            hbaseConfig.set("hbase.zookeeper.property.clientPort", confMap.get("hbase.zookeeper.port"));// zookeeper端口
            hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
            hbaseConfig.set(HConstants.HBASE_RPC_READ_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_RPC_WRITE_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_OPERATION_TIMEOUT, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_SCANNER_TIMEOUT_PERIOD, "1800000");
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(confMap.get("hbase.user"));
            connection = ConnectionFactory.createConnection(hbaseConfig, User.create(userGroupInformation));
        }

        String host = conf.get(CommonConstant.MYSQL_URL);
        String user = conf.get(CommonConstant.MYSQL_USER);
        String pwd = conf.get(CommonConstant.MYSQL_PASSWD);

//        logger.info("host:{} user:{} pwd:{}", host, user, pwd);

        JdbcUtil jdbcUtil = new JdbcUtil();
        jdbcUtil.openMySqlConnection(host, user, pwd);

        String sql_context = CommonConstant.FTTR_SQL;
//        logger.info("InitBeanFunction  FTTR_SQL:{}", sql_context);

        List<TFttrCode> tableArr = jdbcUtil.select(sql_context, TFttrCode.class);
//        logger.info("==========================tableArr:{}", JSONObject.toJSONString(tableArr));
        if (tableArr.size() == 0) {

        }
        HashMap<String, String> fttrMap = new HashMap<>();
        tableArr.forEach(one -> {
            TFttrCode fttrCode = one;
            fttrMap.put(fttrCode.getProductId(), fttrCode.getSvcType());
        });
        this.productIdMap =fttrMap;
//        logger.info("=============广播变量初始化=============productIdMap:{}", JSONObject.toJSONString(this.productIdMap));
    }

    @Override
    public void processElement(Tuple2<Rsp, TfBhTradeBean> value, KeyedBroadcastProcessFunction<String, Tuple2<Rsp, TfBhTradeBean>, HashMap, Tuple2<Rsp, FttrOrderBean>>.ReadOnlyContext ctx, Collector<Tuple2<Rsp, FttrOrderBean>> out) throws Exception {

        //logger.info("=================================productIdMap:{} =====", JSONObject.toJSONString(productIdMap));

        TfBhTradeBean tfBhTradeBean = value.f1;
        // 查tf_b_trade_product
        byte[] tpRowKey = DigestUtils.md5Hex(tfBhTradeBean.getTradeId() + "_" + tfBhTradeBean.getAcceptMonth()).getBytes(StandardCharsets.UTF_8);
        Result tpResult = HbaseUtil.getKeyValue(connection, hbase_trade_product, tpRowKey);
        //logger.info("========================tpResult.isEmpty():{}", tpResult.isEmpty());
        HashMap<String, String> tpMap = new HashMap();
        if (null != tpResult && !tpResult.isEmpty()) {
            for (Cell ce : tpResult.rawCells()) {
                String colValue = new String(CellUtil.cloneValue(ce), StandardCharsets.UTF_8);
                String colName = new String(CellUtil.cloneQualifier(ce), StandardCharsets.UTF_8);
                tpMap.put(colName, colValue);
            }
            //logger.info("=================================tpMap:{}", JSONObject.toJSONString(tpMap));
        }
        //logger.info("=================================tpMap.size:{}", tpMap.size());
        //获取上月1号时间戳
        Long lastMonth = lastMonthTimeStamp();
        List<BTradeProductBean> tpList = new ArrayList<>();
        for (String key : tpMap.keySet()) {
            String tpValue = tpMap.get(key);
            if (!StringUtils.isBlank(tpValue)) {
                String[] tpArr = tpValue.split("\u0001");
                Field[] fields = BTradeProductBean.class.getDeclaredFields();
                BTradeProductBean bTradeProductBean = new BTradeProductBean();
                for (int i = 0; i < tpArr.length; i++) {
                    boolean flag = fields[i].isAccessible();
                    fields[i].setAccessible(true);
                    fields[i].set(bTradeProductBean, tpArr[i]);
                    fields[i].setAccessible(flag);
                }
                //logger.info("=================================bTradeProductBean:{}", JSONObject.toJSONString(bTradeProductBean));
                Long st = Long.valueOf(bTradeProductBean.getStartDate());
                if (productIdMap.containsKey(bTradeProductBean.getProductId()) && ("0".equals(bTradeProductBean.getModifyTag()) || "A".equals(bTradeProductBean.getModifyTag())) && st >= lastMonth) {
                    tpList.add(bTradeProductBean);
                }
            }

        }
        //logger.info("=================================tpList.size:{}", tpList.size());
        String inDate = "";
        String cityCode = "";
        if (tpList.size() > 0) {
            String device_number = tfBhTradeBean.getSerialNumber();
            //logger.info("=================================device_number:{}", device_number);
            // 查实时标签： rts_user_tag
            Result results = HbaseUtil.getKeyValue(connection, hbase_rts_user_tag, device_number.getBytes(StandardCharsets.UTF_8));

            if (null != results && !results.isEmpty()) {
                for (Cell cell : results.rawCells()) {
                    String cloumn = new String(CellUtil.cloneQualifier(cell));
                    if (("rt_b_in_date").equalsIgnoreCase(cloumn)) {
                        inDate = new String(CellUtil.cloneValue(cell));
                    }
                    if (("rt_b_city_code").equalsIgnoreCase(cloumn)) {
                        cityCode = new String(CellUtil.cloneValue(cell));
                    }
                }
            }

        }
//        logger.info("=================================inDate:{},cityCode:{}", inDate,cityCode);
        String finalInDate = inDate;
        String finalCityCode = cityCode;
        tpList.forEach(one -> {
            String productId = one.getProductId();

            FttrOrderBean fttrOrderBean = new FttrOrderBean();

            fttrOrderBean.setTradeId(tfBhTradeBean.getTradeId());
            fttrOrderBean.setUserId(tfBhTradeBean.getUserId());
            fttrOrderBean.setSerialNumber(tfBhTradeBean.getSerialNumber());
            fttrOrderBean.setAcceptDate(tfBhTradeBean.getAcceptDate());
            fttrOrderBean.setFinishDate(tfBhTradeBean.getFinishDate());
            fttrOrderBean.setTradeDepartId(tfBhTradeBean.getTradeDepartId());
            fttrOrderBean.setTradeStaffId(tfBhTradeBean.getTradeStaffId());
            fttrOrderBean.setTradeCityCode(tfBhTradeBean.getTradeCityCode());
            fttrOrderBean.setStartDate(one.getStartDate());
            fttrOrderBean.setEndDate(one.getEndDate());
            fttrOrderBean.setProductId(one.getProductId());
            fttrOrderBean.setModifyTag(one.getModifyTag());
            fttrOrderBean.setInModeCode(tfBhTradeBean.getInModeCode());
            fttrOrderBean.setSubscribeId(tfBhTradeBean.getSubscribeId());

            fttrOrderBean.setInDate(finalInDate);
            fttrOrderBean.setCityCode(finalCityCode);

            fttrOrderBean.setSvcType(productIdMap.get(productId));
//            logger.info("=================================fttrOrderBean:{}", JSONObject.toJSONString(fttrOrderBean));
            value.f0.setDestTopic(dst_topic_name);
            out.collect(new Tuple2<>(value.f0, fttrOrderBean));
        });
    }

    @Override
    public void processBroadcastElement(HashMap value, KeyedBroadcastProcessFunction<String, Tuple2<Rsp, TfBhTradeBean>, HashMap, Tuple2<Rsp, FttrOrderBean>>.Context ctx, Collector<Tuple2<Rsp, FttrOrderBean>> out) throws Exception {
        this.productIdMap.putAll(value);
    }


    private Long lastMonthTimeStamp() throws ParseException {
        // 创建 Calendar 对象
        Calendar calendar = Calendar.getInstance();

        // 将日期设置为当前时间
        calendar.setTimeInMillis(System.currentTimeMillis());

        // 将日期调整为上一个月
        calendar.add(Calendar.MONTH, -1);

        // 输出结果
        String month ="0"+ calendar.get(Calendar.MONTH) + 1; // 注意要加1才能得到正确的月份值
        int year = calendar.get(Calendar.YEAR);

        String lastMonth = year + "-" + month.substring(month.length()-2,month.length())+ "-01 00:00:00";
        Date retDate = simpleDateFormat.parse(lastMonth);
        return retDate.getTime();
    }
}
