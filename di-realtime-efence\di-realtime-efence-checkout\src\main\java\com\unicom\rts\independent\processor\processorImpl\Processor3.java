package com.unicom.rts.independent.processor.processorImpl;

import com.unicom.rts.independent.enums.TagEnum;
import com.unicom.rts.independent.processor.AbstractProcessor;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import static com.unicom.rts.independent.utils.FilterUtil.filterByGroovy;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/13
 **/

/**
 * 过滤条件，groovy（groovy中仅过滤）
 * 输出 record + "\1" + productName + "\1" + productCode + "\1" + innetTime
 * blackimei
 */
public class Processor3 extends AbstractProcessor {

    @Override
    public void subProcess() throws Exception {

        if (null != recordBean && null != ruleBean.getParam() &&
                filterByGroovy(recordBean, ruleBean.getParam()).equals(true)) {

            String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                    recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTNAME.getCode());

            String productCode = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                    recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTCODE.getCode());

            String innetTime = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                    recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.INNETTIME.getCode());
            if (StringUtils.isNotBlank(innetTime) && innetTime.length() > 8) {
                innetTime = innetTime.substring(0, 8);
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            checkoutState.put(recordBean.getDeviceNum(), sdf.format(new Date()));
            for(String sinkTopic : Arrays.asList(ruleBean.getSinkTopic().split(","))) {
                out.collect(new Tuple2<>(sinkTopic, recordBean.getRecord() + "\1" + productName + "\1" + productCode + "\1" + innetTime));
            }
        }
    }
}
