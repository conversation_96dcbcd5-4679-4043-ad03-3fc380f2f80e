package com.unicom.rts.bean;

import org.apache.commons.lang3.time.DateFormatUtils;

public class FormatData {
    private final String separator = "\u0001";
    private final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
    private String deviceNumber;
    private String eparchyCode;
    private Long inDateTime;
    private int dataSource;

    public int getDataSource() {
        return dataSource;
    }

    public void setDataSource(int dataSource) {
        this.dataSource = dataSource;
    }

    public String getDeviceNumber() {
        return deviceNumber;
    }

    public void setDeviceNumber(String deviceNumber) {
        this.deviceNumber = deviceNumber;
    }

    public String getEparchyCode() {
        return eparchyCode;
    }

    public void setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
    }

    public Long getInDateTime() {
        return inDateTime;
    }

    public void setInDateTime(Long inDateTime) {
        this.inDateTime = inDateTime;
    }

    @Override
    public String toString() {
        return deviceNumber + separator + DateFormatUtils.format(inDateTime, DATETIME_FORMAT) + separator + DateFormatUtils.format(System.currentTimeMillis(), DATETIME_FORMAT) + separator + eparchyCode;
    }
}
