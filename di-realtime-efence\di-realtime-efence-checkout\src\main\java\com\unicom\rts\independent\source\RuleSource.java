package com.unicom.rts.independent.source;

import com.unicom.rts.independent.bean.RuleBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/13
 **/
@Slf4j
public class RuleSource extends RichSourceFunction<Map<String, RuleBean>> {

    private final static Logger logger = LoggerFactory.getLogger(WhiteListSource.class);
    private PreparedStatement ps;
    private Connection connection;
    private ResultSet resultSet;
    private volatile boolean isRunning = true;
    private ParameterTool conf;

    public RuleSource(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        logger.info("get rulelist.jdbcurl:{}", conf.get("rulelist.jdbcurl"));
        connection = getConnection(conf.get("rulelist.jdbcurl"), conf.get("rulelist.mysql.user"), conf.get("rulelist.mysql.password"));
        String sql = "select source_topic, sink_topic, param, rule_type from efence_rule_info where status = 1";
        logger.warn("sql :{}", sql);
        if (connection != null) {
            ps = this.connection.prepareStatement(sql);
        }
    }

    @Override
    public void run(SourceContext<Map<String, RuleBean>> ctx) throws Exception {
        Map<String, RuleBean> ruleMap = new HashMap<>();
        while (isRunning) {
            try {
                resultSet = ps.executeQuery();
                while (resultSet.next()) {
                    RuleBean ruleBean = new RuleBean().builder()
                            .sinkTopic(resultSet.getString("sink_topic"))
                            .param(resultSet.getString("param"))
                            .ruleType(resultSet.getString("rule_type")).build();

                    ruleMap.put(resultSet.getString("source_topic"), ruleBean);
                }
                ctx.collect(ruleMap);
                ruleMap.clear();
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                resultSet.close();
            }

            Thread.sleep(1000 * 60);
        }
    }

    @Override
    public void cancel() {
        try {
            super.close();
            if (connection != null) {
                connection.close();
            }
            if (ps != null) {
                ps.close();
            }
        } catch (Exception e) {
            log.error("runException:{}", e);
        }
        isRunning = false;
    }

    private Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            //注意，改成配置参数 数据库地址和用户名、密码
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            log.error("-----------mysql get connection has exception , msg = {}", e.toString());
        }
        return con;
    }
}
