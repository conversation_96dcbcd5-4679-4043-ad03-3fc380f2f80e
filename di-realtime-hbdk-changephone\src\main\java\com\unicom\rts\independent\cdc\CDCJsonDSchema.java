// package com.unicom.rts.independent.cdc;
//
// import com.alibaba.fastjson.JSONObject;
// import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
// import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
// import org.apache.flink.api.common.typeinfo.TypeInformation;
// import org.apache.flink.util.Collector;
// import org.apache.kafka.connect.data.Field;
// import org.apache.kafka.connect.data.Struct;
// import org.apache.kafka.connect.source.SourceRecord;
//
// /**
//  * <AUTHOR>
//  * @description:
//  * @date 2022/4/19 10:23
//  */
// public class CDCJsonDSchema implements DebeziumDeserializationSchema<String> {
//
//     @Override
//     public void deserialize(SourceRecord sourceRecord, Collector<String> collector) throws Exception {
//         JSONObject resultJsonObject = new JSONObject();
//         Struct value = ((Struct) sourceRecord.value());
//         String operatorType = value.getString("op");
//         Struct source = value.getStruct("source");
//         String db = source.getString("db");
//         String table = source.getString("table");
//         // op --> c: create, u: update, d: delete, r: read
//         // 如果是读取、插入和更新，主要关注更新后after的数据
//         if (!"d".equals(operatorType)) {
//             Struct after = value.getStruct("after");
//             JSONObject afterJsonObject = parseRecord(after);
//             resultJsonObject.put("value", afterJsonObject);
//             // 更新状态，需要取出来before的内容
//             if ("u".equals(operatorType)) {
//                 Struct before = value.getStruct("before");
//                 JSONObject beforeJsonObject = parseRecord(before);
//                 resultJsonObject.put("before", beforeJsonObject);
//             }
//         }
//
//         // 如果是删除，主要关注删除前before的数据
//         if ("d".equals(operatorType)) {
//             Struct before = value.getStruct("before");
//             JSONObject beforeJsonObject = parseRecord(before);
//             resultJsonObject.put("value", beforeJsonObject);
//         }
//         //数据库表
//         resultJsonObject.put("table", db + "." + table);
//         resultJsonObject.put("op", operatorType);
//         collector.collect(resultJsonObject.toString());
//     }
//
//     @Override
//     public TypeInformation<String> getProducedType() {
//         return BasicTypeInfo.STRING_TYPE_INFO;
//     }
//
//     private JSONObject parseRecord(Struct struct) {
//         JSONObject jsonObject = new JSONObject();
//         for (Field field : struct.schema().fields()) {
//             switch ((field.schema()).type()) {
//                 case INT8:
//                     int resultInt8 = struct.getInt8(field.name());
//                     jsonObject.put(field.name(), resultInt8);
//                     break;
//                 case INT32:
//                     int resultInt32 = struct.getInt32(field.name());
//                     jsonObject.put(field.name(), resultInt32);
//                     break;
//                 case INT64:
//                     Long resultInt = struct.getInt64(field.name());
//                     jsonObject.put(field.name(), resultInt);
//                     break;
//                 case FLOAT32:
//                     Float resultFloat32 = struct.getFloat32(field.name());
//                     jsonObject.put(field.name(), resultFloat32);
//                     break;
//                 case FLOAT64:
//                     Double resultFloat64 = struct.getFloat64(field.name());
//                     jsonObject.put(field.name(), resultFloat64);
//                     break;
//                 case STRING:
//                     String resultStr = struct.getString(field.name());
//                     jsonObject.put(field.name(), resultStr);
//                     break;
//                 default:
//             }
//         }
//         return jsonObject;
//     }
//
// }
//
