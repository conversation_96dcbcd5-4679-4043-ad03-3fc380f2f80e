// package com.unicom.rts.independent.function;
//
//
// import com.unicom.rts.independent.bean.*;
// import com.unicom.rts.independent.bean.LocData;
// import com.unicom.rts.independent.util.DistanceUtil;
// import com.unicom.rts.independent.util.HbaseUtil;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.flink.api.common.state.*;
// import org.apache.flink.api.common.time.Time;
// import org.apache.flink.api.java.tuple.Tuple2;
// import org.apache.flink.api.java.utils.ParameterTool;
// import org.apache.flink.configuration.Configuration;
// import org.apache.flink.metrics.Gauge;
// import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
// import org.apache.flink.util.Collector;
// import org.apache.hadoop.hbase.TableName;
// import org.apache.hadoop.hbase.client.Connection;
// import org.apache.hadoop.hbase.client.HTable;
// import org.apache.hadoop.hbase.client.Put;
// import org.apache.hadoop.hbase.client.Result;
// import org.apache.hadoop.hbase.util.Bytes;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
//
//
// import java.io.IOException;
// import java.nio.charset.StandardCharsets;
//
// /**
//  * <AUTHOR>
//  */
// public class LacciComputeProcessCdc extends KeyedBroadcastProcessFunction<String, LocData, LacciCDCInfo, Tuple2<String, String>> {
//     private final static Logger logger = LoggerFactory.getLogger(LacciComputeProcessCdc.class);
//     private HbaseUtil hbaseUtil = null;
//     private HTable table = null;
//     private static final byte[] FAMILYNAME = "f".getBytes();
//     ParameterTool conf;
//     MapState<String, LocStateBean> locState;
//     private MapState<String, LacciCDCInfo> lacCiState;
//
//
//     private final transient long valueToExpose = 0L;
//
//     public LacciComputeProcessCdc(ParameterTool conf) {
//         this.conf = conf;
//     }
//
//     //广播描述
//     // ValueStateDescriptor<LacCiInfo> lacCiInfoStateDes = new ValueStateDescriptor<>("LacCiInfoState", LacCiInfo.class);
//     final MapStateDescriptor<String, LacciCDCInfo> broadcastConfig = new MapStateDescriptor<>(
//             "lacCiInfoState",
//             String.class,
//             LacciCDCInfo.class);
//
//
//
//     @Override
//     public void open(Configuration parameters) throws Exception {
//         super.open(parameters);
//         //初始化hbase链接
//         hbaseUtil = new HbaseUtil();
//         Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
//         table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));
//         //状态ttl
//         StateTtlConfig locTtlConfig = StateTtlConfig
//                 .newBuilder(Time.days(7))
//                 .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
//                 .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
//                 .cleanupInRocksdbCompactFilter(6000)
//                 .build();
//
//         //驻留触发
//         MapStateDescriptor<String, LocStateBean> locStateDes = new MapStateDescriptor<>("StayState", String.class, LocStateBean.class);
//         locStateDes.enableTimeToLive(locTtlConfig);
//         locState = getRuntimeContext().getMapState(locStateDes);
//
//         lacCiState = getRuntimeContext().getMapState(broadcastConfig);
//
//
//         //时延统计metric
//         getRuntimeContext()
//                 .getMetricGroup()
//                 .gauge("proDelay", new Gauge<Long>() {
//                     @Override
//                     public Long getValue() {
//                         return valueToExpose;
//                     }
//                 });
//     }
//
//     /**
//      * 接收上游loc流数据，用围栏ID关联本地规则，进行计算，满足规则的数据下发，添加下发的topic
//      *
//      * @param locData
//      * @param readOnlyContext
//      * @param collector
//      * @throws Exception
//      */
//
//     @Override
//     public void processElement(LocData locData, ReadOnlyContext readOnlyContext, Collector<Tuple2<String, String>> collector) throws Exception {
//         String deviceNumber = locData.getDeviceNumber();
//         LocStateBean lastState = locState.get(deviceNumber);
//         LacciCDCInfo lacciState = lacCiState.get(locData.getLacci());
//         double lon = 0L;
//         double lat = 0L;
//         if (StringUtils.isNotBlank(locData.getLat()) && StringUtils.isNotBlank(locData.getLon())) {
//             lon = Double.parseDouble(locData.getLon());
//             lat = Double.parseDouble(locData.getLat());
//         } else if (lacciState != null && StringUtils.isNotBlank(lacciState.getLat()) && StringUtils.isNotBlank(lacciState.getLon())){
//             lon = Double.parseDouble(lacciState.getLon());
//             lat = Double.parseDouble(lacciState.getLat());
//         } else {
//             return;
//         }
//
//         if (lastState == null) {
//             locState.put(deviceNumber, new LocStateBean(locData, lon, lat));
//             return;
//         } else {
//             String lastImei = lastState.getImei();
//             String newImei = locData.getImei();
//             if (lastImei.equals(newImei)) {
//                 return;
//             }
//             long lastTime = lastState.getTime();
//             long newTime = locData.getTime();
//             long timeDiff = newTime - lastTime;
//             if (timeDiff < 3600 * 1000) {
//                 return;
//             }
//             String key = "hbdk_" + deviceNumber + locData.getUserTagStateBean().getInDateMonth();
//             if (notSendOut(key)) {
//                 // 下发数据
//                 long distance = (long)DistanceUtil.getDistance(lon, lat, lastState.getLon(), lastState.getLat());
//
//                 OutDataBean out = new OutDataBean(locData, lastState, timeDiff, distance);
//                 collector.collect(new Tuple2<>(conf.get("sink.topic"), out.toString()));
//             }
//         }
//     }
//
//
//
//     @Override
//     public void processBroadcastElement(LacciCDCInfo lacci, Context ctx, Collector<Tuple2<String, String>> collector) throws Exception {
//         BroadcastState<String, LacciCDCInfo> lacciState = ctx.getBroadcastState(broadcastConfig);
//         String op = lacci.getOperation();
//
//         if ("u".equals(op) || "c".equals(op)) {
//             lacciState.put(lacci.getLacCi(), lacci);
//         } else if ("d".equals(op)) {
//             lacciState.remove(lacci.getLacCi());
//         } else if ("r".equals(op)) {
//             return;
//         } else {
//             logger.warn("lacci operation is else: " + lacci.toString());
//         }
//     }
//
//     public boolean notSendOut(String key) throws IOException {
//         //先查询，是否下发过，下发过result isEmpty true
//         Result result = hbaseUtil.getDataFromHbase(key, table);
//         //未下发过，需要下发，且向hbase中存储
//         if (result.isEmpty()) {
//             //负载均衡
//             Put put = new Put(Bytes.toBytes(key));
//             put.addColumn(FAMILYNAME, "key".getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8));
//             if (!put.isEmpty()) {
//                 table.put(put);
//             }
//         }
//         return result.isEmpty();
//     }
//
// }
