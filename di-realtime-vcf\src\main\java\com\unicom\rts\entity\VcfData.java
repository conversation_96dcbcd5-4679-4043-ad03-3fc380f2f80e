package com.unicom.rts.entity;

import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-04-28 09:29
 */

@Data
public class VcfData{

    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String SEPARATOR = "\u0001";

    private VcfJson vcfJson;

    private String dataReleaseTime;//下行kafka时间

    private String cityCode; //区县编码

    Set<String> topics;//本条数据下发的topic

    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap();

    public void parseHeader(ConsumerRecord<String,String> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, Bytes.toString(next.value()));
            }
        }
        headersMap.put("scene_id", "23");
        headersMap.put("root_time", String.valueOf((record.timestamp())));
        headersMap.put("process_time", String.valueOf(System.currentTimeMillis()));
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    @Override
    public String toString() {
        //下发字段
        StringBuilder sb = new StringBuilder();

        sb.append(vcfJson);
//        sb.append(SEPARATOR);
//        sb.append(DateFormatUtils.format(System.currentTimeMillis(), DATETIME_FORMAT));
//        sb.append(SEPARATOR);
//        sb.append(cityCode == null ? "" : cityCode);

        return sb.toString();
    }

}
