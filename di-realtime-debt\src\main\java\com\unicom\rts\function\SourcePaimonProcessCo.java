package com.unicom.rts.function;

import com.unicom.rts.entity.DebtData;
import com.unicom.rts.entity.PaimonBean;
import com.unicom.rts.entity.UserTagBean;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @create 9/27 1:22 PM
 */


public class SourcePaimonProcessCo extends KeyedCoProcessFunction<String, DebtData,PaimonBean , Tuple3<String, String, Iterable<Header>>> {

    private final static Logger logger = LoggerFactory.getLogger(SourcePaimonProcessCo.class);
    ParameterTool conf;

    private ValueState<PaimonBean> paimonInBeanValueState;
    private String outPutTopic;
    private ValueState<UserTagBean> userTagState;


    public SourcePaimonProcessCo(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        outPutTopic = conf.get("outPut.topic");
        //数据湖表状态
        ValueStateDescriptor<PaimonBean> payInBeanValueStateDes = new ValueStateDescriptor<>("PaimoonBeanValue", PaimonBean.class);
        paimonInBeanValueState = getRuntimeContext().getState(payInBeanValueStateDes);
        // 用户标签表状态
        ValueStateDescriptor<UserTagBean> userTagStateDes = new ValueStateDescriptor<>("userTagState", UserTagBean.class);
        userTagState = getRuntimeContext().getState(userTagStateDes);
    }

    @Override
    public void processElement1(DebtData data, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {

        PaimonBean paimonBeanvalue = paimonInBeanValueState.value();
        //标准场景1.匹配上下发 2.匹配不上扔掉
        if(paimonBeanvalue!=null && paimonBeanvalue.getPhoneNum()!=null && !"".equals(paimonBeanvalue.getPhoneNum())) {

            if ("tag".equals(data.getDataSource())) {
                userTagState.update(data.getUserTagBean());
                return;
            }
            UserTagBean userTag = userTagState.value();
            String provId = null;
            if (userTag != null) {
                data.setUserId(userTag.getUserId());
                data.setCityCode(userTag.getCityCode());
                provId = userTag.getProvId();
                data.setProvId(provId);
            }

            long time = System.currentTimeMillis();
            if(null != provId && 0 != provId.length()) {
                System.out.println("===>flink out ===>"+data.getOldStateCode()+"StateCode"+data.getStateCode()+"=====string===="+data.toString(time));
                collector.collect(new Tuple3<>(outPutTopic, data.toString(time), data.getHeaders(provId)));
            }else {
                System.out.println("flink out ===>"+"oldStateCode===>"+data.getOldStateCode()+"StateCode"+data.getStateCode()+"=====string===="+data.toString(time));
                collector.collect(new Tuple3<>(outPutTopic, data.toString(time), data.getHeaders()));
            }
        }
    }

    @Override
    public void processElement2(PaimonBean paimonBean, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        paimonInBeanValueState.update(paimonBean);
    }
}
