package com.unicom.rts.source;

import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.TConnect;
import com.unicom.rts.function.CustomDeSerializationSchema;
import com.unicom.rts.util.JdbcUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.config.SaslConfigs;
import com.unicom.rts.constant.CommonConstant;
import java.util.*;


public class TencentEKafkaSource {

    public static FlinkKafkaConsumer<ConsumerRecord<Object, Object>> getFlinkKafkaConsumer(ParameterTool parameters) throws Exception {

        String sourceId = parameters.get(CommonConstant.SOURCE_ID);
        String consumerSql = CommonConstant.CONSUMER_SQL.replace("#DatasourceId", sourceId);

        JdbcUtil jdbcUtil = new JdbcUtil();
        jdbcUtil.openMySqlConnection(parameters.get(CommonConstant.MYSQL_URL), parameters.get(CommonConstant.MYSQL_USER), parameters.get(CommonConstant.MYSQL_PASSWD));
        List<TConnect> tableArr = jdbcUtil.select(consumerSql, TConnect.class);
        if (tableArr.size() == 0) {
            throw new Exception("No consumer connect data obtained!");
        }
        TConnect tc = tableArr.get(0);
        String broker = tc.getIpCode();

        JSONObject connectMessage = JSONObject.parseObject(tc.getConnectMessage());
        String kafkaUser = connectMessage.getString("kafka_username");
        String kafkaPassword = connectMessage.getString("kafka_password");
        String isEncrpyt = connectMessage.getString("kafka_encrypt_type");
        String SASL_MECHANISM = connectMessage.getString("kafka_sasl_mechanism");
        String KAFKA_SECURITY = connectMessage.getString("kafka_security");
        String isTiangong = connectMessage.getString("kafka_tiangong");

        Set<String> topicSet = new HashSet<String>();
        List<String> topics = new ArrayList<>();
        tableArr.forEach(one -> {
            String[] topicArr = one.getTopic().split(";");
            for (int i = 0; i < topicArr.length; i++) {
                topicSet.add(topicArr[i]);
            }

                }
        );
        topicSet.forEach(one -> {
            topics.add(one);
        });

        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, broker);
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, parameters.get("group.id"));
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, parameters.get("enable.auto.commit"));
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, parameters.get("offset.reset"));
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
        properties.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        boolean sourceSecurity = false;
        if (!StringUtils.isBlank(isTiangong)) {
            sourceSecurity = Boolean.valueOf(isTiangong);
        }
        if ("1".equals(isEncrpyt)) {
            if (sourceSecurity) {
                //天宫kafka安全设置
                properties.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
                properties.setProperty("security.protocol", "SASL_PLAINTEXT");
                properties.setProperty("sasl.mechanism", "SCRAM-SHA-256");
            } else if ("1".equals(SASL_MECHANISM)) {
                if (StringUtils.isBlank(KAFKA_SECURITY)) {
                    KAFKA_SECURITY = "SASL_PLAINTEXT";
                }
                properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY);
                properties.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
                properties.put("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            } else if ("0".equals(SASL_MECHANISM)) {
                if (StringUtils.isBlank(KAFKA_SECURITY)) {
                    KAFKA_SECURITY = "SASL_PLAINTEXT";
                }
                properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY);
                properties.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
                properties.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            }
        }

        FlinkKafkaConsumer<ConsumerRecord<Object, Object>> tenConsumer = new FlinkKafkaConsumer(topics, new CustomDeSerializationSchema(), properties);
        tenConsumer.setCommitOffsetsOnCheckpoints(true);
        return tenConsumer;

    }
}