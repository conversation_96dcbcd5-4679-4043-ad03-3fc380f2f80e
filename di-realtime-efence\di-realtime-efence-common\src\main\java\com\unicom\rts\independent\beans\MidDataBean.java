package com.unicom.rts.independent.beans;

import lombok.Data;

import java.util.List;

@Data
public class MidDataBean {
    private String 	deviceNumber;	//手机号
    private String 	time;	//信令发生时间
    private String  hprovName;  //归属省份名称
    private String  hcityName;  //归属地市名称
    private String 	imei;	//imei
    private String 	imsi;	//imsi
    private String  currentTime; //当前处理时间
    private String 	lac;	//lac
    private String 	ci;	//ci
    private String 	longitude;	//经度
    private String 	latitude;	//纬度
    private String 	provId;	//发生省份id
    private String  poweroffInd; //关机标识
    private List<String> locModuleName; //此条记录归属高危基站的模块名称列表
    private List<String> imeiModuleName; //此条记录归属黑串号的模块名称列表

        @Override
    public String toString() {
        return deviceNumber +
                "\1" + time +
                "\1" + hprovName +
                "\1" + hcityName +
                "\1" + imei +
                "\1" + imsi +
                "\1" + currentTime +
                "\1" + lac +
                "\1" + ci +
                "\1" + longitude +
                "\1" + latitude +
                "\1" + provId +
                "\1" + poweroffInd;
    }
}
