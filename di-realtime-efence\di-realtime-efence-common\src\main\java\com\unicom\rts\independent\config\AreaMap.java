package com.unicom.rts.independent.config;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/24
 **/
public class AreaMap {

    public static HashMap<String, String> getAreaMap() {
        HashMap<String,String> areaMap = new HashMap<>();
        areaMap.put("V0110000","北京");
        areaMap.put("V0120000","天津");
        areaMap.put("V0130100","石家庄");
        areaMap.put("V0130200","唐山");
        areaMap.put("V0130300","秦皇岛");
        areaMap.put("V0130400","邯郸");
        areaMap.put("V0130500","邢台");
        areaMap.put("V0130600","保定");
        areaMap.put("V0130700","张家口");
        areaMap.put("V0130800","承德");
        areaMap.put("V0130900","沧州");
        areaMap.put("V0131000","廊坊");
        areaMap.put("V0132000","雄安新区");
        areaMap.put("V0133000","衡水");
        areaMap.put("V0140100","太原");
        areaMap.put("V0140200","大同");
        areaMap.put("V0140300","阳泉");
        areaMap.put("V0140400","长治");
        areaMap.put("V0140500","晋城");
        areaMap.put("V0140600","朔州");
        areaMap.put("V0142200","忻州");
        areaMap.put("V0142300","吕梁");
        areaMap.put("V0142400","晋中");
        areaMap.put("V0142600","临汾");
        areaMap.put("V0142700","运城");
        areaMap.put("V0150100","呼和浩特");
        areaMap.put("V0150200","包头");
        areaMap.put("V0150300","乌海");
        areaMap.put("V0150400","赤峰");
        areaMap.put("V0152101","海拉尔");
        areaMap.put("V0152200","兴安盟");
        areaMap.put("V0152301","通辽");
        areaMap.put("V0152500","锡林郭勒");
        areaMap.put("V0152600","乌兰察布");
        areaMap.put("V0152700","鄂尔多斯");
        areaMap.put("V0152800","巴彦淖尔");
        areaMap.put("V0152900","阿盟");
        areaMap.put("V0210100","沈阳");
        areaMap.put("V0210200","大连");
        areaMap.put("V0210300","鞍山");
        areaMap.put("V0210400","抚顺");
        areaMap.put("V0210500","本溪");
        areaMap.put("V0210600","丹东");
        areaMap.put("V0210700","锦州");
        areaMap.put("V0210800","营口");
        areaMap.put("V0210900","阜新");
        areaMap.put("V0211000","辽阳");
        areaMap.put("V0211100","盘锦");
        areaMap.put("V0211200","铁岭");
        areaMap.put("V0211300","朝阳");
        areaMap.put("V0211400","葫芦岛");
        areaMap.put("V0220100","长春");
        areaMap.put("V0220200","吉林");
        areaMap.put("V0220300","四平");
        areaMap.put("V0220400","辽源");
        areaMap.put("V0220500","通化");
        areaMap.put("V0220600","白山");
        areaMap.put("V0220700","松原");
        areaMap.put("V0220800","白城");
        areaMap.put("V0222400","延边");
        areaMap.put("V0230100","哈尔滨");
        areaMap.put("V0230200","齐齐哈尔");
        areaMap.put("V0230300","鸡西");
        areaMap.put("V0230400","鹤岗");
        areaMap.put("V0230500","双鸭山");
        areaMap.put("V0230600","大庆");
        areaMap.put("V0230700","伊春");
        areaMap.put("V0230800","佳木斯");
        areaMap.put("V0230900","七台河");
        areaMap.put("V0231000","牡丹江");
        areaMap.put("V0231100","黑河");
        areaMap.put("V0232300","绥化");
        areaMap.put("V0232700","大兴安岭");
        areaMap.put("V0310000","上海");
        areaMap.put("V0320100","南京");
        areaMap.put("V0320200","无锡");
        areaMap.put("V0320300","徐州");
        areaMap.put("V0320400","常州");
        areaMap.put("V0320500","苏州");
        areaMap.put("V0320600","南通");
        areaMap.put("V0320700","连云港");
        areaMap.put("V0320800","淮安");
        areaMap.put("V0320881","宿迁");
        areaMap.put("V0320900","盐城");
        areaMap.put("V0321000","扬州");
        areaMap.put("V0321082","泰州");
        areaMap.put("V0321100","镇江");
        areaMap.put("V0330100","杭州");
        areaMap.put("V0330200","宁波");
        areaMap.put("V0330300","温州");
        areaMap.put("V0330400","嘉兴");
        areaMap.put("V0330500","湖州");
        areaMap.put("V0330600","绍兴");
        areaMap.put("V0330700","金华");
        areaMap.put("V0330800","衢州");
        areaMap.put("V0330900","舟山");
        areaMap.put("V0332500","丽水");
        areaMap.put("V0332600","台州");
        areaMap.put("V0340100","合肥");
        areaMap.put("V0340200","芜湖");
        areaMap.put("V0340300","蚌埠");
        areaMap.put("V0340400","淮南");
        areaMap.put("V0340500","马鞍山");
        areaMap.put("V0340600","淮北");
        areaMap.put("V0340700","铜陵");
        areaMap.put("V0340800","安庆");
        areaMap.put("V0341000","黄山");
        areaMap.put("V0341100","滁州");
        areaMap.put("V0342100","阜阳");
        areaMap.put("V0342200","宿州");
        areaMap.put("V0342400","六安");
        areaMap.put("V0342500","宣城");
        areaMap.put("V0342600","巢湖");
        areaMap.put("V0342900","池州");
        areaMap.put("V0343000","亳州");
        areaMap.put("V0350100","福州");
        areaMap.put("V0350200","厦门");
        areaMap.put("V0350300","莆田");
        areaMap.put("V0350400","三明");
        areaMap.put("V0350500","泉州");
        areaMap.put("V0350600","漳州");
        areaMap.put("V0352100","南平");
        areaMap.put("V0352200","宁德");
        areaMap.put("V0352600","龙岩");
        areaMap.put("V0360100","南昌");
        areaMap.put("V0360200","景德镇");
        areaMap.put("V0360300","萍乡");
        areaMap.put("V0360400","九江");
        areaMap.put("V0360500","新余");
        areaMap.put("V0360600","鹰潭");
        areaMap.put("V0362100","赣州");
        areaMap.put("V0362200","宜春");
        areaMap.put("V0362233","上饶");
        areaMap.put("V0362400","吉安");
        areaMap.put("V0362500","抚州");
        areaMap.put("V0370100","济南");
        areaMap.put("V0370200","青岛");
        areaMap.put("V0370300","淄博");
        areaMap.put("V0370400","枣庄");
        areaMap.put("V0370500","东营");
        areaMap.put("V0370600","烟台");
        areaMap.put("V0370700","潍坊");
        areaMap.put("V0370800","济宁");
        areaMap.put("V0370900","泰安");
        areaMap.put("V0371000","威海");
        areaMap.put("V0371100","日照");
        areaMap.put("V0371200","莱芜");
        areaMap.put("V0372300","滨州");
        areaMap.put("V0372401","德州");
        areaMap.put("V0372500","聊城");
        areaMap.put("V0372801","临沂");
        areaMap.put("V0372900","菏泽");
        areaMap.put("V0410100","郑州");
        areaMap.put("V0410200","开封");
        areaMap.put("V0410300","洛阳");
        areaMap.put("V0410400","平顶山");
        areaMap.put("V0410500","安阳");
        areaMap.put("V0410600","鹤壁");
        areaMap.put("V0410700","新乡");
        areaMap.put("V0410800","焦作");
        areaMap.put("V0410880","济源");
        areaMap.put("V0410900","濮阳");
        areaMap.put("V0411000","许昌");
        areaMap.put("V0411100","漯河");
        areaMap.put("V0411200","三门峡");
        areaMap.put("V0412300","商丘");
        areaMap.put("V0412700","周口");
        areaMap.put("V0412800","济源");
        areaMap.put("V0412801","驻马店");
        areaMap.put("V0412880","济源");
        areaMap.put("V0412901","南阳");
        areaMap.put("V0413000","信阳");
        areaMap.put("V0420100","武汉");
        areaMap.put("V0420200","黄石");
        areaMap.put("V0420500","宜昌");
        areaMap.put("V0420600","襄阳");
        areaMap.put("V0420681","随州");
        areaMap.put("V0420700","鄂州");
        areaMap.put("V0420800","荆门");
        areaMap.put("V0420900","孝感");
        areaMap.put("V0422100","黄冈");
        areaMap.put("V0422300","咸宁");
        areaMap.put("V0422400","荆州");
        areaMap.put("V0422401","江汉");
        areaMap.put("V0422600","十堰");
        areaMap.put("V0422800","恩施");
        areaMap.put("V0430100","长沙");
        areaMap.put("V0430200","株洲");
        areaMap.put("V0430300","湘潭");
        areaMap.put("V0430400","衡阳");
        areaMap.put("V0430500","邵阳");
        areaMap.put("V0430600","岳阳");
        areaMap.put("V0430700","常德");
        areaMap.put("V0430800","张家界");
        areaMap.put("V0430900","益阳");
        areaMap.put("V0431000","郴州");
        areaMap.put("V0431100","永州");
        areaMap.put("V0431200","怀化");
        areaMap.put("V0432500","娄底");
        areaMap.put("V0433100","湘西自治州");
        areaMap.put("V0440100","广州");
        areaMap.put("V0440201","韶关");
        areaMap.put("V0440300","深圳");
        areaMap.put("V0440400","珠海");
        areaMap.put("V0440510","汕头");
        areaMap.put("V0440600","佛山");
        areaMap.put("V0440700","江门");
        areaMap.put("V0440800","湛江");
        areaMap.put("V0440901","茂名");
        areaMap.put("V0441201","肇庆");
        areaMap.put("V0441281","云浮");
        areaMap.put("V0441300","惠州");
        areaMap.put("V0441401","梅州");
        areaMap.put("V0441500","汕尾");
        areaMap.put("V0441600","河源");
        areaMap.put("V0441700","阳江");
        areaMap.put("V0441800","清远");
        areaMap.put("V0441900","东莞");
        areaMap.put("V0442000","中山");
        areaMap.put("V0445100","潮州");
        areaMap.put("V0445201","揭阳");
        areaMap.put("V0450100","南宁");
        areaMap.put("V0450200","柳州");
        areaMap.put("V0450300","桂林");
        areaMap.put("V0450400","梧州");
        areaMap.put("V0450500","北海");
        areaMap.put("V0450600","防城港");
        areaMap.put("V0452500","玉林");
        areaMap.put("V0452600","百色");
        areaMap.put("V0452700","河池");
        areaMap.put("V0452800","钦州");
        areaMap.put("V0452900","贵港");
        areaMap.put("V0453000","贺州");
        areaMap.put("V0453100","来宾");
        areaMap.put("V0453200","崇左");
        areaMap.put("V0460003","儋州");
        areaMap.put("V04600031"," 东方");
        areaMap.put("V04600032"," 临高");
        areaMap.put("V04600033"," 昌江");
        areaMap.put("V04600034"," 白沙");
        areaMap.put("V0460100","海口");
        areaMap.put("V04601001"," 琼山");
        areaMap.put("V04601002"," 琼海");
        areaMap.put("V04601003"," 文昌");
        areaMap.put("V04601004"," 万宁");
        areaMap.put("V04601005"," 定安");
        areaMap.put("V04601006"," 澄迈");
        areaMap.put("V04601007"," 屯昌");
        areaMap.put("V04601008"," 琼中");
        areaMap.put("V0460200","三亚");
        areaMap.put("V04602001"," 乐东");
        areaMap.put("V04602002"," 陵水");
        areaMap.put("V04602003"," 保亭");
        areaMap.put("V04602004"," 五指山");
        areaMap.put("V0500000","重庆");
        areaMap.put("V0510100","成都");
        areaMap.put("V0510300","自贡");
        areaMap.put("V0510400","攀枝花");
        areaMap.put("V0510500","泸州");
        areaMap.put("V0510600","德阳");
        areaMap.put("V0510700","绵阳");
        areaMap.put("V0510800","广元");
        areaMap.put("V0510900","遂宁");
        areaMap.put("V0511000","内江");
        areaMap.put("V0511100","乐山");
        areaMap.put("V0511300","南充");
        areaMap.put("V0512500","宜宾");
        areaMap.put("V0513000","达州");
        areaMap.put("V0513100","雅安");
        areaMap.put("V0513200","阿坝自治州");
        areaMap.put("V0513300","甘孜自治州");
        areaMap.put("V0513400","凉山自治州");
        areaMap.put("V0513600","广安");
        areaMap.put("V0513700","巴中");
        areaMap.put("V0513800","眉山");
        areaMap.put("V0513900","资阳");
        areaMap.put("V0520100","贵阳");
        areaMap.put("V0520200","六盘水");
        areaMap.put("V0522100","遵义");
        areaMap.put("V0522200","铜仁");
        areaMap.put("V0522400","毕节");
        areaMap.put("V0522500","安顺");
        areaMap.put("V0522800","贵安新区");
        areaMap.put("V0522900","黔西南州");
        areaMap.put("V0523100","黔东南州");
        areaMap.put("V0523200","黔南州");
        areaMap.put("V0530100","昆明");
        areaMap.put("V0530200","东川");
        areaMap.put("V0532100","昭通");
        areaMap.put("V0532200","曲靖");
        areaMap.put("V0532300","楚雄");
        areaMap.put("V0532400","玉溪");
        areaMap.put("V0532500","红河");
        areaMap.put("V0532600","文山");
        areaMap.put("V0532800","西双版纳");
        areaMap.put("V0532900","大理");
        areaMap.put("V0533000","保山");
        areaMap.put("V0533100","德宏");
        areaMap.put("V0533200","丽江");
        areaMap.put("V0533300","怒江");
        areaMap.put("V0533400","迪庆");
        areaMap.put("V0533500","临沧");
        areaMap.put("V0533600","普洱");
        areaMap.put("V0540100","拉萨");
        areaMap.put("V0542100","昌都");
        areaMap.put("V0542200","山南");
        areaMap.put("V0542300","日喀则");
        areaMap.put("V0542400","那曲");
        areaMap.put("V0542500","阿里");
        areaMap.put("V0542600","林芝");
        areaMap.put("V0610100","西安");
        areaMap.put("V0610200","铜川");
        areaMap.put("V0610300","宝鸡");
        areaMap.put("V0610400","咸阳");
        areaMap.put("V0612101","渭南");
        areaMap.put("V0612301","汉中");
        areaMap.put("V0612401","安康");
        areaMap.put("V0612501","商洛");
        areaMap.put("V0612601","延安");
        areaMap.put("V0612701","榆林");
        areaMap.put("V0620100","兰州");
        areaMap.put("V0620200","嘉峪关");
        areaMap.put("V0620300","金昌");
        areaMap.put("V0620400","白银");
        areaMap.put("V0620500","天水");
        areaMap.put("V0622100","酒泉");
        areaMap.put("V0622201","张掖");
        areaMap.put("V0622300","武威");
        areaMap.put("V0622400","定西");
        areaMap.put("V0622600","陇南");
        areaMap.put("V0622700","平凉");
        areaMap.put("V0622800","庆阳");
        areaMap.put("V0622901","临夏");
        areaMap.put("V0623000","甘南");
        areaMap.put("V0630100","西宁");
        areaMap.put("V0632100","海东地区");
        areaMap.put("V0632200","海北自治州");
        areaMap.put("V0632300","黄南自治州");
        areaMap.put("V0632500","海南自治州");
        areaMap.put("V0632600","果洛自治州");
        areaMap.put("V0632700","玉树自治州");
        areaMap.put("V0632800","海西自治州");
        areaMap.put("V0632801","格尔木");
        areaMap.put("V0640100","银川");
        areaMap.put("V0640200","石嘴山");
        areaMap.put("V0642100","吴忠");
        areaMap.put("V0642200","固原");
        areaMap.put("V0642300","中卫");
        areaMap.put("V0650100","乌鲁木齐");
        areaMap.put("V0650200","克拉玛依");
        areaMap.put("V0650300","博州");
        areaMap.put("V0650400","伊犁");
        areaMap.put("V0650500","巴州");
        areaMap.put("V0650600","克州");
        areaMap.put("V0652101","吐鲁番");
        areaMap.put("V0652201","哈密");
        areaMap.put("V0652301","昌吉");
        areaMap.put("V0652901","阿克苏");
        areaMap.put("V0653101","喀什");
        areaMap.put("V0653201","和田");
        areaMap.put("V0654001","奎屯");
        areaMap.put("V0654201","塔城");
        areaMap.put("V0654301","阿勒泰");
        areaMap.put("V0659001","石河子");
        return areaMap;
    }
}
