package com.unicom.realtime.bean.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


/**
 * <AUTHOR>
 * @create 2022-03-31 11:32
 */

@AllArgsConstructor
@Getter
public enum FloEnum {

    /**
     * 用户用量的枚举信息
     */
    deviceNumber(0,"手机号码"),
    totalUseFlux(1,"使用总流量"),
    totalUseLocalFlux(2,"使用本地流量"),
    totalUseProvFlux(3,"使用省内流量"),
    totalUseContFlux(4,"使用国内流量"),
    totalUseIdleProvFlux(5,"使用省内闲时流量"),
    provSaturation(6,"本地流量饱和度"),
    diffprovSaturation(7,"省内流量饱和度"),
    countrySaturation(8,"国内流量饱和度"),
    diffprovIdleSaturation(9,"省内闲时流量饱和度"),
    dealTime(10,"发生时间"),
    provId(11,"省份Id"),
    inTime(12,"上行kafka时间"),
    dataSource(13,"数据源"),
    provBalance(14,"本地可用剩余流量"),
    diffprovBalance(15,"省内可用剩余流量"),
    contBalance(16,"国内可用剩余流量"),
    restIdleProvFlux(17,"省内闲时剩余流量"),
    cdhTime(18,"实时模块接收时间");

    private final Integer code;

    private final String msg;


    /**
     *获取枚举类
     * @param code
     * @return
     */
    public static FloEnum getByCode(Integer code) {
        return Arrays.stream(FloEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .orElse(null);

    }

    /**
     * 根据描述获取枚举类
     * @param msg
     * @return
     */
    public static FloEnum getByMsg(String msg) {
        return Arrays.stream(FloEnum.values())
                .filter(item -> item.getMsg().equals(msg))
                .findAny()
                .orElse(null);
    }
}
