package com.unicom.rts.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class JdbcUtil {

    private final static Logger logger = LoggerFactory.getLogger(JdbcUtil.class);

    private Connection connection;

    static {
        try {
            // 加载MySQL 8.0 以下版本 - JDBC 驱动名
            Class.forName("com.mysql.jdbc.Driver");
            //MySQL 8.0 以上版本 - JDBC 驱动名
            //Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public void openMySqlConnection(String host, String user, String pwd) {
        try {
            if (Objects.nonNull(this.connection) && !this.connection.isClosed()) {
                this.connection.close();
            }
            // MySQL 8.0 以下版本 - 数据库URL
            String dburl = host;
            // MySQL 8.0 以上版本 - 数据库URL
            //String dburl = "***************************/" + dbName + "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
            if (Objects.nonNull(host)) {
                dburl =  host ;
            }
            this.connection = DriverManager.getConnection(dburl, user, pwd);

            if (this.connection == null) {

                System.out.println("Database connection failed！");

            } else {

                System.out.println("Database connection succeeded");

            }
        } catch (SQLException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public List select(String sql, Class clazz)  {

        List ret = new ArrayList<>();
        PreparedStatement stmt = null;
        ResultSet result = null;
        try {

             stmt = this.connection.prepareStatement(sql);
             result = stmt.executeQuery();
            ResultSetMetaData data = result.getMetaData();
            int columnCount = data.getColumnCount();
            String[] columnNames = new String[columnCount];
            for (int i = 1; i <= data.getColumnCount(); i++) {
                // 获得列名
                columnNames[i - 1] = camelName(data.getColumnName(i));
            }
            //业务对象的属性数组
            Field[] fields = clazz.getDeclaredFields();

            while (result.next()) {

                //构造业务对象实体
                Object obj = clazz.newInstance();

                for (int i = 1; i <= columnCount; i++) {
                    for (int j = 0; j < fields.length; j++) {
                        Field f = fields[j];
                        if (f.getName().equalsIgnoreCase(columnNames[i - 1])) {
                            boolean flag = f.isAccessible();
                            f.setAccessible(true);
                            f.set(obj, result.getString(i));
                            f.setAccessible(flag);
                        }
                    }
                }
                ret.add(obj);
            }
        } catch (SQLException | InstantiationException | IllegalAccessException e) {
            logger.error( e.getMessage());
        }finally {
                if(result != null){
                    try {
                        result.close();
                    } catch (SQLException e) {
                        logger.error("SQLException:{}", e.getMessage());
                    }
                }
                if(stmt != null){
                    try {
                        stmt.close();
                    } catch (SQLException e) {
                        logger.error("SQLException:{}", e.getMessage());
                    }
                }
        }
        return ret;
    }
    public void close() {
        try {
            if (connection != null) {
                connection.close();
            }
        } catch (Exception e) {
            logger.error("runException:{}", e.getMessage());
        }
    }

    /**
     * 将下划线大写方式命名的字符串转换为驼峰式。
     * 如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。</br>
     * 例如：hello_world->helloWorld
     * @param name
     * 转换前的下划线大写方式命名的字符串
     * @return 转换后的驼峰式命名的字符串
     */
    public static String camelName(String name) {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!name.contains("_")) {
            // 不含下划线，仅将首字母小写
            return name.substring(0, 1).toLowerCase() + name.substring(1).toLowerCase();
        }
        // 用下划线将原始字符串分割
        String camels[] = name.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 处理真正的驼峰片段
            if (result.length() == 0) {
                // 第一个驼峰片段，全部字母都小写
                result.append(camel.toLowerCase());
            } else {
                // 其他的驼峰片段，首字母大写
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }
}
