package com.unicom.realtime.sink;

/*@Slf4j
public class XCloudSink extends RichSinkFunction<List<WisdNpData>> {
    private final static Logger logger = LoggerFactory.getLogger(XCloudSink.class);

    private static Connection conn = null;
    private static PreparedStatement ps = null;
    private static ResultSet rs = null;
    private ParameterTool conf;

    private Long startDate;
    private Long executeSqlDate;
    private Long enDate;

    public XCloudSink(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        Class.forName("com.bonc.xcloud.jdbc.XCloudDriver").newInstance();
        final String jdbcUrl = getCirroDataDriverUrl();
        logger.info("wisd_np->>get jdbcUrl:{}", jdbcUrl);
        conn = getConnection(jdbcUrl, this.conf.get("sink.xcloud.username"), this.conf.get("sink.xcloud.password"));

        //TODO 增加连接日志
        startDate = System.currentTimeMillis();
        Date date = new Date(startDate);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        logger.info("wisd_np->getConnection：{} ", conn);
        logger.info("wisd_np->getConnection Date：{} ", sd.format(date));

        String sql = conf.get("sink.xcloud.sql");
        if(conn != null){
            ps = conn.prepareStatement(sql);
        }

    }

    @Override
    public void invoke(List<WisdNpData> values, Context context) {
        try {


            executeSqlDate = System.currentTimeMillis();
            Date date = new Date(executeSqlDate);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logger.info("wisd_np->invode Date：{} ", sd.format(date));

            //使用时先判断连接是否断开


            // 每 3000 条 执行一次
            for(int i = 0 ; i < values.size(); i++){
                WisdNpData value = values.get(i);
                ps.setString(1,value.getProvince_code());
                ps.setString(2,value.getEparchy_code());
                ps.setLong(3,value.getUser_id());
                ps.setString(4,value.getSerial_number());
                ps.setString(5,value.getParent_chnl_kind_name());
                ps.setString(6,value.getChnl_code());
                ps.setString(7,value.getChnl_name());
                ps.setString(8,value.getHome_net());
                ps.setString(9,value.getPort_out_net());
                ps.setString(10,value.getPort_in_net());
                ps.setLong(11,value.getPort_time());
                ps.setLong(12,value.getOpen_date());
                ps.setLong(13,value.getProduct_id());
                ps.setString(14,value.getProduct_name());
                ps.setString(15,value.getNet_type());
                ps.setString(16,value.getPort_date());
                ps.addBatch();
                if((i !=0 && i % Integer.parseInt(conf.get("slink.xcloud.split.num")) == 0) || i == (values.size() - 1)){
                    //TODO
                    logger.info("wisd_np->invode values：{} ", ps);

                    ps.executeBatch();
                    ps.clearBatch();
                }
            }
        } catch (SQLException e) {
//            e.printStackTrace();
            log.error("wisd_np->>xCloud invokeException:{} "+e.getMessage());
        }
    }

    @Override
    public void close(){
        System.out.println("11111111111111111111111111111111111111111");
        enDate = System.currentTimeMillis();
        Date date = new Date(enDate);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        logger.info("wisd_np->close Date：{} ", sd.format(date));
        logger.info("wisd_np->during Date：{} ", enDate - startDate);

        try {
            super.close();
            if (rs != null) {
                rs.close();
            }
            if (ps != null){
                ps.close();
            }
            if (conn != null){
                conn.close();
            }
        } catch (Exception e) {
            logger.error("wisd_np->>xCloud runException:{} "+e.getMessage());
        }
    }

    *//**
     * 获取行云连接串
     * @return 返回行云连接串
     *//*
    private String getCirroDataDriverUrl() {
        String url = "";
        final String iplist = conf.get("sink.xcloud.ipList");
        final String port = conf.get("sink.xcloud.portList");
        final String dbName = conf.get("sink.xcloud.dbname");
        String connectRetry = conf.get("sink.xcloud.connect.retry");
        if (connectRetry == null) {
            connectRetry = "3";
        }
        String socketTimeOut = conf.get("sink.xcloud.socket.timeout");
        if (socketTimeOut == null) {
            socketTimeOut = "7200000";
        }
        String connectDirect = conf.get("sink.xcloud.connect.direct");
        if (connectDirect == null) {
            connectDirect = "true";
        }
        String buffMemory = conf.get("sink.xcloud.buffmemory");;
        if (buffMemory != null) {
            final String[] buffM = buffMemory.split("\\*");
            int n = 1;
            for (int x = 0; x < buffM.length; ++x) {
                n *= Integer.parseInt(buffM[x]);
            }
            buffMemory = new StringBuilder(String.valueOf(n)).toString();
        }
        else {
            buffMemory = "16777216";
        }
        final String[] hostSplit = iplist.split(",");
        final StringBuilder sb = new StringBuilder();
        sb.append("jdbc:xcloud:");
        String[] array;
        for (int length = (array = hostSplit).length, i = 0; i < length; ++i) {
            final String hostStr = array[i];
            sb.append("@").append(hostStr).append(":").append(port).append("/").append(dbName);
        }
        sb.append("?");
        sb.append("connectRetry=").append(connectRetry);
        sb.append("&socketTimeOut=").append(socketTimeOut);
        sb.append("&connectDirect=").append(connectDirect);
        sb.append("&buffMemory=").append(buffMemory);
        url = sb.toString();
        return url;
    }

    private Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            logger.error("wisd_np->>XCloud get connection has exception , msg = {}", e.getMessage());
        }
        return con;
    }
}*/

