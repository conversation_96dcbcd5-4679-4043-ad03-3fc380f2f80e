package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.RuleBean;
import com.unicom.rts.independent.beans.RecordBean;
import com.unicom.rts.independent.processor.Processor;
import com.unicom.rts.independent.processor.processorFactory.ProcessorFactory;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.unicom.rts.independent.config.AreaMap.getAreaMap;
import static com.unicom.rts.independent.config.ProvMap.getProvMap;
import static com.unicom.rts.independent.utils.MapUtil.fileToMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/
public class EfenceComputeProcess extends KeyedBroadcastProcessFunction<String, RecordBean, Map<String, RuleBean>, Tuple2<String, String>> {

    private ParameterTool hbaseConf;
    private Connection hbaseConnect;
    private HTable hTableRtsUserTag;
    private String userTagColumnFamily;
    private MapState<String, String> checkoutState;
    private Map<String, RuleBean> ruleMap;
    private HashMap<String,String> provMap = new HashMap<>();
    private HashMap<String,String> areaMap = new HashMap<>();

    public EfenceComputeProcess(ParameterTool hbaseConf) {
        this.hbaseConf = hbaseConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        String zkHost = hbaseConf.get("hbase.zookeeper");
        String zkPort = hbaseConf.get("hbase.zookeeper.port");
        hbaseConnect = HbaseUtil.gchConnectHbase(zkHost, zkPort, hbaseConf.get("hbase.user"));
        hTableRtsUserTag = HbaseUtil.getTable(hbaseConnect, hbaseConf.get("hbase.tablename"));
        userTagColumnFamily = hbaseConf.get("hbase.columnfamily");
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(1))
                .setUpdateType(StateTtlConfig.UpdateType.OnReadAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        MapStateDescriptor stateDescriptor =
                new MapStateDescriptor("checkOutState", Types.STRING, Types.STRING);
        stateDescriptor.enableTimeToLive(ttlConfig);
        checkoutState = getRuntimeContext().getMapState(stateDescriptor);
        ruleMap = new HashMap<>();
        provMap = getProvMap();
        areaMap = getAreaMap();
    }

    @Override
    public void processElement(RecordBean recordBean, ReadOnlyContext ctx, Collector<Tuple2<String, String>> out) throws Exception {
        //根据topic获取不同的处理类并进行过滤
        String sourceTopic = recordBean.getTopic();
        RuleBean ruleBean = ruleMap.get(sourceTopic);

        if (null != sourceTopic && !"".equals(sourceTopic) && null != ruleBean) {
            Processor processor = ProcessorFactory.getProcessor(ruleBean.getRuleType());
            processor.process(recordBean, ruleBean, hTableRtsUserTag, userTagColumnFamily, checkoutState, provMap, areaMap, out);
        }
    }

    @Override
    public void processBroadcastElement(Map<String, RuleBean> ruleMap, Context ctx, Collector<Tuple2<String, String>> out) throws Exception {
        this.ruleMap = ruleMap;
    }
}
