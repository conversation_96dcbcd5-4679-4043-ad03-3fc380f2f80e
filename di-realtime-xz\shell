
--xz.source.broker 10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092
--xz.enable.auto.commit true
--xz.offset.reset latest
--xz.source.kafka.security false
--xz.kafka.user sscjzx-kfk-wzgj-cluster
--xz.kafka.password dGlhbkdvbmcjMTIz

--xz.group.id xz_inde_test

--xz.topic CB_TF_B_TRADE
--xz.source.parallelism 30

--checkpointDataUri hdfs://slfn2/user/hh_slfn2_sschj_gray/flink/checkpoints/dp-independent-xz/
--sink.parallelism 60

--batch.size 16384
--request.timeout.ms 120000
--sink.kafka.bootstrap 10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092
--sink.kafka.security true
--sasl.mechanism PLAIN
--security.protocol SASL_PLAINTEXT
--sasl.jaas.config org.apache.kafka.common.security.plain.PlainLoginModule
--sink.kafka.user context
--sink.kafka.password context
--sink.parallelism 16



--outPut.topic JL_XZ_DATA
--kafkaConsumerStartFrom latest

--APP_ID 6YEacacCkA
--APP_SECRET vaRn92L6lg7y1r6IZWh3NfJQufwFnm2t
--url http://10.245.25.157:8000/api/microservice/custs/checkoutBusinp/v1