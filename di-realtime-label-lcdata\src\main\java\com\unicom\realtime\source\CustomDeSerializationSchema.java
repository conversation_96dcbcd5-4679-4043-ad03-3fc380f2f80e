package com.unicom.realtime.source;

import lombok.Data;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
public class CustomDeSerializationSchema implements KafkaDeserializationSchema<ConsumerRecord<String, String>> {

    private static final Logger logger = LoggerFactory.getLogger(CustomDeSerializationSchema.class);

    @Override
    public boolean isEndOfStream(ConsumerRecord<String, String> nextElement) {
        return false;
    }

    //这里返回一个ConsumerRecord<String,String>类型的数据，除了原数据还包括topic,offset,partition等信息
    @Override
    public ConsumerRecord<String, String> deserialize(ConsumerRecord<byte[], byte[]> record) throws Exception {
        try {
            String key = getStringSafe(record.key());
            String value = getStringSafe(record.value());
            
            // 增加空值过滤
            if (value == null || value.trim().isEmpty()) {
                return null; // Flink会自动过滤null记录
            }
            
            return new ConsumerRecord<>(
                record.topic(),
                record.partition(),
                record.offset(),
                key,
                value
            );
        } catch (Exception e) {
            logger.error("反序列化异常", e);
            return null; // 返回null记录会被自动过滤
        }
    }

    //指定数据的输入类型
    @Override
    public TypeInformation<ConsumerRecord<String, String>> getProducedType() {
        return TypeInformation.of(new TypeHint<ConsumerRecord<String, String>>() {
        });
    }

    // 优化后的安全字符串转换方法
    private String getStringSafe(byte[] data) {
        if (data == null || data.length == 0) {
            return "";
        }
        try {
            return new String(data, StandardCharsets.UTF_8);
        } catch (Exception e) {
            return "";
        }
    }
}
