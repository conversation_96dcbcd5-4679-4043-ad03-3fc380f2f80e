package com.unicom.realtime.main;

import com.unicom.realtime.bean.OrderTag;
import com.unicom.realtime.function.ComputeProcess;
import com.unicom.realtime.source.OrderDeliveryStream;
import com.unicom.realtime.source.OrderItemStream;
import com.unicom.realtime.source.OrderLineStream;
import com.unicom.realtime.source.OrderStream;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.bridge.java.StreamStatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

public class MainJob {
    private final static Logger logger = LoggerFactory.getLogger(MainJob.class);

    public static void main(String[] args) throws Exception {
        StreamTableEnvironment tableEnv;
        StreamStatementSet stmtSet;
        ParameterTool conf = ParameterTool.fromArgs(args);
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(conf);
        //状态后端
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.disableOperatorChaining();

        //checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 5分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(5));
        // 超时时间 10 分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(20));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        DataStream<OrderTag> ocOrderStreamDataStream = new OrderStream().source(env, conf);
        DataStream<OrderTag> orderItemStreamDataStream = new OrderItemStream().source(env, conf);
        DataStream<OrderTag> orderDeliveryStreamDataStream = new OrderDeliveryStream().source(env, conf);
        DataStream<OrderTag> orderLineStreamDataStream = new OrderLineStream().source(env, conf);

        DataStream<OrderTag> outStream = ocOrderStreamDataStream
                .union(orderItemStreamDataStream)
                .union(orderDeliveryStreamDataStream)
                .union(orderLineStreamDataStream)
                .keyBy(OrderTag::getOrderId)
                .process(new ComputeProcess(conf)).uid("computeProcess").name("computeProcess")
                .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        logger.info("stream start >>>>>>>>>>>>> ");

        tableEnv = StreamTableEnvironment.create(env);
        // access flink configuration after table environment instantiation
        TableConfig tableConfig = tableEnv.getConfig();
        // set low-level key-value options
        tableConfig.set("table.exec.sink.upsert-materialize", "NONE");
        tableConfig.set("table.exec.sink.not-null-enforce", "DROP");
        // create paimon catalog
        tableEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + conf.get("warehouse") + "',\n" +
                "    'default-database' = '" + conf.get("default-database") + "'\n" +
                ");");
        tableEnv.executeSql("USE CATALOG paimon_catalog");
        // run multiple INSERT queries on the registered source table and emit the result to registered sink tables
        stmtSet = tableEnv.createStatementSet();

        DataStream<Row> rowDataStream = outStream.flatMap((FlatMapFunction<OrderTag, Row>) (orderTag, collector) -> {
            LocalDateTime now = LocalDateTime.now();
            Row row = Row.withPositions(4);
            row.setField(0, orderTag.getOrderId());
            row.setField(1, orderTag.getSerialNumber());
            row.setField(2, orderTag.getTagValue());
            row.setField(3, now);
            collector.collect(row);
        }).returns(Types.ROW_NAMED(
                new String[]{
                        "order_id",
                        "serial_number",
                        "order_tag",
                        "paimon_time"
                },
                Types.STRING,
                Types.STRING,
                Types.STRING,
                Types.LOCAL_DATE_TIME
        )).uid("ToRowFlatMap1").name("ToRowFlatMap1").setParallelism(conf.getInt("mapper.torow.parallelism", 8));

        Schema schema = Schema.newBuilder()
                .column("order_id", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("order_tag", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .build();

        Table table = tableEnv.fromChangelogStream(rowDataStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        stmtSet.addInsertSql("INSERT INTO `paimon_catalog`.`" + conf.get("default-database") + "`.`" + "dwa_r_paimon_oc_order_tag_log" + "` SELECT * FROM TemporaryView");

        stmtSet.attachAsDataStream();

        env.execute("OrderTagCompute");
    }
}
