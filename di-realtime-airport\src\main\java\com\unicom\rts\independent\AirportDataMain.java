package com.unicom.rts.independent;

import com.jcraft.jsch.ChannelSftp;
import com.unicom.rts.independent.constant.CommonConstant;
import com.unicom.rts.independent.source.TencentEKafkaSource;
import com.unicom.rts.independent.utils.MyTimeUtil;
import com.unicom.rts.independent.utils.SftpUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.serialization.SimpleStringEncoder;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimerService;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.OutputFileConfig;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.bucketassigners.DateTimeBucketAssigner;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.DefaultRollingPolicy;
import org.apache.flink.streaming.api.operators.StreamingRuntimeContext;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/11
 **/
public class AirportDataMain {
    private final static Logger logger = LoggerFactory.getLogger(AirportDataMain.class);



    public static void main(String[] args) throws Exception {

        ParameterTool parameters = ParameterTool.fromArgs(args);
        String sftpHost = parameters.get(CommonConstant.SFTP_HOST);
        String sftpUsername = parameters.get(CommonConstant.SFTP_USER);
        String sinkParallelism = parameters.get(CommonConstant.SINK_PARALLELISM, "1");
        String sftpPassword = "qjpU04feg#Yq";
        String pathHdfs = parameters.get(CommonConstant.PATH_HDFS, "hdfs://slfn2/user/hh_slfn2_sschj_gray/di_realtime_airport/");
        String targetStr = "cjzh/IAP";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String startDate =  parameters.get(CommonConstant.START_DATE, null);
//        ChannelSftp tt = SftpUtil.getSftpChannel(sftpHost, sftpUsername, sftpPassword);
//        String timeStr = "2023-08-04";
//        SftpUtil.upload(tt,pathHdfs,targetStr+"/"+timeStr+".txt",timeStr);
//        SftpUtil.closeSession(tt);

        //1．初始化流计算运行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(parameters.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        String sourceParallelism = parameters.get(CommonConstant.SOURCE_PARALLELISM, "1");
        //2．设置Checkpoint（10s）周期性启动 和 stateBackend 存储路径
        // Sink保证仅一次语义使用 checkpoint 和 二段提交
        int enableCheckpoint = parameters.getInt("enableCheckpoint", 3);
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(enableCheckpoint));

        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        int minPauseBetween = parameters.getInt("minPauseBetween", 2);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetween));

        int checkpointTimeout = parameters.getInt("checkpointTimeout", 10);
        checkpointConfig.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10,
                Time.of(10, TimeUnit.SECONDS)));        //定时任务

        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                Date d = MyTimeUtil.getYesterdayDate();
                String timeStr = simpleDateFormat.format(d);
                String targetName = targetStr+"/"+timeStr+".txt";
                ChannelSftp cs = null;
                try {
                    cs = SftpUtil.getSftpChannel(sftpHost, sftpUsername, sftpPassword);
                    SftpUtil.upload(cs,pathHdfs, targetName,timeStr);
                    SftpUtil.closeSession(cs);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

            }
        };

        Date now = new Date();
        Date tomorrow = MyTimeUtil.getNextDate();
        Date yesterday = MyTimeUtil.getYesterdayDate();
        logger.info(simpleDateFormat.format(yesterday));
        logger.info(simpleDateFormat.format(tomorrow));
        //时间差
        long timeDifference = tomorrow.getTime() - now.getTime()+30*60*1000;
//        Thread.sleep(timeDifference);
//        Thread.sleep(1000*60*30);


        ScheduledThreadPoolExecutor executorService = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "files_newThread");
            }
        });
        executorService.schedule(timerTask ,timeDifference, TimeUnit.DAYS);


        //4．接入kafka数据源，获取数据
        FlinkKafkaConsumer<ConsumerRecord<Object, Object>> source = TencentEKafkaSource.getFlinkKafkaConsumer(parameters);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");

        if(!StringUtils.isBlank(startDate)){
            Date date = sf.parse(startDate);
            source.setStartFromTimestamp(date.getTime());
        }
        SingleOutputStreamOperator<String> inputStream = env.addSource(source)
                .uid("AirportDataMain-Source-Kafka")
                .name("AirportDataMain-Source-Kafka")
                .setParallelism(Integer.valueOf(sourceParallelism))
                .process(new ProcessFunction<ConsumerRecord<Object, Object>, String>() {
                    @Override
                    public void processElement(ConsumerRecord<Object, Object> message, ProcessFunction<ConsumerRecord<Object, Object>, String>.Context ctx, Collector<String> out) throws Exception {
                        byte[] value = (byte[]) message.value();
                        String str = new String(value, StandardCharsets.UTF_8);
                        out.collect(str);
                    }
                })
                .setParallelism(Integer.valueOf(sourceParallelism))
                .uid("AirportDataMain-process")
                .name("AirportDataMain-process");


        DateTimeBucketAssigner dateTimeBucketAssigner = new DateTimeBucketAssigner("yyyy-MM-dd", ZoneId.of("Asia/Shanghai"));

        //5．创建Streamingfilesink对象
        OutputFileConfig config = OutputFileConfig
                .builder()
                .withPartPrefix("iap"+sf.format(now))
                .withPartSuffix(".txt")
                .build();
        //5-1. 创建输出文件配置，指定输出路径 /FlinkStreamFileSink/parquet
//        FileSink sink = FileSink
//                .forRowFormat(
//                        new Path("E:\\out1"),
//                        new SimpleStringEncoder<String>("UTF-8"))
//                // sink-kafka new FlinkKafkaProducer
//                //5-2.StreamingFileSink 行格式化 ， withBucketAssigner->DateTimeBucketAssigner
//                .withBucketAssigner(new DateTimeBucketAssigner<>("yyyy-MM-dd--HH--mm"))
//                //withRollingPolicy -> 默认滚筒策略
//                .withRollingPolicy(DefaultRollingPolicy.builder()
//                        .withMaxPartSize(64 * 1024 * 1024)
//                        .withRolloverInterval(TimeUnit.SECONDS.toMillis(10))
//                        .withInactivityInterval(TimeUnit.SECONDS.toMillis(5))
//                        .build())
//                //withOutputFileConfig -> 输出文件的配置
//                .withOutputFileConfig(config)
//                .build();

        DefaultRollingPolicy<Object, Object> aRollingPolicy = DefaultRollingPolicy.builder()
                .withRolloverInterval(TimeUnit.HOURS.toMillis(2))
                .withInactivityInterval(TimeUnit.MINUTES.toMillis(5))
                .withMaxPartSize(1024 * 1024 * 1024)
                .build();
        String filePath = pathHdfs+"/data";
        StreamingFileSink sink = StreamingFileSink
                .forRowFormat(new Path(filePath), new SimpleStringEncoder<String>("UTF-8"))
                .withBucketAssigner(dateTimeBucketAssigner)
                .withRollingPolicy( aRollingPolicy)
                .withOutputFileConfig(config)
                .build();

        //6.设置输出 sink
        inputStream.addSink(sink).setParallelism(Integer.valueOf(sinkParallelism));
        //7．执行任务
        env.execute();
    }
}
