package com.unicom.realtime.function;

import com.sec.asiainfo.simkey.kms.kmssdk.ClientLocalEncryption;
import com.sec.asiainfo.simkey.kms.kmssdk.KMSClient;
import com.unicom.realtime.bean.FloData;
import com.unicom.realtime.bean.enums.FloEnum;
import com.unicom.realtime.util.HbaseUtil;
import com.unicom.realtime.util.KeysGenerate;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class FloFlatMap extends RichFlatMapFunction<String, FloData> {
    private final Logger logger = LoggerFactory.getLogger(FloFlatMap.class);
//    private static final String DATETIME_FORMAT = "yyyyMMdd";

    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private  KMSClient kmsClient;
    private final ParameterTool conf;
    private final byte[] FAMILYNAME = "f".getBytes();
    // List<Put> puts = new ArrayList<>(128);
//    BufferedMutator mutator = null;

    private ClientLocalEncryption cle;

    public FloFlatMap(ParameterTool conf) {
        this.conf = conf;
    }

    /**
     * @param parameters
     * @throws Exception
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        System.out.println("open = " + parameters);
        super.open(parameters);
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"), conf.get("zookeeper.znode.parent", "/hbase"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.floData")));
        kmsClient = KeysGenerate.getLocalClient(conf.get("appId","1006100000000028"),
                conf.get("appSecret","udYKqnZdbgzdC6K9NINJrGyY/Ll1UWnvVa8kOrLHc0OCcx03FG/0gd3mEzpjcu3c") ,
                conf.get("url","http://10.125.131.157:18301/ksccinterface2"));
        cle = KeysGenerate.getClientLocalEncryption(kmsClient,
                conf.get("userEncKeyId","7f55636eb9b942ed9e3a922541e1a656"),
                conf.get("userEncKey","MGwCAQECAgQCAQH/BCDO8C4xtiTmqQ1aHsclew0/n4uFprQjAvVWun8Yrfm30qASBBCS8ya58d239VuylGDkrSfaoRIMEEQ2MkY1Mjg4NzAxRDE0RTWiCgwINzZEODdGMjWjCgwIMTEwMTUwMDA="),
                conf.get("userHmacKeyId","11edeb7e5cb742328a5aa139166998a2"),
                conf.get("userHmacKey","MGwCAQECAgQCAQH/BCCS6BolsUEXeu036zycAc7X5bdiwO5B7RPy/E/xVOVmGKASBBCS8ya58d239VuylGDkrSfaoRIMEEQ2MkY1Mjg4NzAxRDE0RTWiCgwINzZEODdGMjWjCgwIMTEwMTUwMDA="));
    }

    @Override
    public void flatMap(String record, Collector<FloData> out) throws Exception{

        System.out.println("76 ==========" + record );
        String[] line = StringUtils.splitPreserveAllTokens(record, "\001");
        if (line.length < 19) {
            return;
        }
        if (11 != line[FloEnum.deviceNumber.getCode()].length()) {
            return;
        }
        if (!"051".equals(line[FloEnum.provId.getCode()])) {
            return;
        }
        try {
            byte[] rowKeyPrefix = Bytes.toBytes((short) (line[FloEnum.deviceNumber.getCode()].hashCode() & 0x7FFF));
            byte[] key = Bytes.add(rowKeyPrefix, Bytes.toBytes(line[FloEnum.deviceNumber.getCode()]));
            // 根据号码查询加密后数据
            String row = Bytes.toString(key);
            if (row.length() > 10){
                row = row.substring(row.length()-11);
            }
            String encryptRowkey = null;
            try {
                byte[] bTweak = row.getBytes(StandardCharsets.UTF_8);
                encryptRowkey = cle.fpeCompressedEncrypt(bTweak ,row,true);
            } catch (Exception e) {
                logger.info("加密异常>>>>{},rowkey==== {} " , e.getMessage() ,"");
                e.printStackTrace();
            }
            byte[] rowKeyPrefix_1 = Bytes.toBytes((short) (row.hashCode() & 0x7FFF));
            byte[] eKey = Bytes.add(rowKeyPrefix_1, Bytes.toBytes(encryptRowkey));
            Put p = new Put(eKey);
            p.addColumn(FAMILYNAME, Bytes.toBytes("countrySaturation"), Bytes.toBytes(line[FloEnum.countrySaturation.getCode()]));
            p.addColumn(FAMILYNAME, Bytes.toBytes("totalUseContFlux"), Bytes.toBytes(line[FloEnum.totalUseContFlux.getCode()]));
            p.addColumn(FAMILYNAME, Bytes.toBytes("contBalance"), Bytes.toBytes(line[FloEnum.contBalance.getCode()]));
            p.addColumn(FAMILYNAME, Bytes.toBytes("dealTime"), Bytes.toBytes(line[FloEnum.dealTime.getCode()]));
            table.put(p);
            // mutator.mutate(p);
        } catch (Exception e) {
            logger.info("error >>>>>>>>>> {} : ", e);
            // blocke1.printStackTrace();
        }
    }
}
