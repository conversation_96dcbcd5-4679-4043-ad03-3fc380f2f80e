package com.unicom.rts.bean;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;

@Builder
public class RequestData {

    @J<PERSON><PERSON>ield(name = "UNI_BSS_HEAD")
    private RequestHeader header;
    @JSONField(name = "UNI_BSS_BODY")
    private RequestBody body;
    @JSO<PERSON>ield(name = "UNI_BSS_ATTACHED")
    private RequestAttanch ath;

    public RequestHeader getHeader() {
        return header;
    }

    public void setHeader(RequestHeader header) {
        this.header = header;
    }

    public RequestBody getBody() {
        return body;
    }

    public void setBody(RequestBody body) {
        this.body = body;
    }

    public RequestAttanch getAth() {
        return ath;
    }

    public void setAth(RequestAttanch ath) {
        this.ath = ath;
    }
}
