// package com.unicom.rts.independent.source;
//
// import com.unicom.rts.independent.cdc.CDCJsonDSchema;
// import com.ververica.cdc.connectors.mysql.table.StartupOptions;
// import com.ververica.cdc.connectors.mysql.source.MySqlSource;
// import org.apache.flink.api.java.utils.ParameterTool;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
//
// import java.util.Properties;
//
// /**
//  * <AUTHOR>
//  * @description:
//  * @date 2022/4/19 10:03
//  */
// public class MySqlCDCSource {
//     private final static Logger log = LoggerFactory.getLogger(MySqlCDCSource.class);
//
//     public static MySqlSource<String> create(ParameterTool conf, String serverIdRange) throws Exception {
//         log.info("tableList:{}", conf.get("mysql.cdc.table.region"));
//         log.info("set serverIdRange:" + serverIdRange);
//         Properties debeziumProperties = new Properties();
//         // 配置CDC扫描表时不加锁，加锁需要reload权限
//         debeziumProperties.put("snapshot.locking.mode", "none");
//         return MySqlSource.<String>builder()
//                 .hostname(conf.get("mysql.ip"))
//                 .port(conf.getInt("mysql.port"))
//                 .databaseList(conf.get("mysql.cdc.database")) // set captured database
//                 .tableList(conf.get("mysql.cdc.table.region")) // set captured table
//                 .username(conf.get("mysql.user"))
//                 .password(conf.get("mysql.password"))
//                 .serverId(serverIdRange)
//                 .deserializer(new CDCJsonDSchema()) // converts SourceRecord to JSON String
//                 .startupOptions(StartupOptions.initial())
//                 .debeziumProperties(debeziumProperties)
//                 .build();
//     }
// }