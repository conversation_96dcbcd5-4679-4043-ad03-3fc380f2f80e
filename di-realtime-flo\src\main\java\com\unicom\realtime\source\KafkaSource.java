package com.unicom.realtime.source;

// import com.unicom.realtime.kafka.MultiTopicSerializationSchema;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
// import org.apache.flink.connector.kafka.sink.HeaderSelector;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.TopicSelector;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
// import org.apache.flink.streaming.api.functions.sink.TwoPhaseCommitSinkFunction;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class KafkaSource {

    private static final String SINK_SUFFIX = "_Sink";
    private static final String SINK_PREFIX = "sink.kafka.properties.";
    public static  FlinkKafkaConsumer<String> getTgKafkaConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String bootStrapServer = conf.get("tg.bootStrapServer");
        String groupId = conf.get("tg.group.id");
        String autoOffsetReset = conf.get("tg.auto.offset.reset", "latest");
        String enableAutoCommit = conf.get("tg.enable.auto.commit", "true");
        long startFromTimeStamp = conf.getLong("tg.kafkaConsumerStartFromTimeStamp", 0L);
        String startFrom = conf.get("tg.kafkaConsumerStartFrom", "latest");

        //**kafka鉴权**
        boolean sourceSecurity = conf.getBoolean("tg.kafka.security");
        String kafkaUser = conf.get("tg.kafka.user");
        String kafkaPassword = conf.get("tg.kafka.password");
        String saslJaasConfig = conf.get("tg.sasl.jaas.config");
        String securityProtocol = conf.get("tg.security.protocol");
        String saslMechanism = conf.get("tg.sasl.mechanism");

        return getFlinkKafkaConsumer(bootStrapServer, groupId, autoOffsetReset, enableAutoCommit, startFromTimeStamp, startFrom, sourceSecurity, kafkaUser, kafkaPassword, saslJaasConfig, securityProtocol, saslMechanism, topics);
    }


    public static  FlinkKafkaConsumer<String> getCjzhKafkaConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String bootStrapServer = conf.get("cjzh.bootStrapServer");
        String groupId = conf.get("cjzh.group.id");
        String autoOffsetReset = conf.get("cjzh.auto.offset.reset", "latest");
        String enableAutoCommit = conf.get("cjzh.enable.auto.commit", "true");
        long startFromTimeStamp = conf.getLong("cjzh.kafkaConsumerStartFromTimeStamp", 0L);
        String startFrom = conf.get("cjzh.kafkaConsumerStartFrom", "latest");

        //**kafka鉴权**
        boolean sourceSecurity = conf.getBoolean("cjzh.kafka.security");
        String kafkaUser = conf.get("cjzh.kafka.user", "");
        String kafkaPassword = conf.get("cjzh.kafka.password", "");
        String saslJaasConfig = conf.get("cjzh.sasl.jaas.config", "");
        String securityProtocol = conf.get("cjzh.security.protocol", "");
        String saslMechanism = conf.get("cjzh.sasl.mechanism", "");

        return getFlinkKafkaConsumer(bootStrapServer, groupId, autoOffsetReset, enableAutoCommit, startFromTimeStamp, startFrom, sourceSecurity, kafkaUser, kafkaPassword, saslJaasConfig, securityProtocol, saslMechanism, topics);
    }


    public static  FlinkKafkaConsumer<String> getRootKafkaConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String bootStrapServer = conf.get("root.bootStrapServer");
        String groupId = conf.get("root.group.id");
        String autoOffsetReset = conf.get("root.auto.offset.reset", "latest");
        String enableAutoCommit = conf.get("root.enable.auto.commit", "true");
        long startFromTimeStamp = conf.getLong("root.kafkaConsumerStartFromTimeStamp", 0L);
        String startFrom = conf.get("root.kafkaConsumerStartFrom", "latest");

        //**kafka鉴权**
        boolean sourceSecurity = false;
        String kafkaUser = "";
        String kafkaPassword = "";
        String saslJaasConfig = "";
        String securityProtocol = "";
        String saslMechanism = "";

        return getFlinkKafkaConsumer(bootStrapServer, groupId, autoOffsetReset, enableAutoCommit, startFromTimeStamp, startFrom, sourceSecurity, kafkaUser, kafkaPassword, saslJaasConfig, securityProtocol, saslMechanism, topics);
    }


    public static  FlinkKafkaConsumer<ConsumerRecord<String, String>> getCjzhUdsConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String bootStrapServer = conf.get("cjzh.bootStrapServer");
        String groupId = conf.get("cjzh.group.id");
        String autoOffsetReset = conf.get("cjzh.auto.offset.reset", "latest");
        String enableAutoCommit = conf.get("cjzh.enable.auto.commit", "true");
        long startFromTimeStamp = conf.getLong("cjzh.kafkaConsumerStartFromTimeStamp", 0L);
        String startFrom = conf.get("cjzh.kafkaConsumerStartFrom", "latest");

        //**kafka鉴权**
        boolean sourceSecurity = conf.getBoolean("cjzh.kafka.security");
        String kafkaUser = conf.get("cjzh.kafka.user", "");
        String kafkaPassword = conf.get("cjzh.kafka.password", "");
        String saslJaasConfig = conf.get("cjzh.sasl.jaas.config", "");
        String securityProtocol = conf.get("cjzh.security.protocol", "");
        String saslMechanism = conf.get("cjzh.sasl.mechanism", "");

        return getFlinkKafkaUdsConsumer(bootStrapServer, groupId, autoOffsetReset, enableAutoCommit, startFromTimeStamp, startFrom, sourceSecurity, kafkaUser, kafkaPassword, saslJaasConfig, securityProtocol, saslMechanism, topics);
    }


    public static  FlinkKafkaConsumer<ConsumerRecord<String, String>> getRootUdsConsumer(ParameterTool conf, String topic) throws Exception{

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic,","));
        String bootStrapServer = conf.get("root.bootStrapServer");
        String groupId = conf.get("root.group.id");
        String autoOffsetReset = conf.get("root.auto.offset.reset", "latest");
        String enableAutoCommit = conf.get("root.enable.auto.commit", "true");
        long startFromTimeStamp = conf.getLong("root.kafkaConsumerStartFromTimeStamp", 0L);
        String startFrom = conf.get("root.kafkaConsumerStartFrom", "latest");

        //**kafka鉴权**
        boolean sourceSecurity = false;
        String kafkaUser = "";
        String kafkaPassword = "";
        String saslJaasConfig = "";
        String securityProtocol = "";
        String saslMechanism = "";

        return getFlinkKafkaUdsConsumer(bootStrapServer, groupId, autoOffsetReset, enableAutoCommit, startFromTimeStamp, startFrom, sourceSecurity, kafkaUser, kafkaPassword, saslJaasConfig, securityProtocol, saslMechanism, topics);
    }

    private static  FlinkKafkaConsumer<ConsumerRecord<String, String>> getFlinkKafkaUdsConsumer(String bootStrapServer, String groupId, String autoOffsetReset,
                                                                     String enableAutoCommit, long startFromTimeStamp, String startFrom, boolean sourceSecurity, String kafkaUser,
                                                                     String kafkaPassword, String saslJaasConfig, String securityProtocol, String saslMechanism, List<String> topics) throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", bootStrapServer);
        props.put("group.id", groupId);
        props.put("auto.offset.reset", autoOffsetReset);
        props.put("enable.auto.commit", enableAutoCommit);
        props.put("request.timeout.ms", "600000");
        props.put("default.api.timeout.ms", "600000");
        props.put("retries", "100");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("auto.commit.interval.ms", "1000");
        props.put("session.timeout.ms", "30000");
        props.put("flink.partition-discovery.interval-millis","60000");

        //**kafka鉴权**
        if (sourceSecurity) {
            props.setProperty("sasl.jaas.config", saslJaasConfig + " required username=\""+kafkaUser+"\" password=\""+kafkaPassword+"\";");
            props.setProperty("security.protocol", securityProtocol);
            props.setProperty("sasl.mechanism", saslMechanism);
        }
        FlinkKafkaConsumer<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer<>(topics, new CustomDeSerializationSchema(), props);

        if (startFromTimeStamp > 0L) {
            consumer.setStartFromTimestamp(startFromTimeStamp);
        } else if ("earliest".equals(startFrom)) {
            consumer.setStartFromEarliest();
        } else if ("latest".equals(startFrom)) {
            consumer.setStartFromLatest();
        } else {
            consumer.setStartFromGroupOffsets();
        }

        return consumer;
    }

    private static  FlinkKafkaConsumer<String> getFlinkKafkaConsumer(String bootStrapServer, String groupId, String autoOffsetReset,
                 String enableAutoCommit, long startFromTimeStamp, String startFrom, boolean sourceSecurity, String kafkaUser,
                 String kafkaPassword, String saslJaasConfig, String securityProtocol, String saslMechanism, List<String> topics) throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", bootStrapServer);
        props.put("group.id", groupId);
        props.put("auto.offset.reset", autoOffsetReset);
        props.put("enable.auto.commit", enableAutoCommit);
        props.put("request.timeout.ms", "600000");
        props.put("default.api.timeout.ms", "600000");
        props.put("retries", "100");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("auto.commit.interval.ms", "1000");
        props.put("session.timeout.ms", "30000");
        props.put("flink.partition-discovery.interval-millis","60000");

        //**kafka鉴权**
        if (sourceSecurity) {
            props.setProperty("sasl.jaas.config", saslJaasConfig + " required username=\""+kafkaUser+"\" password=\""+kafkaPassword+"\";");
            props.setProperty("security.protocol", securityProtocol);
            props.setProperty("sasl.mechanism", saslMechanism);
        }
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics, new SimpleStringSchema(), props);

        if (startFromTimeStamp > 0L) {
            consumer.setStartFromTimestamp(startFromTimeStamp);
        } else if ("earliest".equals(startFrom)) {
            consumer.setStartFromEarliest();
        } else if ("latest".equals(startFrom)) {
            consumer.setStartFromLatest();
        } else {
            consumer.setStartFromGroupOffsets();
        }

        return consumer;
    }


    // public static void addSink(DataStream<Tuple3<String, String, Iterable<Header>>> stream, ParameterTool conf) {
    //     // sink: sink kafka配置
    //     Properties producerProp = new Properties();
    //     producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
    //     producerProp.put("acks", conf.get("acks", "0")); //Must set acks to all in order to use the idempotent producer
    //     producerProp.put("retries", conf.get("retries", "2"));
    //     producerProp.put("batch.size", conf.get("batch.size", "16384"));
    //     producerProp.put("linger.ms", conf.get("linger.ms", "50"));
    //     producerProp.put("buffer.memory", conf.get("buffer.memory", "33554432"));
    //     producerProp.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));
    //
    //     // 下发kafka开启认证
    //     boolean sinkSecurity = conf.getBoolean("sink.kafka.security");
    //     if (sinkSecurity) {
    //         String mechanism = conf.get("sasl.mechanism");
    //         String protocol = conf.get("security.protocol");
    //         String jaasConfig = conf.get("sasl.jaas.config");
    //         producerProp.setProperty("sasl.jaas.config", jaasConfig + " required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
    //         producerProp.setProperty("security.protocol", protocol);
    //         producerProp.setProperty("sasl.mechanism", mechanism);
    //     }
    //
    //     final TopicSelector<Tuple3<String, String, Iterable<Header>>> topicSelector =
    //             (tp) -> {
    //                 return tp.f0;
    //             };
    //
    //     final HeaderSelector<Tuple3<String, String, Iterable<Header>>> headerSelector =
    //             (tp) -> {
    //                 return tp.f2;
    //             };
    //
    //     KafkaSink<Tuple3<String, String, Iterable<Header>>> sink = KafkaSink.<Tuple3<String, String, Iterable<Header>>>builder()
    //             .setBootstrapServers(conf.get("sink.kafka.bootstrap"))
    //             .setKafkaProducerConfig(producerProp)
    //             .setRecordSerializer(new MultiTopicSerializationSchema())
    //             .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
    //             .build();
    //
    //     stream.sinkTo(sink).uid("KafkaMulti" + SINK_SUFFIX).setParallelism(Integer.parseInt(conf.get("sink.parallelism")));
    // }
}
