package com.unicom.rts.source;

import com.unicom.rts.sink.KafkaMultiSinkSerializationSchema;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.flink.streaming.connectors.kafka.internals.KafkaDeserializationSchemaWrapper;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.Arrays;
import java.util.Properties;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/19 14:18
 */
public class KafkaStream {
    private static final String SOURCE_SUFFIX = "_Source";
    private static final String SINK_SUFFIX = "_Sink";

    public static DataStream<String> addSource(StreamExecutionEnvironment env, ParameterTool conf, String topic) {
        String kafkaUser = conf.get("kafka.user");
        String kafkaPassword = conf.get("kafka.password");
        String brokers = conf.get("source.bootstrap");
        String groupId = conf.get("group.id");
        int parallelism = conf.getInt("source.parallelism");

        Properties properties = new Properties();
        properties.put("bootstrap.servers", brokers);
        properties.put("group.id", groupId);
        properties.put("commit.offsets.on.checkpoint", conf.getBoolean("commit.offsets.on.checkpoint", true));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "10000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        Boolean sourceSecurity = conf.getBoolean("source.kafka.security");
        if (sourceSecurity) {
            //天宫kafka安全设置
            properties.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", "SASL_PLAINTEXT");
            properties.setProperty("sasl.mechanism", "SCRAM-SHA-256");
        }

        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(brokers)
                .setTopics(topic)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperty("partition.discovery.interval.ms", "30000")
                .setProperty("commit.offsets.on.checkpoint", conf.get("commit.offsets.on.checkpoint", "true"))
                .setProperties(properties)
                .build();

        return env.fromSource(source, WatermarkStrategy.noWatermarks(), topic + SOURCE_SUFFIX)
                .setParallelism(parallelism)
                .uid(topic + SOURCE_SUFFIX);
    }

    public static void addSink(DataStream<Tuple2<String, String>> stream, ParameterTool conf) {
        // sink: sink kafka配置
        Properties producerProp = new Properties();
        producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
        producerProp.put("acks", conf.get("acks", "0")); //Must set acks to all in order to use the idempotent producer
        producerProp.put("retries", conf.get("retries", "2"));
        producerProp.put("batch.size", conf.get("batch.size", "16384"));
        producerProp.put("linger.ms", conf.get("linger.ms", "50"));
        producerProp.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        producerProp.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));

        // 下发kafka开启认证
        Boolean sinkSecurity = conf.getBoolean("sink.kafka.security");
        if (sinkSecurity) {
            String mechanism = conf.get("sasl.mechanism");
            String protocol = conf.get("security.protocol");
            String jaasConfig = conf.get("sasl.jaas.config");
            producerProp.setProperty("sasl.jaas.config", jaasConfig + " required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
            producerProp.setProperty("security.protocol", protocol);
            producerProp.setProperty("sasl.mechanism", mechanism);
        }

        FlinkKafkaProducer<Tuple2<String, String>> producer = new FlinkKafkaProducer<>(
                "all",      // target topic
                new KafkaMultiSinkSerializationSchema(),
                producerProp,
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE, 5);

        stream.addSink(producer).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("KafkaMulti" + SINK_SUFFIX);
    }

    public static DataStream<String> addSourceOld(StreamExecutionEnvironment env, ParameterTool conf, String topic, KafkaDeserializationSchema deserializationSchema) {
        int parallelism = conf.getInt("source.parallelism");
        String brokers = conf.get("source.bootstrap");
        String kafkaUser = conf.get("kafka.user");
        String kafkaPassword = conf.get("kafka.password");
        Properties properties = new Properties();
        properties.put("bootstrap.servers", brokers);
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("flink.partition-discovery.interval-millis", "60000");

        Boolean sourceSecurity = conf.getBoolean("source.kafka.security");
        if (sourceSecurity) {
            //kafka安全设置
            properties.setProperty("sasl.jaas.config", conf.get("source.sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule") + " required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", conf.get("source.security.protocol", "SASL_PLAINTEXT"));
            properties.setProperty("sasl.mechanism", conf.get("source.sasl.mechanism", "SCRAM-SHA-256"));
        }

        FlinkKafkaConsumer consumer = new FlinkKafkaConsumer(
                Arrays.asList(StringUtils.splitPreserveAllTokens(topic, ",")), deserializationSchema, properties);
        consumer.setCommitOffsetsOnCheckpoints(true);
        String startFrom = conf.get("kafkaConsumerStartFrom", "GroupOffsets");
        Long startFromTimeStamp = conf.getLong("kafkaConsumerStartFromTimeStamp", 0L);

        if (startFromTimeStamp > 0L) {
            consumer.setStartFromTimestamp(startFromTimeStamp);
        } else if ("earliest".equals(startFrom)) {
            consumer.setStartFromEarliest();
        } else if ("latest".equals(startFrom)) {
            consumer.setStartFromLatest();
        } else {
            consumer.setStartFromGroupOffsets();
        }

        return env.addSource(consumer).uid(topic + SOURCE_SUFFIX + "UID").name(topic + SOURCE_SUFFIX)
                .setParallelism(parallelism);
    }

    public static DataStream<String> addSourceOld(StreamExecutionEnvironment env, ParameterTool conf, String topic) {
        return addSourceOld(env, conf, topic, new KafkaDeserializationSchemaWrapper(new SimpleStringSchema()));
    }
}
