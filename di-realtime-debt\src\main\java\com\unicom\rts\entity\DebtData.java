package com.unicom.rts.entity;

import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023-01-30 15:26
 */

@Data
public class DebtData {


    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String SEPARATOR = "\u0001";

    /**
     * hal 数据源
     * device_number,
     * partition_id,
     * user_id,
     * service_id,
     * main_tag,state_code,start_date,end_date,update_time,provid,cityid,intime,datasource
     */
    private String deviceNumber;
    private String partitionId;
    private String userId;
    private String serviceId;
    private String mainTag;
    private String stateCode;
    private String oldStateCode;
    private String startDate;
    private String endDate;
    private String updateTime;      //eventTime
    private String provId; //发生省
    private String cityId;
    private String hProvId;//归属省
    private String cityCode;


    private String dataReceiveTime;
    private String dataSource;
    private String inTime;

    private UserTagBean userTagBean;

    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap();


    public String toString(long time) {
        //下发字段
        StringBuilder sb = new StringBuilder();
        sb.append(deviceNumber);
        sb.append(SEPARATOR);
        sb.append(partitionId);
        sb.append(SEPARATOR);
        sb.append(userId);
        sb.append(SEPARATOR);
        sb.append(serviceId);
        sb.append(SEPARATOR);
        sb.append(mainTag);
        sb.append(SEPARATOR);
        sb.append(stateCode);
        sb.append(SEPARATOR);
        sb.append(startDate);
        sb.append(SEPARATOR);
        sb.append(endDate);
        sb.append(SEPARATOR);
        sb.append(updateTime);
        sb.append(SEPARATOR);
        sb.append(provId);
        sb.append(SEPARATOR);
        sb.append(cityId);
        sb.append(SEPARATOR);
        sb.append(inTime);
        sb.append(SEPARATOR);
        sb.append(dataSource);
        sb.append(SEPARATOR);
        sb.append(DateFormatUtils.format(time, DATETIME_FORMAT));
        sb.append(SEPARATOR);
        sb.append(dataReceiveTime);
        sb.append(SEPARATOR);
        sb.append(cityCode);
        return sb.toString();
    }

    public void parseHeader(ConsumerRecord<String,String> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, Bytes.toString(next.value()));
            }
        }
        headersMap.put("scene_id", "11");
        headersMap.put("root_time", String.valueOf((record.timestamp())));
        headersMap.put("process_time", String.valueOf(System.currentTimeMillis()));
        if (headers.lastHeader("receive_time") != null && headers.lastHeader("receive_time").value() != null) {
            headersMap.put("receive_time",new String(headers.lastHeader("receive_time").value()));
        }
        if (headers.lastHeader("integration_time") != null && headers.lastHeader("integration_time").value() != null) {
            headersMap.put("integration_time",new String(headers.lastHeader("integration_time").value()) );
        }
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    public Iterable<Header> getHeaders(String provId) {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        headersMap.put("owner_province", provId);
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

}
