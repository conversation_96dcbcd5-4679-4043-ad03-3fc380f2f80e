package com.unicom.rts.independent.source;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/

@Slf4j
public class WhiteListSource extends RichSourceFunction<List<String>> {
    private final static Logger logger = LoggerFactory.getLogger(WhiteListSource.class);
    private PreparedStatement ps;
    private Connection connection;
    private ResultSet resultSet;
    private volatile boolean isRunning = true;
    private ParameterTool conf;

    public WhiteListSource(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        logger.info("get whitelist.jdbcUrl:{}", conf.get("whitelist.jdbcUrl"));
        connection = getConnection(conf.get("whitelist.jdbcUrl"), conf.get("whitelist.mysql.user"), conf.get("whitelist.mysql.password"));
        String sql = "select distinct device_number from contextdata.loc_ga_new_iot_data where info_time=DATE(NOW())";
        logger.warn("sql :{}", sql);
        if (connection != null) {
            ps = this.connection.prepareStatement(sql);
        }
    }

    @Override
    public void run(SourceContext<List<String>> ctx) throws Exception {
        List<String> whiteList = new ArrayList<>();
        while (isRunning) {
            try {
                resultSet = ps.executeQuery();
                while (resultSet.next()) {
                    whiteList.add(resultSet.getString("device_number"));
                }
                ctx.collect(whiteList);
                whiteList.clear();
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                resultSet.close();
            }

            Thread.sleep(1000 * 60);
        }
    }

    @Override
    public void cancel() {
        try {
            super.close();
            if (connection != null) {
                connection.close();
            }
            if (ps != null) {
                ps.close();
            }
        } catch (Exception e) {
            log.error("runException:{}", e);
        }
        isRunning = false;
    }

    private Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            //注意，改成配置参数 数据库地址和用户名、密码
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            log.error("-----------mysql get connection has exception , msg = {}", e.toString());
        }
        return con;
    }
}
