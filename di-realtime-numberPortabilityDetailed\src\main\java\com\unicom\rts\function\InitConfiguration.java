package com.unicom.rts.function;

import com.unicom.rts.bean.TConnect;
import com.unicom.rts.constant.CommonConstant;
import com.unicom.rts.util.JdbcUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class InitConfiguration {
    private final static Logger logger = LoggerFactory.getLogger(InitConfiguration.class);

    public static TConnect initFunc(ParameterTool parameters) throws Exception {


        String hbaseCode = parameters.get("hbaseCode");
        String hbaseSql = CommonConstant.HBASE_SQL.replace("#NAME", hbaseCode);
        JdbcUtil jdbcUtil = new JdbcUtil();
        jdbcUtil.openMySqlConnection(parameters.get(CommonConstant.MYSQL_URL), parameters.get(CommonConstant.MYSQL_USER), parameters.get(CommonConstant.MYSQL_PASSWD));

        List<TConnect> hbaseArr = jdbcUtil.select(hbaseSql, TConnect.class);
        if (hbaseArr.size() == 0) {
            throw new Exception("Hbase:No data obtained!");
        }
        TConnect ret = hbaseArr.get(0);
        return ret;
    }
}

