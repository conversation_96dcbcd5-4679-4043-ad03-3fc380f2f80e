package com.unicom.rts.function;

import com.unicom.rts.bean.Pay;
import com.unicom.rts.bean.PayInBean;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> <PERSON>
 * @create 9/13/23 4:22 PM
 */
public class PayPaimonProcessCo extends KeyedCoProcessFunction<String, Pay,PayInBean , Tuple3<String, String, Iterable<Header>>> {

//    private final static Logger logger = LoggerFactory.getLogger(PayPaimonProcessCo.class);
    ParameterTool conf;

    private ValueState<PayInBean> payInBeanValueState;
    private String outPutTopic;

    public PayPaimonProcessCo(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        outPutTopic = conf.get("outPut.topic");
        //数据湖表状态
        ValueStateDescriptor<PayInBean> payInBeanValueStateDes = new ValueStateDescriptor<>("payInBeanValue", PayInBean.class);
        payInBeanValueState = getRuntimeContext().getState(payInBeanValueStateDes);
    }

    @Override
    public void processElement1(Pay pay, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
//        System.out.println("payInBeanValueState.value()"+payInBeanValueState.value());
        PayInBean value = payInBeanValueState.value();
        //标准场景1.匹配上下发 2.匹配不上扔掉
        if(value!=null && value.getPhoneNum()!=null && !"".equals(value.getPhoneNum())) {
            System.out.println("下发数据输出"+pay.toString()+"header"+pay.getHeaders());
            collector.collect(new Tuple3<>(outPutTopic, pay.toString(), pay.getHeaders()));
        }
    }

    @Override
    public void processElement2(PayInBean payInBean, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        payInBeanValueState.update(payInBean);
    }
}
