package com.unicom.rts.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-02-23 10:13
 */
public class TimeUtil {
    private static Logger logger = LoggerFactory.getLogger(TimeUtil.class);


    public static boolean isTimeCheckTGT(){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("mm:ss");
        String[] splitFormat = simpleDateFormat.format(new Date()).split(":");
        if (Integer.valueOf(splitFormat[0]) == 0 && Integer.valueOf(splitFormat[1]) == 0){
            logger.info("isCheckTime-->minuts:"+splitFormat[0]+" "+"second:"+splitFormat[1]);
            return true;
        }
        return false;
    }


    public static String  timeday(String millisecond){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(Long.valueOf(millisecond));
        String format = simpleDateFormat.format(date);
        return format;
    }

    public static String getYmdDate(){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = simpleDateFormat.format(new Date());
        return  format;
    }

    public static String convertTimestamp2Date(String timestamp, String pattern) {

        if (!"".equals(timestamp) && !("null").equals(timestamp)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
            long timestampLong = Long.parseLong(timestamp);
            return simpleDateFormat.format(new Date(timestampLong));
        }else {
            return "";
        }

    }


}
