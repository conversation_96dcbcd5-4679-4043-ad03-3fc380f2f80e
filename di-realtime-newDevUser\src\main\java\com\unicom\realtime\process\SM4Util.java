package com.unicom.realtime.process;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;

import javax.crypto.SecretKey;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/10/18 10:13
 */
public class SM4Util {

    private static SM4 sm4(String key){
        int strKeyLen = 16;
        int hexKeyLen = 32;
        if(key == null){
            throw new RuntimeException("SecretKey can not be null");
        }else if(key.length() != strKeyLen && key.length() != hexKeyLen){
            throw new RuntimeException("SecretKey length wrong");
        }
        SM4 sm4;
        if(key.length() == hexKeyLen){
            sm4 = SmUtil.sm4(HexUtil.decodeHex(key));
        }else{
            sm4 = SmUtil.sm4(key.getBytes());

        }
        return sm4;
    }

    public static byte[] encrypt(SecretKey key, byte[] data) {
        SM4 sm4 = SmUtil.sm4(key.getEncoded());
        return sm4.encrypt(data);
    }

    public static String encrypt(String key, String data) {
        return encryptBase64(key,data);
    }

    public static String encryptHex(String key, String data) {
        SM4 sm4 = sm4(key);
        return sm4.encryptHex(data);
    }

    public static String encryptBase64(String key, String data) {
        SM4 sm4 = sm4(key);
        return sm4.encryptBase64(data);
    }

    public static String decrypt(String key, String data) {
        SM4 sm4 = sm4(key);
        return sm4.decryptStr(data, CharsetUtil.CHARSET_UTF_8);
    }

    public static void main(String[] args) {
        String content = "测试报文test";
        SecretKey key = SecureUtil.generateKey("SM4");
        String keyStr = HexUtil.encodeHexStr(key.getEncoded());

        String encryptData = SM4Util.encrypt(keyStr, content);
        String decryptData = SM4Util.decrypt(keyStr, encryptData);

        System.out.println("key:" + keyStr);
        System.out.println("encryptData:" + encryptData);
        System.out.println("decryptData:" + decryptData);

    }
}
