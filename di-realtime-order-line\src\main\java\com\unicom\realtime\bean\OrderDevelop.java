package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDevelop {
    private String orderId;
    private String orderLineId;
    private String subTypeCode;
    private String subTypeValue;
    private String developDate;
    private String developManagerId;
    private String developManagerName;
    private String developStaffId;
    private String developStaffName;
    private String developNickName;
    private String developNickId;
    private String developContact;
    private String provinceCode;
    private String developProvinceCode;
    private String developCity;
    private String developDepartName;
    private String developDepartId;
    private String standardKindCode;
    private String modifyTag;
    private String developType;
    private String channelId;
    private String codeTypeCode;
    private String channelType;
    private String opt;
    private String opttime;
    private String cdhtime;
    private String dataReceiveTime;

    @Override
    public String toString() {
        return (orderId != null ? orderId : "") + "\u0001" +
                (orderLineId != null ? orderLineId : "") + "\u0001" +
                (subTypeCode != null ? subTypeCode : "") + "\u0001" +
                (subTypeValue != null ? subTypeValue : "") + "\u0001" +
                (developDate != null ? developDate : "") + "\u0001" +
                (developManagerId != null ? developManagerId : "") + "\u0001" +
                (developManagerName != null ? developManagerName : "") + "\u0001" +
                (developStaffId != null ? developStaffId : "") + "\u0001" +
                (developStaffName != null ? developStaffName : "") + "\u0001" +
                (developNickName != null ? developNickName : "") + "\u0001" +
                (developNickId != null ? developNickId : "") + "\u0001" +
                (developContact != null ? developContact : "") + "\u0001" +
                (provinceCode != null ? provinceCode : "") + "\u0001" +
                (developProvinceCode != null ? developProvinceCode : "") + "\u0001" +
                (developCity != null ? developCity : "") + "\u0001" +
                (developDepartName != null ? developDepartName : "") + "\u0001" +
                (developDepartId != null ? developDepartId : "") + "\u0001" +
                (standardKindCode != null ? standardKindCode : "") + "\u0001" +
                (modifyTag != null ? modifyTag : "") + "\u0001" +
                (developType != null ? developType : "") + "\u0001" +
                (channelId != null ? channelId : "") + "\u0001" +
                (codeTypeCode != null ? codeTypeCode : "") + "\u0001" +
                (channelType != null ? channelType : "") + "\u0001" +
                (opt != null ? opt : "") + "\u0001" +
                (dataReceiveTime != null ? dataReceiveTime : "") +
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }
}
