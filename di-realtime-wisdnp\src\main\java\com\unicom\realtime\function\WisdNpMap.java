package com.unicom.realtime.function;

import com.unicom.realtime.bean.WisdNpData;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.functions.MapFunction;

public class WisdNpMap implements MapFunction<String, WisdNpData> {

    @Override
    public WisdNpData map(String value) throws Exception {
        String[] cols = StringUtils.splitPreserveAllTokens(value, "\u0001");
        WisdNpData wisdNpData = new WisdNpData();
        wisdNpData.setProvince_code(cols[0]!=null && !"".equals(cols[0])&&!"null".equals(cols[0]) ? cols[0]:null);
        wisdNpData.setEparchy_code(cols[1]!=null && !"".equals(cols[1])&&!"null".equals(cols[1]) ? cols[1]:null);
        wisdNpData.setUser_id(cols[2]!=null && !"".equals(cols[2])&&!"null".equals(cols[2]) ? Long.valueOf(cols[2]):null);
        wisdNpData.setSerial_number(cols[3]!=null && !"".equals(cols[3])&&!"null".equals(cols[3]) ? cols[3]:null);
        wisdNpData.setParent_chnl_kind_name(cols[4]!=null && !"".equals(cols[4])&&!"null".equals(cols[4]) ? cols[4]:null);
        wisdNpData.setChnl_code(cols[5]!=null && !"".equals(cols[5])&&!"null".equals(cols[5]) ? cols[5]:null);
        wisdNpData.setChnl_name(cols[6]!=null && !"".equals(cols[6])&&!"null".equals(cols[6]) ? cols[6]:null);
        wisdNpData.setHome_net(cols[7]!=null && !"".equals(cols[7])&&!"null".equals(cols[7]) ? cols[7]:null);
        wisdNpData.setPort_out_net(cols[8]!=null && !"".equals(cols[8])&&!"null".equals(cols[8]) ? cols[8]:null);
        wisdNpData.setPort_in_net(cols[9]!=null && !"".equals(cols[9])&&!"null".equals(cols[9]) ? cols[9]:null);
        wisdNpData.setPort_time(cols[10]!=null && !"".equals(cols[10])&&!"null".equals(cols[10]) ? Long.valueOf(cols[10]):null);
        wisdNpData.setOpen_date(cols[11]!=null && !"".equals(cols[11])&&!"null".equals(cols[11]) ? Long.valueOf(cols[11]):null);
        wisdNpData.setProduct_id(cols[12]!=null && !"".equals(cols[12])&&!"null".equals(cols[12]) ? Long.valueOf(cols[12]):null);
        wisdNpData.setProduct_name(cols[13]!=null && !"".equals(cols[13])&&!"null".equals(cols[13]) ? cols[13]:null);
        wisdNpData.setNet_type(cols[14]!=null && !"".equals(cols[14])&&!"null".equals(cols[14]) ? cols[14]:null);
        if(wisdNpData.getPort_time()!=null){
            wisdNpData.setPort_date(DateFormatUtils.format(wisdNpData.getPort_time(), "yyyyMMdd"));
        }else{
            wisdNpData.setPort_date("00000000");
        }

        return wisdNpData;
    }
}
