package com.unicom.rts.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023-06-19 17:08
 */

@AllArgsConstructor
@Getter
public enum ThrEnum {

    /**
     * 套内阈值枚举
     */
    deviceNumber,
    /**
     * 套内阈值枚举
     */
    smsNoticeId,
    /**
     * 套内阈值枚举
     */
    eparchyCode,
    /**
     * 套内阈值枚举
     */
    earlyWarningType,
    /**
     * 套内阈值枚举
     */
    domesticJacketUsedflow,
    /**
     * 套内阈值枚举
     */
    domesticJacketResidualflow,
    /**
     * 套内阈值枚举
     */
    domesticOutsetUsedflow,
    /**
     * 套内阈值枚举
     */
    dealTime,
    /**
     * 套内阈值枚举
     */
    intime,
    /**
     * 套内阈值枚举
     */
    datasource,
    /**
     * 套内阈值枚举
     */
    saturationThreshold,
    /**
     * 套内阈值枚举
     */
    reserve1,
    /**
     * 套内阈值枚举
     */
    cdhTime;
}
