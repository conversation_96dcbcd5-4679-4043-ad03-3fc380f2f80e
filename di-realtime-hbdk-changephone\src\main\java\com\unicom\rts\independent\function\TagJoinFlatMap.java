package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocData;
import com.unicom.rts.independent.bean.LocStateBean;
import com.unicom.rts.independent.bean.OutDataBean;
import com.unicom.rts.independent.bean.UserTagStateBean;
import com.unicom.rts.independent.util.DistanceUtil;
import com.unicom.rts.independent.util.HbaseUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 */
public class TagJoinFlatMap extends RichFlatMapFunction<LocData, Tuple2<String, String>> {
    private final static Logger logger = LoggerFactory.getLogger(TagJoinFlatMap.class);
    private ValueState<UserTagStateBean> userTagState;
    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private static final byte[] FAMILYNAME = "f".getBytes();
    ValueState<LocStateBean> locState;
    ParameterTool conf;
    String topic;
    private final transient long valueToExpose = 0L;
    int locDataSecondDiff = 0;
    String provId;

    public TagJoinFlatMap(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        provId = conf.get("provId");
        topic = conf.get("sink.topic");
        locDataSecondDiff = conf.getInt("locDataSecondDiff", 3600);
        //初始化hbase链接
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));
        //状态ttl
        StateTtlConfig locTtlConfig = StateTtlConfig
                .newBuilder(Time.days(7))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .cleanupInRocksdbCompactFilter(6000)
                .build();

        //驻留触发
        ValueStateDescriptor<LocStateBean> locStateDes = new ValueStateDescriptor<>("StayState", LocStateBean.class);
        locStateDes.enableTimeToLive(locTtlConfig);
        locState = getRuntimeContext().getState(locStateDes);

        //时延统计metric
        getRuntimeContext()
                .getMetricGroup()
                .gauge("proDelay", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return valueToExpose;
                    }
                });

        StateTtlConfig userTagTtlConfig = StateTtlConfig
                .newBuilder(Time.days(10))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .cleanupInRocksdbCompactFilter(6000)
                .build();


        ValueStateDescriptor<UserTagStateBean> userTagStateDes = new ValueStateDescriptor<>("userTagState", UserTagStateBean.class);
        userTagStateDes.enableTimeToLive(userTagTtlConfig);
        userTagState = getRuntimeContext().getState(userTagStateDes);

    }


    @Override
    public void flatMap(LocData locData, Collector<Tuple2<String, String>> collector) throws Exception {
        try {
            locData = jointag(locData);
            if (null == locData) {
                return;
            }
            LocStateBean lastState = locState.value();

            double lon = Double.parseDouble(locData.getLon());
            double lat = Double.parseDouble(locData.getLat());
            if (lastState == null) {
                locState.update(new LocStateBean(locData, lon, lat));
                return;
            } else {
                /*if (!locData.getLacci().equals(lastState.getLac() + "_" + lastState.getCi())) {
                    System.out.println("locData >>>>>>>>>>>>>> " + locData);
                    System.out.println("lastState >>>>>>>>>>>>>> " + lastState);
                }*/
                String lastImei = lastState.getImei().substring(0,14);
                String newImei = locData.getImei().substring(0,14);
                if (lastImei.equals(newImei)) {
                    locState.update(new LocStateBean(locData, lon, lat));
                    return;
                }
                long lastTime = lastState.getTime();
                long newTime = locData.getTime();
                long timeDiff = Math.abs(newTime - lastTime) / 1000;
                if (timeDiff < locDataSecondDiff) {
                    locState.update(new LocStateBean(locData, lon, lat));
                    return;
                }
                String key = "hbdk_" + locData.getDeviceNumber() + locData.getUserTagStateBean().getInDateMonth();
                if (notSendOut(key)) {
                    // 下发数据
                    long distance = (long) DistanceUtil.getDistance(lon, lat, lastState.getLon(), lastState.getLat());

                    OutDataBean out = new OutDataBean(locData, lastState, timeDiff, distance);
                    // logger.info("collector.collect >>>>>>>>>>>>> {}", out);
                    collector.collect(new Tuple2<>(topic, out.toString()));
                } else {
                    locState.update(new LocStateBean(locData, lon, lat));
                }
            }
        } catch (Exception e) {
            logger.error(e.toString());
            logger.error(locData.toString());
        }
    }


    public LocData jointag(LocData locData) throws Exception {
        if ("0".equals(locData.getDatasource()) || "bus".equals(locData.getDatasource())) {
            userTagState.update(locData.getUserTagStateBean());
            return null;
        }
        UserTagStateBean userTag = userTagState.value();
        String eparchyCode;
        String inDate = null;
        String provIdFinal;
        if (userTag != null) {
            if (StringUtils.isBlank(userTag.getProvIdFinal())){
                if (StringUtils.isNotBlank(userTag.getProvId())) {
                    provIdFinal = userTag.getProvId();
                } else if (StringUtils.isNotBlank(userTag.getK000046())) {
                    provIdFinal = userTag.getK000046();
                } else {
                    return null;
                }
                userTag.setProvIdFinal(provIdFinal);
                userTagState.update(userTag);
            } else {
                provIdFinal = userTag.getProvIdFinal();
            }
            // 非河北用户返回
            if (!provIdFinal.equals(provId)) {
                return null;
            }

            if (StringUtils.isBlank(userTag.getEparchyCodeFinal())){
                if (StringUtils.isNotBlank(userTag.getEparchyCode())) {
                    eparchyCode = userTag.getEparchyCode();
                } else if (StringUtils.isNotBlank(userTag.getK002436())) {
                    eparchyCode = userTag.getK002436();
                } else {
                    return null;
                }
                userTag.setEparchyCodeFinal(eparchyCode);
                userTagState.update(userTag);
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            if (StringUtils.isBlank(userTag.getInDate())) {
                if (StringUtils.isNotBlank(userTag.getFinishDate())) {
                    inDate = df.format(new Date(Long.parseLong(userTag.getFinishDate())));
                } else if (StringUtils.isNotBlank(userTag.getK003589())) {
                    if ( userTag.getK003589().length() == 14) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                        Date date = simpleDateFormat.parse(userTag.getK003589());
                        inDate = df.format(date);
                    } else if (userTag.getK003589().length() == 8) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                        Date date = simpleDateFormat.parse(userTag.getK003589());
                        inDate = df.format(date);
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
                userTag.setInDate(inDate);
                userTag.setInDateMonth(inDate.substring(0,7));
                userTagState.update(userTag);
            } else {
                inDate = userTag.getInDate();
            }
        } else {
            return null;
        }
        if (StringUtils.isNotBlank(inDate)) {
            if (calculateDurationDays(inDate) > 90) {
                return null;
            }
        } else {
            return null;
        }

        locData.setUserTagStateBean(userTag);
        return locData;
    }

    public static Long calculateDurationDays(String targetDate) throws Exception {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String todayString = df.format(new Date());
        Date target = df.parse(targetDate);
        Date today = df.parse(todayString);
        // logger.info("target >>>>>>> " + target);
        // logger.info("today >>>>>>> " + today);
        return (today.getTime() - target.getTime()) / (24 * 60 * 60 * 1000L);
    }


    public boolean notSendOut(String key) throws IOException {
        //先查询，是否下发过，下发过result isEmpty true
        Result result = hbaseUtil.getDataFromHbase(key, table);
        //未下发过，需要下发，且向hbase中存储
        if (result.isEmpty()) {
            //负载均衡
            Put put = new Put(Bytes.toBytes(key));
            put.addColumn(FAMILYNAME, "key".getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8));
            if (!put.isEmpty()) {
                table.put(put);
            }
        }
        return result.isEmpty();
    }

    // public static void main(String[] args) throws Exception {
    //     SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    //     String inDate = df.format(new Date(1665554798000L));
    //     logger.info(calculateDurationDays(inDate) + "");
    //     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
    //     Date date = simpleDateFormat.parse("20221018202233");
    //     String inDate2 = df.format(date);
    //     logger.info(calculateDurationDays(inDate2) + "");
    //     Date date2 = simpleDateFormat.parse("20221018202233");
    //     logger.info(df.format(date2));
    //     System.out.println("2022-10-20".substring(0,7));
    // }
}
