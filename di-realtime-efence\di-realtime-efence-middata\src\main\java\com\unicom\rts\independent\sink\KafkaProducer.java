package com.unicom.rts.independent.sink;

import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;

import java.util.Properties;

public class KafkaProducer {

    public static FlinkKafkaProducer<MidDataBean> getKafkaProducer(ParameterTool conf) {
        Properties producerProp = new Properties();
        producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
        producerProp.put("acks", "0");//Must set acks to all in order to use the idempotent producer
        producerProp.put("retries", "3");
        producerProp.put("batch.size", "16384");
        producerProp.put("linger.ms", "50");
        producerProp.put("buffer.memory", "33554432");
        producerProp.put("request.timeout.ms", "120000");

        return new FlinkKafkaProducer<>(
                "all",      // target topic
                new xinanKafkaSerializationSchema(conf.get("sink.kafka.topic")),    // serialization schema
                producerProp,                // producer config
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE, 5);
    }

}
