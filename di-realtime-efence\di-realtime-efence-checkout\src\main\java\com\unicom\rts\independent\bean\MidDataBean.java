package com.unicom.rts.independent.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/19
 **/
@Data
public class MidDataBean {

    private String deviceNumber;

    private String time;

    private String hprovName;

    private String hareaName;

    private String area;

    private String imei;

    private String imsi;

    private String currentTime;

    private String lac;

    private String ci;

    private String longitude;

    private String latitude;

    private String provId;

    private String poweroffInd;

    private String sourceTopic;

    @Override
    public String toString() {
        return deviceNumber
                + "|" + time
                + "|" + hprovName
                + "|" + hareaName
                + "|" + area
                + "|" + imei
                + "|" + imsi
                + "|" + currentTime
                + "|" + lac
                + "|" + ci
                + "|" + longitude
                + "|" + latitude
                + "|" + provId
                + "|" + poweroffInd
                + "|" + sourceTopic;
    }
}
