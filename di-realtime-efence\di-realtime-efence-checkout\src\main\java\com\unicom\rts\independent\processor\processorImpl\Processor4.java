package com.unicom.rts.independent.processor.processorImpl;

import com.unicom.rts.independent.processor.AbstractProcessor;
import org.apache.flink.api.java.tuple.Tuple2;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.unicom.rts.independent.utils.FilterUtil.filterByGroovy;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/19
 **/

/**
 * 过滤条件，groovy（groovy中仅过滤）
 * 输出 record
 * 缅北iot
 */
public class Processor4 extends AbstractProcessor {
    @Override
    public void subProcess() throws Exception {
        if (null != recordBean && null != ruleBean.getParam() &&
                filterByGroovy(recordBean, ruleBean.getParam()).equals(true)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            checkoutState.put(recordBean.getDeviceNum(), sdf.format(new Date()));
            out.collect(new Tuple2<>(ruleBean.getSinkTopic(), recordBean.getRecord()));
        }
    }
}
