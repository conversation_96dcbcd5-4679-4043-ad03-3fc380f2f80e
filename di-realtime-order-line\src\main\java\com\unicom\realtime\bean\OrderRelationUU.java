package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderRelationUU {
    private String orderId;
    private String shortCode;
    private String modifyTag;
    private String serialNumberB;
    private String serialNumberA;
    private String relationTypeCode;
    private String itemId;
    private String roleCodeA;
    private String roleCodeB;
    private String startDate;
    private String endDate;
    private String orderno;
    private String idB;
    private String idA;
    private String provinceCode;
    private String opt;
    private String opttime;
    private String cdhtime;
    private String dataReceiveTime;

    @Override
    public String toString() {
        return (orderId != null ? orderId : "") + "\u0001" +
                (shortCode != null ? shortCode : "") + "\u0001" +
                (modifyTag != null ? modifyTag : "") + "\u0001" +
                (serialNumberB != null ? serialNumberB : "") + "\u0001" +
                (serialNumberA != null ? serialNumberA : "") + "\u0001" +
                (relationTypeCode != null ? relationTypeCode : "") + "\u0001" +
                (itemId != null ? itemId : "") + "\u0001" +
                (roleCodeA != null ? roleCodeA : "") + "\u0001" +
                (roleCodeB != null ? roleCodeB : "") + "\u0001" +
                (startDate != null ? startDate : "") + "\u0001" +
                (endDate != null ? endDate : "") + "\u0001" +
                (orderno != null ? orderno : "") + "\u0001" +
                (idB != null ? idB : "") + "\u0001" +
                (idA != null ? idA : "") + "\u0001" +
                (provinceCode != null ? provinceCode : "") + "\u0001" +
                (opt != null ? opt : "") + "\u0001" +
                (dataReceiveTime != null ? dataReceiveTime : "") +
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }
}
