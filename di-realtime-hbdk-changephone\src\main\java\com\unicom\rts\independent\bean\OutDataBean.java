package com.unicom.rts.independent.bean;

import com.unicom.rts.independent.util.DistanceUtil;
import lombok.Data;

@Data
public class OutDataBean {

    private final String SEPARATOR = "\u0001";
    String eparchyCode;
    String deviceNumber;
    String newImei;
    String lastImei;
    long secondDiff;
    String newLac;
    String newCi;
    String lastLac;
    String lastCi;
    long distance;
    long processTime;
    long newTime;
    long lastTime;


    public OutDataBean(LocData loc, LocStateBean locState, long timeDiff, long dist) {
        this.eparchyCode = loc.getUserTagStateBean().getEparchyCodeFinal();
        this.deviceNumber = loc.getDeviceNumber();
        this.newImei = loc.getImei();
        this.lastImei = locState.getImei();
        this.secondDiff = timeDiff;
        this.newLac = loc.getLac();
        this.newCi = loc.getCi();
        this.lastLac = locState.getLac();
        this.lastCi = locState.getCi();
        this.distance = dist;
        this.processTime = System.currentTimeMillis();
        this.newTime = loc.getTime();
        this.lastTime = locState.getTime();
    }


    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(eparchyCode);
        sb.append(SEPARATOR);
        sb.append(deviceNumber);
        sb.append(SEPARATOR);
        sb.append(newImei);
        sb.append(SEPARATOR);
        sb.append(lastImei);
        sb.append(SEPARATOR);
        sb.append(secondDiff);
        sb.append(SEPARATOR);
        sb.append(newLac);
        sb.append(SEPARATOR);
        sb.append(newCi);
        sb.append(SEPARATOR);
        sb.append(lastLac);
        sb.append(SEPARATOR);
        sb.append(lastCi);
        sb.append(SEPARATOR);
        sb.append(distance);
        sb.append(SEPARATOR);
        sb.append(processTime);
        sb.append(SEPARATOR);
        sb.append(newTime);
        sb.append(SEPARATOR);
        sb.append(lastTime);

        return sb.toString();
    }
}
