package com.unicom.rts;

import com.unicom.rts.bean.SourceData;
import com.unicom.rts.function.DataFlatMap;
import com.unicom.rts.sink.DataSinkFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.unicom.rts.source.BroadbandVoiceKafkaSource.getFlinkKafkaConsumer;

public class Main {

    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        // 获取checkpoint 参数
        Integer checkpointInterval = conf.getInt("checkpointInterval", 5);
        Integer checkpointTimeout = conf.getInt("checkpointTimeout", 10);
        Integer minPauseBetweenCheckpoints = conf.getInt("minPauseBetweenCheckpoints", 6);
        String checkPointDir = conf.get("checkpointDataUri");
        // 根topic流,根据mobile分区
        String topicNameAll = conf.get("source.topic.all.prefix");

        List<String> topicList = new ArrayList<String>();
        topicList.add(topicNameAll);
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // backend
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.getCheckpointConfig().setCheckpointStorage(checkPointDir);
        // checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(checkpointInterval));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetweenCheckpoints));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        // 保存checkpoint
        checkpointConf.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));
        FlinkKafkaConsumerBase<ConsumerRecord<String, String>> bbvConsumer = getFlinkKafkaConsumer(conf, topicList);
        SingleOutputStreamOperator<ConsumerRecord<String, String>> inputStream = env.addSource(bbvConsumer).uid("bbvConsumerSource").name("bbvConsumerSource");

        SingleOutputStreamOperator<SourceData> bbvStream = inputStream
                .flatMap(new DataFlatMap(conf)).uid("bbvStreamFlatMap").name("bbvStreamFlatMap");

        bbvStream.addSink(new DataSinkFunction(conf)).name("sinkData").uid("sinkData");

        env.execute();
    }



}
