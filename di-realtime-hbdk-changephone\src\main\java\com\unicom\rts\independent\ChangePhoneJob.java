/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.unicom.rts.independent;

import com.unicom.rts.independent.bean.LocData;
// import com.unicom.rts.independent.cdc.ServerIdUtil;
import com.unicom.rts.independent.function.*;
import com.unicom.rts.independent.source.KafkaSource;
// import com.unicom.rts.independent.source.MySqlCDCSource;
import com.unicom.rts.independent.source.MySqlSource;
import com.unicom.rts.independent.source.UserTagFromHDFSource;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class ChangePhoneJob {
    private final static Logger logger = LoggerFactory.getLogger(ChangePhoneJob.class);
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String provId = conf.get("provId");
        String topicLoc = conf.get("udp.source.topic");
        String topicRoam = conf.get("roam.source.topic");
        String topicBus = conf.get("bus.source.topic");
        int checkpointInterval = conf.getInt("checkpointInterval", 5);
        int checkpointTimeout = conf.getInt("checkpointTimeout", 10);
        int minPauseBetweenCheckpoints = conf.getInt("minPauseBetweenCheckpoints", 6);
        long udpDelayMinute = conf.getLong("udp.delayMinute");
        long roamDelayMinute = conf.getLong("roam.delayMinute");
        String checkPointDir = conf.get("checkpointDataUri");


        // 4G和5G 信令
        Map<String, String> saConfMap = new HashMap<>();
        saConfMap.put("sa.source.parallelism", conf.get("5gSa.source.parallelism"));
        saConfMap.put("sa.source.bootstrap", conf.get("5gSa.source.kafka.properties.bootstrap"));
        saConfMap.put("sa.group.id", conf.get("sa.group.id"));
        saConfMap.put("sa.source.kafka.security", conf.get("5gSa.source.kafka.properties.security", "false"));
        ParameterTool saConf = ParameterTool.fromMap(saConfMap);
        saConf = conf.mergeWith(saConf);
        String saTopic = conf.get("5gSa.topic", "");

        boolean isSa = conf.getBoolean("is.access.5gSa", true);



        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(conf);
        env.disableOperatorChaining();
        // backend
        EmbeddedRocksDBStateBackend rocksDbStateBackend = new EmbeddedRocksDBStateBackend(true);
        //设置为机械硬盘+内存模式
        rocksDbStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);
        env.setStateBackend(rocksDbStateBackend);
        env.getCheckpointConfig().setCheckpointStorage(checkPointDir);
        //checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(checkpointInterval));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetweenCheckpoints));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        // 保存checkpoint
        checkpointConf.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        // 获取serverId
        // String serverIdRange = ServerIdUtil.serverIdGenerator(conf);
        // if ("".equals(serverIdRange)) {
        //     System.out.println("cdc生成serverId失败");
        //     return;
        // }
        // source: mysql规则信息变更流
        // SingleOutputStreamOperator<String> lacCiInfoStream = env.fromSource(MySqlCDCSource.create(conf, serverIdRange), WatermarkStrategy.noWatermarks(), "CDCSource")
        //         .uid("CDCSourceUID").setParallelism(conf.getInt("mysql.cdc.parallelism", 1));
        // KeyedStream<LacciCDCInfo, String> lacCiKeyedStream = lacCiInfoStream
        //         .flatMap(new LacciFlatMap(provId)).uid("TradeAreaFlatMapUID").name("TradeAreaFlatMap")
        //         .keyBy(LacciCDCInfo::getLacCi);
        // final MapStateDescriptor<String, LacciCDCInfo> broadcastConfig = new MapStateDescriptor<>("lacciState", String.class, LacciCDCInfo.class);
        // BroadcastStream<LacciCDCInfo> lacciBroadcastStream = lacCiKeyedStream.broadcast(broadcastConfig);

        // DataStream<LacciInfo> lacCiStream = lacCiInfoStream
        //         .flatMap(new LacciFlatMap(provId)).uid("TradeAreaFlatMapUID").name("TradeAreaFlatMap");
        // final MapStateDescriptor<String, LacciInfo> broadcastConfig = new MapStateDescriptor<>("lacciState", String.class, LacciInfo.class);
        // BroadcastStream<LacciInfo> lacciBroadcastStream = lacCiStream.broadcast(broadcastConfig);

        // final MapStateDescriptor<Void, List<LacciInfo>> broadcastConfig = new MapStateDescriptor<>(
        //         "ruleState",
        //         Types.VOID,
        //         Types.LIST(Types.POJO(LacciInfo.class)));
        // logger.info("Start >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> ");
        DataStream<LocData> lacCiStream = env.addSource(new MySqlSource(conf, provId)).uid("MySqlSource").name("MySqlSource").setParallelism(1);
                // .broadcast(broadcastConfig);

        DataStream<LocData> busStream = env.addSource(KafkaSource.getRootKafkaConsumer(conf, topicBus)).uid("busSource").name("busSource")
                .setParallelism(Integer.parseInt(conf.get("bus.source.parallelism")))
                .flatMap(new BusClean()).uid("BusCleanUID").name("BusClean");;
        DataStream<LocData> locStream = null;

        if(isSa){
            // add by wangjh 切换数据源
            locStream = env.addSource(KafkaSource.getSaKafkaConsumer(saConf, saTopic)).uid("locSource").name("locSource")
                    .setParallelism(Integer.parseInt(saConf.get("5gSa.source.parallelism")))
                    .flatMap(new SaClean(udpDelayMinute)).uid("LocSaCleanSaUID").name("LocSaClean").setParallelism(Integer.parseInt(saConf.get("5gSa.source.parallelism")));
        }else {
            locStream = env.addSource(KafkaSource.getTgKafkaConsumer(conf, topicLoc)).uid("locSource").name("locSource")
                    .setParallelism(Integer.parseInt(conf.get("loc.source.parallelism")))
                    .flatMap(new LocClean(udpDelayMinute)).uid("LocCleanUID").name("LocClean");
        }

        DataStream<LocData> roamStream = env.addSource(KafkaSource.getCjzhKafkaConsumer(conf, topicRoam)).uid("roamSource").name("roamSource")
                .setParallelism(Integer.parseInt(conf.get("roam.source.parallelism")))
                .flatMap(new RoamClean()).uid("RoamCleanUID").name("RoamClean");

        // 用户标签数据源
        SingleOutputStreamOperator<LocData> userTag = env.addSource(new UserTagFromHDFSource(conf)).uid("userTagFromHDFSourceUID").name("userTagFromHDFSource")
                .setParallelism(conf.getInt("userTagRead.parallelism", 1));

        // DataStream: 规则计算数据流
        // DataStream<LocData> joinTagStream = locStream
        //         .union(userTag)
        //         .union(roamStream)
        //         .union(busStream)
        //         .keyBy(LocData::getDeviceNumber)
        //         .flatMap(new TagJoinFlatMap()).uid("TagJoinFlatMap").name("TagJoinFlatMap").setParallelism(conf.getInt("tagJoinFlatMap.parallelism", 20));
        //
        // DataStream<Tuple2<String, String>> outStream = joinTagStream
        //         .union(lacCiStream)
        //         .keyBy(LocData::getLacci)
        //         .flatMap(new LacciJoinFlatMap(conf)).uid("LacciJoinFlatMap").name("LacciJoinFlatMap").setParallelism(conf.getInt("lacciJoinFlatMap.parallelism", 1))
        //         .keyBy(LocData::getDeviceNumber)
        //         .flatMap(new CalcFlatMap(conf)).uid("CalcFlatMap").name("CalcFlatMap");


        DataStream<LocData>  joinLacciStream = locStream
                .union(roamStream)
                .union(lacCiStream)
                .keyBy(LocData::getLacci)
                .flatMap(new LacciJoinFlatMap(conf)).uid("LacciJoinFlatMap").name("LacciJoinFlatMap").setParallelism(conf.getInt("lacciJoinFlatMap.parallelism", 10));
                // .keyBy(LocData::getDeviceNumber)
                // .flatMap(new CalcFlatMap(conf)).uid("CalcFlatMap").name("CalcFlatMap");

        DataStream<Tuple2<String, String>> outStream = joinLacciStream
                .union(userTag)
                .union(busStream)
                .keyBy(LocData::getDeviceNumber)
                .flatMap(new TagJoinFlatMap(conf)).uid("TagJoinFlatMap").name("TagJoinFlatMap").setParallelism(conf.getInt("tagJoinFlatMap.parallelism", 20));

        // sink
        KafkaSource.addSink(outStream, conf);
        // execute program
        env.execute("hbdk_changePhone");
    }
}
