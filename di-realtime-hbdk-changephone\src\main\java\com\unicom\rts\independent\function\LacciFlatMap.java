// package com.unicom.rts.independent.function;
//
//
// import com.alibaba.fastjson.JSONObject;
// import com.unicom.rts.independent.bean.LacciInfo;
// // import com.unicom.rts.independent.bean.LacciInfo;
// import org.apache.flink.api.common.functions.RichFlatMapFunction;
// import org.apache.flink.util.Collector;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
//
// /**
//  * <AUTHOR>
//  */
// public class LacciFlatMap extends RichFlatMapFunction<String, LacciInfo> {
//     private final static Logger logger = LoggerFactory.getLogger(LacciFlatMap.class);
//     private String provId;
//
//     public LacciFlatMap(String provId) {
//         this.provId = provId;
//     }
//
//     @Override
//     public void flatMap(String value, Collector<LacciInfo> collector) {
//         JSONObject cdcJson = null;
//         try {
//             cdcJson = JSONObject.parseObject(value);
//             JSONObject lacciJson = cdcJson.getJSONObject("value");
//             //获取省分ID，只加载本省围栏变化数据
//             String provId = lacciJson.getString("prov_id");
//             if (this.provId.equals(provId)) {
//                 String lacci = lacciJson.getString("lacci");
//                 String operation = cdcJson.getString("op");
//                 String lon = lacciJson.getString("wgs84_lon");
//                 String lat = lacciJson.getString("wgs84_lat");
//                 String area = lacciJson.getString("area_id");
//                 String city = lacciJson.getString("district_id");
//
//                 LacciInfo lacciInfo = new LacciInfo(lacci, lon, lat, area, city, operation);
//                 // LacciCDCInfo lacciCdcInfo = null;
//                 // if ("u".equals(operation)) {
//                 //     JSONObject lacciInfoBefore = cdcJson.getJSONObject("before");
//                 //     String lacciBefore = lacciInfoBefore.getString("lacci");
//                 //     String lonBefore = lacciInfoBefore.getString("wgs84_lon");
//                 //     String latBefore = lacciInfoBefore.getString("wgs84_lat");
//                 //     String areaBefore = lacciInfoBefore.getString("area_id");
//                 //     String cityBefore = lacciInfoBefore.getString("district_id");
//                 //     LacciInfo lacciInfo = new LacciInfo(lacciBefore, lonBefore, latBefore, areaBefore, cityBefore);
//                 //     lacciCdcInfo = new LacciCDCInfo(lacci, lon, lat, area, city, operation, lacciInfo);
//                 // } else {
//                 //     lacciCdcInfo = new LacciCDCInfo(lacci, lon, lat, area, city, operation);
//                 // }
//
//                 collector.collect(lacciInfo);
//             }
//         } catch (Exception e) {
//             logger.error("Exception:{} ;tradeAreaJson {}", e, cdcJson);
//         }
//     }
// }
