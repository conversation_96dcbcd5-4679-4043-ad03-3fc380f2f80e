package com.unicom.rts.independent.function;


import com.unicom.rts.independent.bean.LocData;
import com.unicom.rts.independent.bean.UserTagStateBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Bus<PERSON>lean extends RichFlatMapFunction<String, LocData> {
    private final static Logger logger = LoggerFactory.getLogger(BusClean.class);

    @Override
    public void flatMap(String busStr, Collector<LocData> out) throws Exception {
        try {
            String[] line = StringUtils.splitPreserveAllTokens(busStr, "\u0001");
            if (44 != line.length) {
                logger.error("busStr Exception: busStr: {}", busStr);
                return;
            }
            String deviceNumber = line[14];
            if (!"018".equals(line[39])) {
                return;
            }
            // 过滤掉非新入网用户
            if (!("9".equals(line[5]) && "0".equals(line[43]) && ("10".equals(line[3]) || "592".equals(line[3])))) {
                return;
            }
            // 号码长度限制
            if (deviceNumber.length() != 11) {
                return;
            }

            LocData loc = new LocData();
            UserTagStateBean userTag = new UserTagStateBean();
            userTag.setUserId(line[8]);
            userTag.setEparchyCode(line[22]);
            userTag.setFinishDate(line[25]);
            userTag.setProvId(line[39]);
            loc.setUserTagStateBean(userTag);
            loc.setDeviceNumber(deviceNumber);
            loc.setDatasource("bus");
            out.collect(loc);
        } catch (Exception e) {
            logger.error("BusClean Exception:{} busStr:{}", e, busStr);
        }
    }
}
