package com.unicom.rts.independent.functions;

import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.List;

public class JoinLocImeiProcessFunction extends BroadcastProcessFunction<MidDataBean, List<Tuple2<String, String>>, MidDataBean> {

    private MapStateDescriptor<Void, List<Tuple2<String,String>>> stateDescriptor;

    public JoinLocImeiProcessFunction() {
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        stateDescriptor = new MapStateDescriptor<>("loc_imei",
                Types.VOID,
                Types.LIST(Types.TUPLE(Types.STRING,Types.STRING)));

    }

    @Override
    public void close() throws Exception {
    }

    @Override
    public void processElement(MidDataBean midDataBean, ReadOnlyContext ctx, Collector<MidDataBean> out) throws Exception {
        List<Tuple2<String, String>> locImeiList = ctx.getBroadcastState(stateDescriptor).get(null);
        for(Tuple2<String, String> locImeiTuple : locImeiList){
            String[] locOrImei = locImeiTuple.f0.split("|");
            String moduleName = locImeiTuple.f1;
            String valueImei = midDataBean.getImei();
            String valueLacci = midDataBean.getLac() + midDataBean.getCi();
            String valueProvId = midDataBean.getProvId();
            if(locOrImei.length == 1){
                String imeiPrefix = locOrImei[0];
                if(valueImei.startsWith(imeiPrefix)){
                    midDataBean.getImeiModuleName().add(moduleName);
                }
            }else if(locOrImei.length == 2){
                String lacci = locOrImei[0];
                String provId = locOrImei[1];
                if(lacci.equals(valueLacci) && provId.equals(valueProvId)){
                    midDataBean.getLocModuleName().add(moduleName);
                }
            }
        }
    }

    @Override
    public void processBroadcastElement(List<Tuple2<String, String>> value, Context ctx, Collector<MidDataBean> out) throws Exception {
        List<Tuple2<String, String>> locImeiList = ctx.getBroadcastState(stateDescriptor).get(null);
        locImeiList.clear();
        locImeiList.addAll(value);
    }
}
