package com.unicom.realtime.sink;

import com.unicom.realtime.bean.PhoneBill;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.Map;
import java.util.Properties;

public class KafkaSink extends RichSinkFunction<PhoneBill> {

    private KafkaProducer<String, String> kafkaProducer;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        Properties producerProp = new Properties();
        ExecutionConfig.GlobalJobParameters globalJobParameters = getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        Map<String, String> confMap = globalJobParameters.toMap();
        producerProp.setProperty("bootstrap.servers", confMap.get("sink.kafka.bootstrap"));
        producerProp.put("group.id", "diBillDueRemindFlinkJobKafkaProducer");
        producerProp.put("enable.auto.commit", "true");
        producerProp.put("auto.commit.interval.ms", "100");
        producerProp.put("session.timeout.ms", "30000");

        producerProp.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\""
                + confMap.get("sink.kafka.user") + "\" password=\"" + confMap.get("sink.kafka.password") + "\";");
        producerProp.setProperty("security.protocol", confMap.get("security.protocol"));
        producerProp.setProperty("sasl.mechanism", confMap.get("sasl.mechanism"));
        producerProp.put("key.serializer", StringSerializer.class.getName());
        producerProp.put("value.serializer", StringSerializer.class.getName());
        kafkaProducer = new KafkaProducer<>(producerProp);
    }

    @Override
    public void invoke(PhoneBill value, Context context) throws Exception {
        ProducerRecord record = new ProducerRecord<>("ZT_STRATEGY_9900_DTS01", value.toString());
        kafkaProducer.send(record);
    }

    @Override
    public void close() throws Exception {
        super.close();
        kafkaProducer.close();
    }
}
