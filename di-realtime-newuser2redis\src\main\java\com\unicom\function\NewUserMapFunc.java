package com.unicom.function;

import com.unicom.beans.NewUserBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/23
 **/
public class NewUserMapFunc extends ProcessFunction<ConsumerRecord<String, String>, NewUserBean> {

    private OutputTag<NewUserBean> errorDataStream;

    public NewUserMapFunc(OutputTag<NewUserBean> errorDataStream) {
        this.errorDataStream = errorDataStream;
    }

    @Override
    public void processElement(ConsumerRecord<String, String> record, Context ctx, Collector<NewUserBean> out) throws Exception {
        String inputStr = record.value();
        if (inputStr != null && inputStr.length() > 0) {
            String[] columns = StringUtils.splitPreserveAllTokens(inputStr, "\001");
            NewUserBean newUserBean = new NewUserBean();
            newUserBean.setDeviceNumber(columns[14]);
            newUserBean.setCustId(columns[9]);
            newUserBean.setAcceptDate(columns[16]);
            newUserBean.setFinishDate(columns[25]);
            newUserBean.setTradeStaffId(columns[18]);
            newUserBean.setTradeDepartId(columns[19]);
            newUserBean.setProductId(columns[6]);

            //新增
            newUserBean.setProvId(columns[39]);
            newUserBean.setEparchyCode(columns[22]);
            newUserBean.setUserId(columns[8]);

            if (isErrorData(newUserBean)) {
                ctx.output(errorDataStream, newUserBean);
            } else {
                out.collect(newUserBean);
            }
        }
    }

    private boolean isErrorData(NewUserBean newUserBean) {
        String deviceNumber = newUserBean.getDeviceNumber();
        if (!((StringUtils.isNumeric(deviceNumber)) && ((StringUtils.length(deviceNumber) == 11)
                || ((StringUtils.length(deviceNumber) == 13) && (StringUtils.startsWith(deviceNumber, "14000")
                || StringUtils.startsWith(deviceNumber, "10646")))
                || (StringUtils.startsWith(deviceNumber, "86")
                || StringUtils.startsWith(deviceNumber, "086")
                || StringUtils.startsWith(deviceNumber, "0086"))))) {
            return true;
        }
        return false;
    }
}
