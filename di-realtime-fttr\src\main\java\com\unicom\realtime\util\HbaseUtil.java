package com.unicom.realtime.util;

import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class HbaseUtil {

    private final static Logger logger = LoggerFactory.getLogger(HbaseUtil.class);

    private static Map<String, HTable> tablePool = new HashMap<>();

    public static Result getKeyValue(Connection connection, String hbaseTableName, byte[] rowKey) {
        Result ret = new Result();
        try {
            if (tablePool.containsKey(hbaseTableName)) {

                return tablePool.get(hbaseTableName).get(new Get(rowKey));
            }
            tablePool.put(hbaseTableName, (HTable) connection.getTable(TableName.valueOf(hbaseTableName)));
            return tablePool.get(hbaseTableName).get(new Get(rowKey));
        } catch (Exception e) {
            logger.error("Exception:{}", e.getMessage());
        }
        return ret;
    }

}
