package com.unicom.rts.util;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-15 11:21
 */
public class HbaseUtil {

    private final static Logger logger = Logger.getLogger(HbaseUtil.class);

    private Configuration conf = null;
    private UserGroupInformation ugi = null;
    private Connection connection = null;

    private static String hbaseEncode = "utf8";// 编码格式

    public Connection init(String zkIp, String port, String user) {
        Configuration hbaseConfig = new Configuration();
        hbaseConfig.set("hbase.zookeeper.quorum", zkIp);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", port);// zookeeper端口
        hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
        conf = HBaseConfiguration.create(hbaseConfig);
        try {
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(user);
            connection = ConnectionFactory.createConnection(conf, User.create(userGroupInformation));
            logger.info("成功连接Hbase {}" + conf.toString());
            return connection;
        } catch (IOException e) {
            logger.error("获取Hbase连接异常，" + e.getClass() + "：" + e.getMessage());
            return null;
        }
    }

    public HTable getTable(String tableName) {
        try {
            HTable table = (HTable) connection.getTable(TableName.valueOf(tableName));
            //HTable table1 = new HTable(conf, Bytes.toBytes(tableName));
//            System.out.println("获取 HTable 对象成功 " + table.toString());
            return table;
        } catch (IOException e) {
            logger.error("获取 HTable 对象失败 IOException：{}",e);
            return null;
        }
    }

    public static Result selectDataByRowkey(HTable table, String rowKey) throws IOException {
        //获取表描述对象
        Result result = null;
        Get get = new Get(Bytes.toBytes(rowKey));
        result = table.get(get);
        return result;
    }

    public static byte[] getRowKey(String deviceNumber) {
        byte[] deviceNumberByte = Bytes.toBytes(deviceNumber);
        byte[] deviceNumberHash = Bytes.toBytes((short) (deviceNumber.hashCode() & 0x7fff));
        byte[] rowKey = Bytes.add(deviceNumberHash, deviceNumberByte);
        return rowKey;
    }


    public List<Cell> getValuesWithColumnsByDeviceNumber(HTable hTable, String rowKey, String columnfamily, List<String> columns) throws IOException, InterruptedException {
        try {
            if (StringUtils.isEmpty(hTable.getName().toString()) || StringUtils.isEmpty(rowKey)) {
                return null;
            }
//          logger.info( "getValuesWithColumns======>rowKey " + rowKey + "getValuesWithColumns======>columnfamily ："+ columnfamily );

            Get get = new Get(Bytes.toBytes(rowKey));
//            logger.warn("rowKey=" + rowKey);
            Result result = hTable.get(get);
//            System.out.println("result=====>" + get);
            for (String column : columns) {
                if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isNotEmpty(column)) {
//                                logger.info("getValuesWithColumns======>TableFamily " + columnfamily.getBytes(hbaseEncode) + "getValuesWithColumns======>TableColumn ：" + column.getBytes(hbaseEncode));
                    get.addColumn(Bytes.toBytes(columnfamily), Bytes.toBytes(column));
                }
            }
            boolean resultExists = result.isEmpty();
            if (resultExists) {
//                System.out.println(" 查询rowKeyStr " + rowKey + " 在Table ：" + hTable.getName() + " 无这条记录 ");
                return null;
            }

            return result.listCells();

        } catch (IOException e) {
            logger.error("Table :" + hTable.getName() + " get" + rowKey + "exception: " + e);
            // e.printStackTrace();
            return null;
        }
    }

    public List<Cell> getValuesWithColumns(HTable hTable, String rowKey, String columnfamily, List<String> columns) throws IOException, InterruptedException {
        try {
            if (StringUtils.isEmpty(hTable.getName().toString()) || StringUtils.isEmpty(rowKey)) {
                return null;
            }
//          logger.info( "getValuesWithColumns======>rowKey " + rowKey + "getValuesWithColumns======>columnfamily ："+ columnfamily );

            Get get = new Get(getRowKey(rowKey));
//            logger.warn("rowKey=" + rowKey);
            Result result = hTable.get(get);
//            System.out.println("result=====>" + get);
            for (String column : columns) {
                if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isNotEmpty(column)) {
//                                logger.info("getValuesWithColumns======>TableFamily " + columnfamily.getBytes(hbaseEncode) + "getValuesWithColumns======>TableColumn ：" + column.getBytes(hbaseEncode));
                    get.addColumn(Bytes.toBytes(columnfamily), Bytes.toBytes(column));
                }
            }
            boolean resultExists = result.isEmpty();
            if (resultExists) {
//                System.out.println(" 查询rowKeyStr " + rowKey + " 在Table ：" + hTable.getName() + " 无这条记录 ");
                return null;
            }

            return result.listCells();

        } catch (IOException e) {
            logger.error("Table :" + hTable.getName() + " get" + rowKey + "exception: " + e);
            return null;
        }
    }

    public Result getValuesWithColumns2(HTable hTable, String rowKey, String columnfamily, List<String> columns) throws IOException, InterruptedException {
        try {
            if (StringUtils.isEmpty(hTable.getName().toString()) || StringUtils.isEmpty(rowKey)) {
                return null;
            }

            Get get = new Get(Bytes.toBytes(rowKey));
            Result result = hTable.get(get);
            for (String column : columns) {
                if (StringUtils.isNotEmpty(columnfamily) && StringUtils.isNotEmpty(column)) {
                    get.addColumn(Bytes.toBytes(columnfamily), Bytes.toBytes(column));
                }
            }
            boolean resultExists = result.isEmpty();
            if (resultExists) {
                return null;
            }

            return result;

        } catch (IOException e) {
            logger.error("Table :" + hTable.getName() + " get" + rowKey + "exception: " + e);
            // e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据行号查询数据
     *
     * @param rowKey
     * @return
     * @throws IOException
     */
    public static Result selectDataByRowkey(Connection connection, Table table, String rowKey) throws IOException {
        byte[] rowKeyPrefix = Bytes.toBytes((short) (rowKey.hashCode() & 0x7FFF));
        byte[] key = Bytes.add(rowKeyPrefix, Bytes.toBytes(rowKey.toString()));
        Get get = new Get(key);
        Result result = table.get(get);
        return result;
    }



    public static Result getCellsData(Table table, String rowKey, String family, List<String> cols){
        Result result = null;
        try {
            Get get = new Get(Bytes.toBytes(rowKey));
            for (String col : cols) {
                get.addColumn(Bytes.toBytes(family),Bytes.toBytes(col));
            }
            result = table.get(get);
        } catch (IOException e) {
            logger.error(e.getCause().getMessage());
            // e.printStackTrace();
        }
        return result;
    }

    public static void insertTimeData(HTable table, String rowKey, String family, List<String> column, List<String> value) throws IOException {
        byte[] rowKeyPrefix = Bytes.toBytes((short) (rowKey.hashCode() & 0x7FFF));
        byte[] key = Bytes.add(rowKeyPrefix, Bytes.toBytes(rowKey.toString()));
        //获取添加对象
        Put put = new Put(key);
        //添加列
        for (int i = 0; i < column.size(); i++) {
            put.addColumn(Bytes.toBytes(family), Bytes.toBytes(column.get(i)), Bytes.toBytes(value.get(i)));
        }

        //添加
        table.put(put);
    }

}
