package com.unicom.rts.independent.source;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.independent.util.Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.common.KafkaFuture;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.TopicPartitionInfo;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicLong;

public class KafkaSource {

    /**
     * 组装Properties对象
     * @param map 数据库中kafka链接对象
     * @param properties nacos配置对象
     * @param isConsumer
     * @return
     */
    public static Properties getProps(Map<String,Object> map, Properties properties,boolean isConsumer){

        JSONObject json = JSON.parseObject(map.get("CONNECT_MESSAGE")+"");
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG,map.get("IP_CODE"));

        String encrypt = json.getString("kafka_encrypt_type");
        String username = json.getString("kafka_username");
        String password = json.getString("kafka_password");
        String mechanism = json.getString("kafka_sasl_mechanism");
        if(StringUtils.isNotEmpty(encrypt)){
            if("0".equals(encrypt)){
                props.put("security.protocol", "PLAINTEXT");
            }
            if("1".equals(encrypt)){
                props.put("security.protocol", "SASL_PLAINTEXT");
                if (StringUtils.isNotEmpty(mechanism)){
                    if("0".equals(mechanism)){
                        props.put("sasl.mechanism","PLAIN");
                    }
                    if("1".equals(mechanism)){
                        props.put("sasl.mechanism","SCRAM-SHA-256");
                    }
                }
            }
            //增加kafka鉴权方式
            if("2".equals(encrypt)){
                props.put("security.protocol", "SASL_SSL");
                props.put("ssl.truststore.location",properties.getProperty("kafka.ssl.truststore.location"));
                props.put("ssl.truststore.password",properties.getProperty("ssl.truststore.password"));
                props.put("ssl.endpoint.identification.algorithm","");
                if (StringUtils.isNotEmpty(mechanism)){
                    if("0".equals(mechanism)){
                        props.put("sasl.mechanism","PLAIN");
                    }
                    if("1".equals(mechanism)){
                        props.put("sasl.mechanism","SCRAM-SHA-256");
                    }
                }
            }
        }
        getProps(props,encrypt,mechanism,username,password);
        if(isConsumer){
            props.put("group.id", Util.getRandom());
            props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        }else{
            props.put(CommonClientConfigs.SESSION_TIMEOUT_MS_CONFIG,"600000");
        }
        return props;
    }
    private static void getProps(Properties props,String encrypt,String mechanism,String username,String password){
        if (StringUtils.isNotEmpty(username) && StringUtils.isNotEmpty(password)) {
            if ("0".equals(encrypt) || ( ("1".equals(encrypt)||"2".equals(encrypt)) && "0".equals(mechanism))) {
                props.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\""+username+"\" password=\""+password+"\";");
            }
            if ( ("1".equals(encrypt)||"2".equals(encrypt)) && "1".equals(mechanism) ) {
                props.put("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\""+username+"\" password=\""+password+"\";");
            }
        }
    }
    /**
     * 获取消费者对象
     * @param map
     * @return
     */
    public static KafkaConsumer<String,String> getConsumer(Map<String,Object> map){
        Properties properties = getProps(map,null,false);
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        return new KafkaConsumer<>(properties);
    }

    /**
     * 获取生产者对象
     * @param map
     * @return
     */
    public static KafkaProducer<String,String> getProducer(Map<String,Object> map,Properties prop){
        Properties properties = getProps(map,prop,false);
        properties.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        return new KafkaProducer<>(properties);
    }

    /**
     * 通过配置获取生产者对象
     * @param prop
     * @return
     */
    public static KafkaProducer<String,String> getProducer(Properties prop){
        Properties properties = new Properties();
        boolean isSecret = Boolean.parseBoolean(prop.getProperty("kfk.is.secret","false"));
        properties.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put(CommonClientConfigs.SESSION_TIMEOUT_MS_CONFIG,"600000");
        properties.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, prop.getProperty("bootstrap.server"));
        if (isSecret){
            String username = prop.getProperty("kfk.secret.user");
            String password = prop.getProperty("kfk.secret.password");
            // PLAINTEXT  SASL_PLAINTEXT
            String protocol = prop.getProperty("kfk.security.protocol");
            properties.put("security.protocol", protocol);
            // PLAIN  SCRAM-SHA-256
            String mechanism = prop.getProperty("kfk.sasl.mechanism");
            properties.put("sasl.mechanism", prop.getProperty("kfk.sasl.mechanism"));
            if ("PLAINTEXT".equals(protocol) || ("SASL_PLAINTEXT".equals(protocol) && "PLAIN".equals(mechanism))) {
                properties.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\""+username+"\" password=\""+password+"\";");
            }
            if ("SASL_PLAINTEXT".equals(protocol) && "SCRAM-SHA-256".equals(mechanism) ) {
                properties.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.ScramLoginModule required username=\""+username+"\" password=\""+password+"\";");
            }
        }
        return new KafkaProducer<>(properties);
    }
    /**
     * 通过consumer获取topic的offset
     * @param topic
     * @param map
     */
    public static void getOffsetByConsumer(String topic,Map<String,Object> map){
        KafkaConsumer<String,String> consumer = getConsumer(map);
        consumer.subscribe(Collections.singletonList(topic));
        consumer.poll(0); // 先进行一次poll来确保消费者有metadata更新
        Map<TopicPartition, Long> endOffsets = consumer.endOffsets(consumer.assignment());
        for (TopicPartition partition : consumer.assignment()) {
            System.out.println("Partition: " + partition.partition() + " - End Offset: " + endOffsets.get(partition));
        }
    }
    /**
     * 获取AdminClient对象
     * @param map
     * @return
     */
    public static AdminClient getAdminClient(Map<String,Object> map,Properties props) {
        return AdminClient.create(getProps(map,props,false));
    }
    /**
     * 通过AdminClient获取所有的TOPIC
     * @param adminClient
     * @return
     */
    public static Set<String> getTopics(AdminClient adminClient) {
        ListTopicsOptions options = new ListTopicsOptions();
        options.listInternal(false);
        Set<String> topicNames = null;
        try {
            ListTopicsResult topics = adminClient.listTopics(options);
            KafkaFuture<Set<String>> names = topics.names();
            topicNames = names.get();
        } catch (Exception e) {
            System.out.println("获取Topic失败：" + e.getMessage());
        }
        return topicNames;
    }

    /**
     * 通过AdminClient获取TOPIC对应的offset
     * @param adminClient
     * @param topic
     * @return
     */
    public static Long getOffset(AdminClient adminClient,String topic) throws Exception{
        long offsets = 0L;
        DescribeTopicsResult describeTopics = adminClient.describeTopics(Arrays.asList(topic));
        Collection<KafkaFuture<TopicDescription>> values = describeTopics.values().values();
        for (KafkaFuture<TopicDescription> value : values) {
            TopicDescription topicDescription = value.get();
            Map<TopicPartition, OffsetSpec> map = new HashMap<>();
            List<TopicPartitionInfo> list = topicDescription.partitions();
            for (TopicPartitionInfo topicPartitionInfo : list) {
                map.put(new TopicPartition(topicDescription.name(), topicPartitionInfo.partition()), OffsetSpec.latest());
            }
            ListOffsetsResult listOffsetsResult = adminClient.listOffsets(map);
            KafkaFuture<Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo>> result = listOffsetsResult.all();
            Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo> partitionMap = result.get();
            for (TopicPartition topicPartition : partitionMap.keySet()) {
                offsets = offsets + partitionMap.get(topicPartition).offset();
            }
        }
        return offsets;
    }

    /**
     * 获取消费者组 OFFSET
     * @param adminClient
     * @param topic
     * @return
     * @throws Exception
     */
    public static Map<String,Long> getCgOffset(AdminClient adminClient,String topic) throws Exception{
        Map<String,Long> offsets = new HashMap<>();
        // 获取所有消费者组的偏移量
        Map<String, ListConsumerGroupOffsetsResult> groupIdToOffsets = new HashMap<>();
        adminClient.listConsumerGroups().valid().get().forEach(group -> {
            ListConsumerGroupOffsetsResult offsetsResult = adminClient.listConsumerGroupOffsets(group.groupId(), new ListConsumerGroupOffsetsOptions());
            groupIdToOffsets.put(group.groupId(), offsetsResult);
        });
        // 打印偏移量
        groupIdToOffsets.forEach((groupId, offsetsResult) -> {
            Map<TopicPartition, OffsetAndMetadata> partitionToOffset = null;
            try {
                partitionToOffset = offsetsResult.partitionsToOffsetAndMetadata().get();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
//            System.out.println("Group ID: " + groupId);
            AtomicLong cgOffset = new AtomicLong(0L);
            partitionToOffset.forEach((partition, offset) -> {
//                System.out.println("Topic: " + partition.topic() + ", Partition: " + partition.partition() + ", Offset: " + offset.offset());
                cgOffset.addAndGet(offset.offset());
            });
            offsets.put(groupId, cgOffset.get());
        });
        return offsets;
    }
    /**
     * 释放资源
     * @param client
     * @param producer
     */
    public static void release(AdminClient client, Producer producer){
        if(producer != null){
            producer.close();
        }
        if (client != null ){
            client.close();
        }
    }

    public static void main(String[] args) {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG,"10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092");
        props.put("security.protocol", "SASL_PLAINTEXT");
        //SASL机制 PLAIN
        props.put("sasl.mechanism", "PLAIN");
        props.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"context\" password=\"context\";");
        props.put("group.id", Util.getRandom());
        props.put("key.Deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.Deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(CommonClientConfigs.SESSION_TIMEOUT_MS_CONFIG,"600000");
        Consumer consumer = new KafkaConsumer<>(props);
    }
}
