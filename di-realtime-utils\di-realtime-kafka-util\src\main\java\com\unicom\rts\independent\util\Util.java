package com.unicom.rts.independent.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Random;

public class Util {
    public static String getRandom(){
        String sources = "1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ"; // 数字加上一些字母，就可以生成6位的验证码
        Random random = new Random();
        StringBuffer flag = new StringBuffer();
        for (int j = 0; j < 6; j++){
            flag.append(sources.charAt(random.nextInt(36)) + "");
        }
        return flag.toString();
    }

    /**
     * 封装json 标准场景
     * @param offset
     * @param topic
     * @param rule
     * @return
     */
    public static JSONObject getJsonData(long offset, String topic, Map<String,Map<String,Object>> rule)throws Exception{
        JSONObject jsonObject = new JSONObject();
        Map<String,Object> topicRule = rule.get(topic);
        jsonObject.put("source","sscj");
        // 标准场景bussine_id（数字）配置化场景 code(字母)
        jsonObject.put("topicScenePrefix",StringUtils.isEmpty((String)getValue(topicRule,"SCENE_ID"))?"":getValue(topicRule,"SCENE_ID"));
        jsonObject.put("topicStatus", StringUtils.isEmpty((String)getValue(topicRule,"DEL_FLAG")) ? "0":getValue(topicRule,"DEL_FLAG"));
        jsonObject.put("type", "1");
        jsonObject.put("topicName",topic);
        jsonObject.put("topicOffset",offset);
        jsonObject.put("topicOffsetTime",getLocalDateNow());
        jsonObject.put("topicAccount",StringUtils.isEmpty((String)getValue(topicRule,"CREATE_USER"))?"":getValue(topicRule,"CREATE_USER"));
        jsonObject.put("topicExpire",getLocalDateTime(getValue(topicRule,"END_TIME")));
        jsonObject.put("isFixTopic","0");//是否固定topic 1：是  0：否
        jsonObject.put("kafkaUser","");
        jsonObject.put("KafkaGroupNo","");
        return jsonObject;
    }

    /**
     * 封装json 独立任务
     * @param offset 消费者组对应的offset
     * @param topic
     * @param alone 独立任务
     * @param cg 消费者组ID
     * @return
     * @throws Exception
     */
    public static JSONObject getJsonDataAlone(long offset, String topic, Map<String,Map<String,Object>> alone,String cg)throws Exception{
        JSONObject jsonObject = new JSONObject();
        Map<String,Object> topicRule = alone.get(topic);
        jsonObject.put("source","sscj");
        //场景前缀，场景动态topic必填，非场景类不填
        jsonObject.put("topicScenePrefix","");
        //topic状态 0： 正常    1：删除
        jsonObject.put("topicStatus", StringUtils.isEmpty((String)getValue(topicRule,"DEL_FLAG")) ? "0":getValue(topicRule,"DEL_FLAG"));
        jsonObject.put("type", "2");
        jsonObject.put("topicName",topic);
        jsonObject.put("topicOffset",offset);
        jsonObject.put("topicOffsetTime",getLocalDateNow());
        //场景动态topic必填，非场景类不填
        jsonObject.put("topicAccount","");
        //规则结束时间，场景动态topic必填，非场景类不填
        jsonObject.put("topicExpire","");
        jsonObject.put("isFixTopic","1");//是否固定topic 1：是  0：否
        jsonObject.put("kafkaUser","");
        jsonObject.put("KafkaGroupNo",cg);
        return jsonObject;
    }

    /**
     * 取值判断
     * @param map
     * @param key
     * @return
     */
    public static Object getValue(Map<String,Object> map ,String key) throws Exception{
        Object object = null;
        if(map != null && !map.isEmpty()){
            object = map.get(key);
        }
        return  object;
    }
    public static String getLocalDateNow(){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedString = now.format(formatter);
        return formattedString;
    }
    public static String getLocalDateTime(Object localDateTime){
        String formattedString = "";
        if(localDateTime != null ){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            formattedString = ((LocalDateTime)localDateTime).format(formatter);
        }
        return formattedString;
    }

}
