package com.unicom.realtime.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderPayRelation {
    private String orderId;
    private String orderLineId;
    private String userId;
    private String acctId;
    private String payitemCode;
    private String acctPriority;
    private String userPriority;
    private String addupMethod;
    private String addupMonths;
    private String bindType;
    private String startAcycId;
    private String endAcycId;
    private String defaultTag;
    private String actTag;
    private String limitType;
    private String limitValue;
    private String complementTag;
    private String provinceCode;
    private String opt;
    private String opttime;
    private String cdhtime;
    private String dataReceiveTime;

    @Override
    public String toString() {
        return (orderId != null ? orderId : "") + "\u0001" +
                (orderLineId != null ? orderLineId : "") + "\u0001" +
                (userId != null ? userId : "") + "\u0001" +
                (acctId != null ? acctId : "") + "\u0001" +
                (payitemCode != null ? payitemCode : "") + "\u0001" +
                (acctPriority != null ? acctPriority : "") + "\u0001" +
                (userPriority != null ? userPriority : "") + "\u0001" +
                (addupMethod != null ? addupMethod : "") + "\u0001" +
                (addupMonths != null ? addupMonths : "") + "\u0001" +
                (bindType != null ? bindType : "") + "\u0001" +
                (startAcycId != null ? startAcycId : "") + "\u0001" +
                (endAcycId != null ? endAcycId : "") + "\u0001" +
                (defaultTag != null ? defaultTag : "") + "\u0001" +
                (actTag != null ? actTag : "") + "\u0001" +
                (limitType != null ? limitType : "") + "\u0001" +
                (limitValue != null ? limitValue : "") + "\u0001" +
                (complementTag != null ? complementTag : "") + "\u0001" +
                (provinceCode != null ? provinceCode : "") + "\u0001" +
                (opt != null ? opt : "") + "\u0001" +
                (dataReceiveTime != null ? dataReceiveTime : "") + "\u0001" +
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }
}
