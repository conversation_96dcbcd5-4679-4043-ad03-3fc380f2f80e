package com.unicom.rts.independent.source;

import com.unicom.rts.independent.bean.LocData;
import com.unicom.rts.independent.bean.UserTagStateBean;
import com.unicom.rts.independent.util.FileSystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichParallelSourceFunction;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR> Jiang
 * @create 2022-03-23 15:21
 */
public class UserTagFromHDFSource extends RichParallelSourceFunction<LocData> implements CheckpointedFunction {

    private final static Logger logger = LoggerFactory.getLogger(UserTagFromHDFSource.class);
    private final ParameterTool conf;
    private String provId;
    private String userTagPath;
    private FileSystem fs;
    private boolean isRunning;
    private boolean isRead = false;
    private Long lastTime = 0L;
    ListState<Long> userTagLastUpdateTimeState;

    public UserTagFromHDFSource(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        provId = conf.get("provId");
        userTagPath = conf.get("usertag.path");
        FileSystemUtil fsu = new FileSystemUtil(conf);
        fs = fsu.getFS();
        isRunning = true;
        logger.info("==========成功获取fs=========");
    }

    @Override
    public void run(SourceContext<LocData> sourceContext) throws Exception {
        readUsertagHdfsFile(sourceContext);
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    private void readUsertagHdfsFile(SourceContext<LocData> sourceContext) throws Exception {
        // Thread.sleep(2 * 60 * 1000);
        int parallelism = getRuntimeContext().getNumberOfParallelSubtasks();
        int subTaskId = getRuntimeContext().getIndexOfThisSubtask();
        // logger.info("##############################################");
        while (isRunning) {
            // logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
            FileStatus[] status = new FileStatus[0];
            try {
                status = fs.listStatus(new Path(userTagPath));
            } catch (IOException e) {
                logger.error("fs.listStatus IOException {}", e);
            }
            logger.info("get usertag files");
            if (status.length == 0) {
                logger.error("not find file");
            }
            if (status.length > 1) {
                logger.info("multi files size {}", status.length);
            }
            Long successTime;
            for (FileStatus file : status) {
                if ("SUCCESS".equals(file.getPath().getName())) {
                    //获取success文件更新时间
                    successTime = file.getModificationTime();
                    //success文件更新时间大于当前时间
                    isRead = lastTime < successTime;
                    logger.info("lastTime{} successTime {} isRead {}", lastTime, successTime, isRead);
                }
            }
            if (isRead) {
                lastTime = System.currentTimeMillis();
                for (FileStatus file : status) {
                    int fileHashCode = file.getPath().getName().hashCode() & Integer.MAX_VALUE;
                    if (!"SUCCESS".equals(file.getPath().getName()) && fileHashCode % parallelism == subTaskId) {
                        logger.info("parallelism {} subtask {} read file {}", parallelism, subTaskId, file.getPath().getName());
                        try (BufferedReader br = new BufferedReader(new InputStreamReader(fs.open(file.getPath())))) {
                            String userTagData = "";
                            while ((userTagData = br.readLine()) != null) {
                                parseUserTag(userTagData, sourceContext);
                            }
                        } catch (IOException e) {
                            logger.info("read HDFS file IOException {}", e);
                        }
                    }
                }
            }
            int updateTime = Integer.parseInt(conf.get("update.usertag.file.time"));
            try {
                Thread.sleep(updateTime * 60 * 1000L);
            } catch (InterruptedException e) {
                logger.error("InterruptedException {}", e);
            }
        }
    }

    private void parseUserTag(String userTagStr, SourceContext<LocData> ctx) {
        try {
            String[] line = StringUtils.splitPreserveAllTokens(userTagStr, "\001");
            if (line.length < 33) {
                return;
            }
            if (line[0].length() != 11) {
                return;
            }
            if (!provId.equals(line[5])) {
                return;
            }
            UserTagStateBean userTag = new UserTagStateBean();
            userTag.setK000046(line[5]);
            userTag.setK003681(line[16]);
            userTag.setK002436(line[18]);
            userTag.setK003589(line[23]);
            LocData locData = new LocData();
            locData.setDatasource("0");
            locData.setDeviceNumber(line[0]);
            locData.setUserTagStateBean(userTag);
            ctx.collect(locData);
        } catch (Exception e) {
            logger.error("parseUserTag Exception {}", e);
        }
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        if (!isRunning) {
            logger.info("snapshotState() called on closed UserTagFromHDFSource source");
        } else {
            userTagLastUpdateTimeState.clear();
            userTagLastUpdateTimeState.add(lastTime);
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        ListStateDescriptor<Long> userTagLastUpdateTimeDes = new ListStateDescriptor<>("userTagLastUpdateTime", Types.LONG);
        userTagLastUpdateTimeState = context.getOperatorStateStore().getListState(userTagLastUpdateTimeDes);
        if (context.isRestored()) {
            for (Long restoreTime : userTagLastUpdateTimeState.get()) {
                lastTime = restoreTime > lastTime ? restoreTime : lastTime;
            }
            logger.info(
                    "UserTagFromHDFSource subtask {} restored state: {}.",
                    getRuntimeContext().getIndexOfThisSubtask(),
                    lastTime);
        } else {
            logger.info(
                    "UserTagFromHDFSource subtask {} has no restore state.",
                    getRuntimeContext().getIndexOfThisSubtask());
        }
    }
}
