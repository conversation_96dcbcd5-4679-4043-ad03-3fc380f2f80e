package com.unicom.rts.fun;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.Xz;
import com.unicom.rts.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.apache.directory.api.util.DateUtils;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.kafka.common.header.Header;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collector;

/**
 * <AUTHOR> Jiang
 * @create 10/26/23 10:48 AM
 */
@Slf4j
public class XzProcess extends KeyedProcessFunction<String, Xz, Tuple3<String, String, Iterable<Header>>> {

    ParameterTool conf;
    private String outPutTopic;

    public XzProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        outPutTopic = conf.get("outPut.topic");
    }


    @Override
    public void processElement(Xz xz, Context context, org.apache.flink.util.Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        //todo:调用接口取值
        Map cbssCheckoutBusinp = getCbssCheckoutBusinp(xz.getProvinceCode(), xz.getSerialNumber());
        String outTag = String.valueOf(cbssCheckoutBusinp.get("outTag"));
        String limitRemark = String.valueOf(cbssCheckoutBusinp.get("limitRemark"));
        xz.setOutTag(outTag);
        xz.setLimitRemark(limitRemark);
        System.out.println("下发数据输出topic===>" +"getOutTag===》"+xz.getOutTag()+"limitRemark===》"+xz.getLimitRemark() +xz.toString() + "header" + xz.getHeaders());
        collector.collect(new Tuple3<>(outPutTopic, xz.toString(outPutTopic), xz.getHeaders()));
    }

    public Map getCbssCheckoutBusinp(String provinceCode, String serialNumber) {
        Map<String, String> map = new HashMap<>();

        JSONObject bssMsgJson = new JSONObject();
        String appId = conf.get("APP_ID");
        String appSecret = conf.get("APP_SECRET");
        JSONObject unibssBody = new JSONObject();

        JSONObject unibssHead = new JSONObject();
        Date now = new Date();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        SimpleDateFormat sdfnew = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat sdfnew1 = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdfnew2 = new SimpleDateFormat("yyyyMMdd");

        String formattedDateTime = sdf.format(now);
        String formattedDateTimeNew = sdfnew.format(now);

        unibssHead.put("APP_ID",appId);
        unibssHead.put("TIMESTAMP",formattedDateTime);
        Random random = new Random();
        int randomNumber = random.nextInt(999999);
        String formattedNumber = String.format("%06d", randomNumber);
        String transId = formattedDateTimeNew+formattedNumber;
        unibssHead.put("TRANS_ID",transId);
        String token = "APP_ID"+appId+"TIMESTAMP"+formattedDateTime+"TRANS_ID"+transId+appSecret;
        String tokenMd5 = Md5Util.stringToMD5(token);
        unibssHead.put("TOKEN",tokenMd5);

        JSONObject checkoutBusinpReq = new JSONObject();
        JSONObject reqHead = new JSONObject();
        reqHead.put("SERVICE_NAME", "CustSer");//服务名称
        reqHead.put("OPERATE_NAME", "qryCustInfo");//操作代码
        reqHead.put("ROUTE_EPARCHY_CODE", "1");//路由地市代码
        String provinceCodeLastTwo = provinceCode.substring(provinceCode.length() - 2);
        reqHead.put("PROVINCE_CODE", provinceCodeLastTwo);//省分代码
        reqHead.put("TRADE_CITY_CODE", "1");//区县代码
        reqHead.put("TRADE_EPARCH_CODE", "1");//地市代码
        reqHead.put("TRANS_IDO", "");//交易流水 省份编码+时间序列+随机数字
        reqHead.put("ROUTE_TYPE", "00");//路由类型
        reqHead.put("ROUTE_VALUE", "99");//路由值
        reqHead.put("TRADE_STAFF_ID", "Z000DZQD");//工号
        reqHead.put("TRADE_DEPART_ID", "ZDZQD");//机构代码
        reqHead.put("CHANNEL_TYPE", "9999999");//渠道类型

        String formattedDateTime1 = sdfnew1.format(now);
        String formattedDateTime2 = sdfnew2.format(now);

        reqHead.put("PROCESS_TIME", formattedDateTime1);//时间戳
        reqHead.put("CUT_OFF_DAY", formattedDateTime2);//年月日
        JSONObject reqBody = new JSONObject();
        reqBody.put("SERIAL_NUMBER", serialNumber);//业务号码
        reqBody.put("CRED_TYPE", "02");//证件类型
        reqBody.put("CERD_NUMBER", "1");//证件号码
        JSONObject para = new JSONObject();
        para.put("PARA_ID", "CUST_NAME");//证件名称
        para.put("PARA_VALUE", "1");//证件号码
        reqBody.put("PARA", para);
        checkoutBusinpReq.put("REQ_HEAD", reqHead);
        checkoutBusinpReq.put("REQ_BODY", reqBody);
        unibssBody.put("CHECKOUT_BUSINP_REQ", checkoutBusinpReq);
        bssMsgJson.put("UNI_BSS_BODY", unibssBody);

        bssMsgJson.put("UNI_BSS_HEAD", unibssHead);

        String jsonStr = bssMsgJson.toJSONString();
        String rspJsonStr = "";
        try {
            System.out.println("调接口:" + jsonStr);
            rspJsonStr = doPostJson(conf.get("url"), jsonStr);
            System.out.println("调接口完成返回值:" + rspJsonStr);
        } catch (Exception e) {
            System.out.println("无感知携号转网携出校验入参：" + jsonStr);
            System.out.println("无感知携号转网携出校验报错：" + e);
            map.put("outTag", "9999");
            return map;
        }
        //System.out.println("无感知携号转网携出校验出参内容:" + rspJsonStr);
        JSONObject body = JSONObject.parseObject(rspJsonStr);
        JSONObject uniBssBody = body.getJSONObject("UNI_BSS_BODY");
        JSONObject uniBssHead = body.getJSONObject("UNI_BSS_HEAD");
        if (!uniBssHead.getString("RESP_CODE").equals("00000")) {
            map.put("outTag", "9999");
            return map;
        }
        JSONObject checkoutBusinpRsp = uniBssBody.getJSONObject("CHECKOUT_BUSINP_RSP");
        if (!checkoutBusinpRsp.containsKey("STATUS")) {
            map.put("outTag", "9999");
            return map;
        }
        if (!checkoutBusinpRsp.getString("STATUS").equals("0000")) {
            map.put("outTag", "9999");
            return map;
        }
        JSONObject rsp = checkoutBusinpRsp.getJSONObject("RSP");
        String rspCode = rsp.get("RSP_CODE") + "";
        String rspDesc = rsp.get("RSP_DESC") + "";
        if (rspCode.equals("9999")) {
            map.put("outTag", "9999");
            return map;
        }
        if (rspDesc.equals("查询号码费用信息发生错误，请联系系统管理员")) {
            map.put("outTag", "9999");
            return map;
        }
        String subCode = rsp.get("SUB_CODE") + "";
        String subDesc = rsp.get("SUB_DESC") + "";

        if (subCode.equals("0000")) {
            map.put("outTag", "0");
            map.put("limitRemark", "");
        } else {
            map.put("outTag", "1");
            map.put("limitRemark", subDesc);
        }
        if (subDesc.indexOf("满足携转条件") > -1) {
            map.put("outTag", "0");
            map.put("limitRemark", subDesc);
        }
        return map;
    }


    public String doPostJson(String url, String json) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {

            HttpPost httpPost = new HttpPost(url);

            httpPost.addHeader(new BasicHeader("Content-Type", "application/json;charset=UTF-8"));
            httpPost.addHeader(new BasicHeader("Accept","application/json"));
            httpPost.addHeader(new BasicHeader("Accept-Encoding",""));

            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            System.out.println("调接口error:" + String.valueOf(e));
            log.error(String.valueOf(e));
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                System.out.println("调接口error:" + String.valueOf(e));
                log.error(String.valueOf(e));
            }
        }
        System.out.println("调接口success:" + resultString);
        return resultString;
    }



}