package com.unicom.rts.enums;

public enum BusEnum {
    /**
     * 注释
     */
    TRADE_ID,
    /**
     * 注释
     */
    SUBSCRIBE_ID,
    /**
     * 注释
     */
    BPM_ID,
    /**
     * 注释
     */
    TRADE_TYPE_CODE,
    /**
     * 注释
     */
    IN_MODE_CODE,
    /**
     * 注释
     */
    SUBSCRIBE_STATE,
    /**
     * 注释
     */
    PRODUCT_ID,
    /**
     * 注释
     */
    BRAND_CODE,
    /**
     * 注释
     */
    USER_ID,
    /**
     * 注释
     */
    CUST_ID,
    /**
     * 注释
     */
    USECUST_ID,
    /**
     * 注释
     */
    ACCT_ID,
    /**
     * 注释
     */
    USER_DIFF_CODE,
    /**
     * 注释
     */
    NET_TYPE_CODE,
    /**
     * 注释
     */
    DEVICE_NUMBER,
    /**
     * 注释
     */
    CUST_NAME,
    /**
     * 注释
     */
    ACCEPT_DATE,
    /**
     * 注释
     */
    OPERATION_TIME,
    /**
     * 注释
     */
    TRADE_STAFF_ID,
    /**
     * 注释
     */
    TRADE_DEPART_ID,
    /**
     * 注释
     */
    TRADE_CITY_CODE,
    /**
     * 注释
     */
    TRADE_EPARCHY_CODE,
    /**
     * 注释
     */
    EPARCHY_CODE,
    /**
     * 注释
     */
    CITY_CODE,
    /**
     * 注释
     */
    EXEC_TIME,
    /**
     * 注释
     */
    FINISH_DATE,
    /**
     * 注释
     */
    OPER_FEE,
    /**
     * 注释
     */
    FOREGIFT,
    /**
     * 注释
     */
    ADVANCE_PAY,
    /**
     * 注释
     */
    FEE_STATE,
    /**
     * 注释
     */
    FEE_TIME,
    /**
     * 注释
     */
    FEE_STAFF_ID,
    /**
     * 注释
     */
    CHECK_TYPE_CODE,
    /**
     * 注释
     */
    IF_MAINTENANCE,
    /**
     * 注释
     */
    ORDER_ID,
    /**
     * 注释
     */
    SUB_ORDER_ID,
    /**
     * 注释
     */
    MAIN_DISCNT_CODE,
    /**
     * 注释
     */
    PRODUCT_SPEC,
    /**
     * 注释
     */
    STANDARD_KIND_CODE,
    /**
     * 注释
     */
    PROV_ID,
    /**
     * 注释
     */
    INTIME,
    /**
     * 注释
     */
    DATA_SOURCE,
    /**
     * 注释
     */
    CDH_TIME,
    /**
     * 注释
     */
    NEXT_DEAL_TAG;
}
