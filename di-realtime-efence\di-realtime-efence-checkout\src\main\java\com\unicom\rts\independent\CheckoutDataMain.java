package com.unicom.rts.independent;

import com.unicom.rts.independent.bean.CheckoutDataBean;
import com.unicom.rts.independent.bean.MidDataBean;
import com.unicom.rts.independent.bean.RuleBean;
import com.unicom.rts.independent.beans.RecordBean;
import com.unicom.rts.independent.function.CheckOutBakProcess;
import com.unicom.rts.independent.function.EfenceComputeProcess;
import com.unicom.rts.independent.function.RecordBeanMapFunction;
import com.unicom.rts.independent.function.WhiteListFunction;
import com.unicom.rts.independent.sink.KafkaProducer;
import com.unicom.rts.independent.source.RuleSource;
import com.unicom.rts.independent.source.WhiteListSource;
import com.unicom.rts.independent.utils.DataStreamToHdfsUtil;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.unicom.rts.independent.source.KafkaSource.getKafkaConsumerForTopicList;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/11
 **/
public class CheckoutDataMain {
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        //ParameterTool conf = ParameterTool.fromPropertiesFile("D:\\shuke\\code\\xinan\\context_xinan_efence\\efence_checkout\\conf\\config_test.properties");

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"),
                true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);  //设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        //checkpoint
        // 开启Checkpoint，间隔为 3 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(3));

        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        // 最小间隔 4分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(4));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);


        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10,
                Time.of(10, TimeUnit.SECONDS)));

        //获取白名单广播流，并广播出去
        final MapStateDescriptor<Void, List<String>> whiteListConfig = new MapStateDescriptor<>(
                "whiteList",
                Types.VOID,
                Types.LIST(Types.STRING));

        BroadcastStream<List<String>> whiteListStream = env.addSource(new WhiteListSource(conf)).name("whiteList")
                .broadcast(whiteListConfig);

        //获取过滤规则广播流，并广播出去
        final MapStateDescriptor<Void, List<RuleBean>> ruleConfig = new MapStateDescriptor<>(
                "ruleList",
                Types.VOID,
                Types.LIST(Types.POJO(RuleBean.class)));

        BroadcastStream<Map<String, RuleBean>> ruleStream = env.addSource(new RuleSource(conf)).name("ruleList")
                .broadcast(ruleConfig);

        //创建检出的侧输出流
        OutputTag<MidDataBean> midBak = new OutputTag<>("checkoutBak", TypeInformation.of(MidDataBean.class));
        OutputTag<CheckoutDataBean> checkoutBak = new OutputTag<>("checkoutBak", TypeInformation.of(CheckoutDataBean.class));

        DataStream<ConsumerRecord<String, String>> inputStream =
                env.addSource(getKafkaConsumerForTopicList(conf))
                .setParallelism(Integer.parseInt(conf.get("source.parallelism")));

        SingleOutputStreamOperator<RecordBean> dataStreamPhase = inputStream
                .process(new RecordBeanMapFunction(midBak)).uid("efenceFlatMapFunc")
                .name("efenceFlatMapFunc").setParallelism(Integer.parseInt(conf.get("flatmap.parallelism")));

        //检出程序
        DataStream<Tuple2<String, String>> resultStream = dataStreamPhase
                .connect(whiteListStream)
                .process(new WhiteListFunction()).uid("efenceWhiteListFunc")
                .name("efenceWhiteListFunc").setParallelism(Integer.parseInt(conf.get("whitelist.parallelism")))
                .keyBy(v -> v.getDeviceNum())
                .connect(ruleStream)
                .process(new EfenceComputeProcess(conf)).uid("efenceComputeFunc")
                .name("efenceComputeFunc").setParallelism(Integer.parseInt(conf.get("compute.parallelism")));

        SingleOutputStreamOperator<CheckoutDataBean> checkoutBakStream = resultStream
                .process(new CheckOutBakProcess(checkoutBak)).uid("checkoutBak").name("checkOutBak")
                .setParallelism(Integer.parseInt(conf.get("checkoutbak.parallelism")));

        //src侧输出流
        DataStreamToHdfsUtil.midDataStreamToHdfs(dataStreamPhase.getSideOutput(midBak), conf);
        DataStreamToHdfsUtil.checkoutStreamToHdfs(checkoutBakStream.getSideOutput(checkoutBak), conf);;

        //添加sink
        FlinkKafkaProducer<Tuple2<String, String>> producer = KafkaProducer.getFlinkKafkaProducer(conf);
        resultStream.addSink(producer).name("kafka sink");

        env.execute("context_efence_checkout");
    }
}
