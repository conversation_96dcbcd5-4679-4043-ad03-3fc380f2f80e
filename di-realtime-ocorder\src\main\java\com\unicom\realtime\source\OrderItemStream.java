package com.unicom.realtime.source;

import com.unicom.realtime.bean.OrderTag;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class OrderItemStream {
    public DataStream<OrderTag> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(OC_ORDER_ITEM + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, OrderTag>) (row, collector) -> {
            OrderTag orderTag = new OrderTag();
            orderTag.setOrderId((String) row.getField("order_id"));
            orderTag.setAttrCode((String) row.getField("attr_code"));
            orderTag.setAttrValue((String) row.getField("attr_value"));
            orderTag.setOpt((String) row.getField("opt"));
            orderTag.setDatasource("OC_ORDER_ITEM");
            collector.collect(orderTag);
        }).returns(OrderTag.class).uid("Oc_Order_Item_TagFlatMap").name("Oc_Order_Item_TagFlatMap")
                .setParallelism(conf.getInt("Oc_Order_Item_TagFlatMap.parallelism", 8));
    }

    private static final String OC_ORDER_ITEM = "SELECT\n" +
            "    order_id order_id,\n" +
            "    attr_code attr_code,\n" +
            "    attr_value attr_value,\n" +
            "    opt opt \n" +
            "    FROM dwd_r_paimon_oc_order_item";
}
