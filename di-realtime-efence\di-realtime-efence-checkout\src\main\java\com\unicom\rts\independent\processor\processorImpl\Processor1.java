package com.unicom.rts.independent.processor.processorImpl;

import com.unicom.rts.independent.enums.TagEnum;
import com.unicom.rts.independent.processor.AbstractProcessor;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.flink.api.java.tuple.Tuple2;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.unicom.rts.independent.utils.FilterUtil.filterByGroovy;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/12
 **/

/**
 * 过滤条件，groovy（groovy中仅过滤）
 * 输出 record + "\1" + productName
 * 北京漫游、河南漫游、贵州imei、广西imei、重庆漫游、云南imei、广西漫游、贵州漫游、福建漫游
 */
public class Processor1 extends AbstractProcessor {
    @Override
    public void subProcess() throws Exception {

        if (null != recordBean && null != ruleBean.getParam() &&
                filterByGroovy(recordBean, ruleBean.getParam()).equals(true)) {
            String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                    recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTNAME.getCode());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            checkoutState.put(recordBean.getDeviceNum(), sdf.format(new Date()));
            out.collect(new Tuple2<>(ruleBean.getSinkTopic(), recordBean.getRecord() + "\1" + productName));
        }
    }
}
