package com.unicom.realtime;

import com.unicom.realtime.entity.ZjData;
import com.unicom.realtime.function.ZjFlatMap;
import com.unicom.realtime.sink.HBaseSink;
import com.unicom.realtime.source.KafkaSource;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2024年8月13日 19:34:49
 */
public class MainJob {

    private final static Logger logger = LoggerFactory.getLogger(MainJob.class);

    public static void main(String[] args) throws Exception {

        ParameterTool conf = ParameterTool.fromArgs(args);
        String topicSource = conf.get("zj.source.topic");
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.disableOperatorChaining();
        env.getConfig().setGlobalJobParameters(conf);
        //状态后端
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        //env.disableOperatorChaining();
        env.enableChangelogStateBackend(conf.getBoolean("checkpoint.changelog.enable", false));
        env.getCheckpointConfig().setCheckpointStorage(conf.get("checkpoint.checkpointDataUrl"));
        // 开启Checkpoint，间隔为 1 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(conf.getInt("checkpoint.enableCheckpointing")));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
//        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(6));
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(conf.getInt("checkpoint.minPauseBetweenCheckpoints")));
        // 超时时间 10 分钟
//        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(conf.getInt("checkpoint.checkpointTimeout")));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        FlinkKafkaConsumerBase<ConsumerRecord<String, String>> source = KafkaSource.getRootConsumer(conf, topicSource);

        SingleOutputStreamOperator<ZjData> zjDataSingleOutputStreamOperator = env.addSource(source).uid("ZjSource").name("ZjSource").setParallelism(conf.getInt("zj.source.paralleism", 20))
                //.rebalance()
                .flatMap(new ZjFlatMap()).uid("ZjFlatMap").name("ZjFlatMap").setParallelism(conf.getInt("zjFlatMap.paralleism", 20));
        //.keyBy(ZjData::getSerialNumber);

        logger.info("zjmsg addSink start. ");
        zjDataSingleOutputStreamOperator.addSink(new HBaseSink(conf)).uid("ZjSink").name("ZjSink").setParallelism(conf.getInt("zjSink.paralleism", 20));

        env.execute(conf.get("job.name"));
    }
}
