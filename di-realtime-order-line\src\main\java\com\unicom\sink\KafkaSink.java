package com.unicom.sink;

import com.unicom.realtime.bean.OrderUnion;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class KafkaSink extends RichSinkFunction<OrderUnion> {

    private KafkaProducer<String, String> kafkaProducer;
    private Map<String, String> confMap = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        Properties producerProp = new Properties();
        ExecutionConfig.GlobalJobParameters globalJobParameters = getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        confMap = globalJobParameters.toMap();
        producerProp.setProperty("bootstrap.servers", confMap.get("sink.kafka.bootstrap"));
        producerProp.put("enable.auto.commit", "true");
        producerProp.put("auto.commit.interval.ms", "100");
        producerProp.put("session.timeout.ms", "30000");

        producerProp.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\""
                + "sjzt-sssc-outkafka1" + "\" password=\"" + "sjzt-Sssc#@240910" + "\";");
        producerProp.setProperty("security.protocol", "SASL_SSL");
        producerProp.setProperty("sasl.mechanism", "SCRAM-SHA-256");
        producerProp.put("key.serializer", StringSerializer.class.getName());
        producerProp.put("value.serializer", StringSerializer.class.getName());
        producerProp.setProperty("ssl.truststore.location", "/usr/share/sscj/client.truststore.jks");
        producerProp.setProperty("ssl.truststore.password", "Sunsd10#");
        producerProp.setProperty("ssl.endpoint.identification.algorithm", "");
        kafkaProducer = new KafkaProducer<>(producerProp);
    }

    @Override
    public void invoke(OrderUnion value, Context context) {
        String content = "";
        if(value.getDatasource().toUpperCase().equals("OC_ORDER_PRESS")) {
            content = value.getOrderPress().toString();
        } else if(value.getDatasource().toUpperCase().equals("OC_ORDER_PAYRELATION")) {
            content = value.getOrderPayRelation().toString();
        } else if(value.getDatasource().toUpperCase().equals("OC_ORDER_RELATION_UU")) {
            content = value.getOrderRelationUU().toString();
        } else if(value.getDatasource().toUpperCase().equals("OC_ORDER_DEVELOP")) {
            content = value.getOrderDevelop().toString();
        }
        ProducerRecord record = new ProducerRecord<>(confMap.getOrDefault("sink.kafka.topic", "ZT_STRATEGY_9900_DTS01"), content);
        kafkaProducer.send(record);
    }

    @Override
    public void close() throws Exception {
        super.close();
        kafkaProducer.close();
    }
}
