package com.unicom.realtime.function;


import com.unicom.realtime.bean.OrderUnion;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

public class OrderPressComputeProcess extends KeyedProcessFunction<String, OrderUnion, OrderUnion> {
    private MapState<String, OrderUnion> orderLineState;
    private MapState<String, OrderUnion> orderPressState;

    @Override
    public void open(Configuration parameters) {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(7))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        MapStateDescriptor<String, OrderUnion> orderLineStateDescriptor = new MapStateDescriptor<>("orderLineMapState", Types.STRING, Types.POJO(OrderUnion.class));
        MapStateDescriptor<String, OrderUnion> orderPressStateDescriptor = new MapStateDescriptor<>("orderPressMapState", Types.STRING, Types.POJO(OrderUnion.class));
        orderLineStateDescriptor.enableTimeToLive(ttlConfig);
        orderPressStateDescriptor.enableTimeToLive(ttlConfig);
        orderLineState = getRuntimeContext().getMapState(orderLineStateDescriptor);
        orderPressState = getRuntimeContext().getMapState(orderPressStateDescriptor);
    }

    @Override
    public void processElement(OrderUnion orderUnion, Context context, Collector<OrderUnion> collector) throws Exception {
        if (orderUnion.getDatasource().toUpperCase().startsWith("OC_ORDER_LINE")) {
            orderLineState.put(orderUnion.getOrderId(), orderUnion);
            OrderUnion orderPress = orderPressState.get(orderUnion.getOrderId());
            if(orderPress != null) {
                collector.collect(orderPress);
            }
        } else if (orderUnion.getDatasource().toUpperCase().startsWith("OC_ORDER_PRESS")) {
            orderPressState.put(orderUnion.getOrderId(), orderUnion);
            OrderUnion orderLine = orderLineState.get(orderUnion.getOrderId());
            if(orderLine != null) {
                collector.collect(orderUnion);
            }
        }
    }
}