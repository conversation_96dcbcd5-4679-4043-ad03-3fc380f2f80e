package com.unicom.rts.function;

import com.alibaba.fastjson.JSONObject;
import com.unicom.rts.bean.Rsp;
import com.unicom.rts.pojo.SetXhzwZCEntry;
import com.unicom.rts.pojo.XhzwZCEntry;
import com.unicom.rts.util.GetFineshData;
import com.unicom.rts.util.HbaseUtil;
import com.unicom.rts.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class DataProcessFunction extends ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, String>> {

    private final static Logger logger = LoggerFactory.getLogger(InitConfiguration.class);

    private Connection connection = null;
    private String terminated = null;

    private String hbaseUser = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        ExecutionConfig.GlobalJobParameters globalJobParameters = getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        Map<String, String> confMap = globalJobParameters.toMap();

        this.terminated = confMap.get("terminated");

        if (connection == null || connection.isClosed()) {
            org.apache.hadoop.conf.Configuration hbaseConfig = HBaseConfiguration.create();

            hbaseConfig.set("hbase.zookeeper.quorum", confMap.get("hbase.zookeeper"));
            hbaseConfig.set("hbase.zookeeper.property.clientPort", confMap.get("hbase.zookeeper.port"));// zookeeper端口
            hbaseConfig.set("zookeeper.znode.parent", "/hbase");
            hbaseConfig.set(HConstants.HBASE_RPC_READ_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_RPC_WRITE_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_OPERATION_TIMEOUT, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_SCANNER_TIMEOUT_PERIOD, "1800000");
            hbaseUser = confMap.get("hbase.user");
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(hbaseUser);
            connection = ConnectionFactory.createConnection(hbaseConfig, User.create(userGroupInformation));
        }
    }

    @Override
    public void processElement(ConsumerRecord<Object, Object> cr, ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, String>>.Context ctx, Collector<Tuple2<Rsp, String>> out) throws Exception {
        String value = new String((byte[]) cr.value(), "GBK");
        //String value = " 010187658911606266001000002345100035451000234510125561151990001606266000000160626600100009745102021-04-07 11:03:07.444A24Delete";
        String[] splitsValueArr = value.split(terminated, -1);
        if ("Insert".equals(splitsValueArr[15])) {
            logger.info("---------------------------Insert"+value);
            logger.info("---------------------------Insert"+cr.headers());
            String integrationTime = "";
            for (Header header : cr.headers()) {
                logger.info("---------------------------for----");
                if("integration_time".equalsIgnoreCase(header.key())){
                    logger.info("---------------------------if----");
                    integrationTime = new String(header.value());
                    logger.info("---------------------------integrationTime--"+integrationTime);
                }
            }
            //hbase字段：user_id,trade_staff_id,finish_date,chnl_code,level_a_code,chnl_name
            //String[] column = new String[]{"user_id", "trade_staff_id", "finish_date", "chnl_code", "level_a_code", "chnl_name"};
            String[] column = new String[]{"q9","q18","q25","q43","q44","q45"};
            //从hbase表中获取该记录中手机号的信息

            // dwd_d_mrt_e_chnl_area_info_20210328_htab
            Result results = HbaseUtil.getKeyValue(connection, hbaseUser+":dwd_d_mrt_e_chnl_area_info_20210328_htab", splitsValueArr[1], "f", column);

            //ugiHybase.printRecord(results.rawCells());
            //如果hbase中有该号码的记录，则组装数据进行下发，否则直接跳过
            if (results != null && !results.isEmpty()) {
                logger.info("---------------xhzw_yczw_pro_all-----------------start");
                Cell[] cells = results.rawCells();
                //全量携号转网数据下发至 xhzw_yczw_pro_all
                XhzwZCEntry xhzwZCEntryAll = new XhzwZCEntry();
                SetXhzwZCEntry.setXhzwEntry_new(xhzwZCEntryAll, splitsValueArr, cells);//组装结果数据实体
                logger.info(xhzwZCEntryAll.getShoujihao());
                String producerValueAll = SetXhzwZCEntry.pjProducerValue(xhzwZCEntryAll);//通过\u0001将数据实体连接成最终结果数据
                //发送组装好的数据到目标topic

                Rsp rsp = new Rsp();
                rsp.setKeyBy(splitsValueArr[1]);
                rsp.setIntegrationTime(integrationTime);
                rsp.setReceiveTime(String.valueOf(cr.timestamp()));
                rsp.setDestTopic("xhzw_yczw_pro_all");
                out.collect(new Tuple2<>(rsp,producerValueAll));
                logger.info("producerValueAll-------------------"+producerValueAll);
                logger.info("---------------xhzw_yczw_pro_all------------------end");
            }
            //当天入网且当天携出的数据
            if (results != null &&!results.isEmpty()) {
                logger.info("---------------xhzw_yczw_pro_01-----------------start");
                Cell[] cells = results.rawCells();
                //从hbase表获取finsh_Date（88888888的finishdate）
                String finsh_Date = GetFineshData.finshDateFromHtable(cells);
                //判断申请时间和finsh_Date均不为空
                if (!"".equals(splitsValueArr[2]) && splitsValueArr[2] != null && !"".equals(finsh_Date)) {
                    //如果申请时间和finsh_Date在同一天
                    if (TimeUtil.timeday(splitsValueArr[2]).equals(TimeUtil.timeday(finsh_Date))) {

                        XhzwZCEntry xhzwZCEntry = new XhzwZCEntry();
                        SetXhzwZCEntry.setXhzwEntry(xhzwZCEntry, splitsValueArr, cells);//组装结果数据实体
                        logger.info(xhzwZCEntry.getShoujihao());
                        String producerValue = SetXhzwZCEntry.pjProducerValue(xhzwZCEntry);//通过\u0001将数据连接成最终结果数据
                        //发送符合条件的数据到目标topic
                        Rsp rsp = new Rsp();
                        rsp.setKeyBy(splitsValueArr[1]);
                        rsp.setDestTopic("xhzw_yczw_pro_01");
                        rsp.setIntegrationTime(integrationTime);
                        rsp.setReceiveTime(String.valueOf(cr.timestamp()));
                        out.collect(new Tuple2<>(rsp,producerValue));
                        logger.info("producerValue-------------------"+producerValue);
                    }
                }
                logger.info("---------------xhzw_yczw_pro_01-----------------end");
            }
        }
    }

}
