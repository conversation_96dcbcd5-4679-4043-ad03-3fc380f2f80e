package com.unicom.rts;

import com.unicom.rts.common.KafkaStream;
import com.unicom.rts.entity.PaimonBean;
import com.unicom.rts.entity.ThrData;
import com.unicom.rts.function.RowToPaimonBean;
import com.unicom.rts.function.SourcePaimonProcessCo;
import com.unicom.rts.function.ThrClean;
import com.unicom.rts.sink.KafkaProducer;
import com.unicom.rts.source.Sql;
import com.unicom.rts.util.PaimonUtil;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.kafka.common.header.Header;

import java.util.concurrent.TimeUnit;

;

public class MainJob{
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String thrTopic = conf.get("thr.topic");
        ParameterTool thrKafkaConf = KafkaStream.mergeConf(conf, "thr.");
        // 创建流式的执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);// 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        // checkpoint
        // 开启Checkpoint，间隔为 3 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
//        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(6));
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(conf.getInt("minPauseBetweenCheckpoints")));
        // 超时时间 10 分钟
//        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(conf.getInt("checkpointTimeout")));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(5, Time.of(10, TimeUnit.SECONDS)));


        // 消费vcf kafka数据
        DataStream<ThrData> sourceStream = env.addSource(KafkaStream.getRootConsumer(thrKafkaConf, thrTopic)).uid("getRootConsumer").name("getRootConsumer")
                .flatMap(new ThrClean()).uid("ThrClean").name("ThrClean");

        /*************common*****************************/
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        tabEnv.executeSql(Sql.CREATE_DATA_INDEPENDENT);
        Table paymentDataTable= tabEnv.sqlQuery(Sql.SELECT_DATA_INDEPENDENT + options);
        DataStream<PaimonBean> paymentPaimonStream = tabEnv.toChangelogStream(paymentDataTable).flatMap(new RowToPaimonBean()).uid("RowToPaimonBean").name("RowToPaimonBean").filter(
            new FilterFunction<PaimonBean>() {
                @Override
                public boolean filter(PaimonBean data) throws Exception {
                    //0新增，1过滤
                    if ("0".equals(data.getIsDelete())) {
                        return true;
                    }else {
                        return false;
                        }
                    }
                }
        ).keyBy(PaimonBean::getPhoneNum);
        /*************common*****************************/

        DataStream<ThrData> userTagDataStream = new PaimonUtil().source(env, conf);

//        DataStream<ThrData> userTagDataStream = new HudiUtil(conf).getBuilder().source(env)
//                .flatMap(new UserTagFlatMap()).uid("UserTagFlatMap").name("UserTagFlatMap")
//                .setParallelism(conf.getInt("UserTagFlatMap.parallelism", 20));

        DataStream<Tuple3<String, String, Iterable<Header>>> outStream = sourceStream.union(userTagDataStream).keyBy(
                new KeySelector<ThrData, String>() {
                    @Override
                    public String getKey(ThrData value) throws Exception {
                        return value.getDeviceNumber();
                    }
                }
        ).connect(paymentPaimonStream).process(new SourcePaimonProcessCo(conf)).uid("SourcePaimonProcessCo").name("SourcePaimonProcessCo");

        FlinkKafkaProducer<Tuple3<String , String, Iterable<Header>>> flinkKafkaProducer = KafkaProducer.getFlinkKafkaProducer(conf);
        // sink
        outStream.addSink(flinkKafkaProducer).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("kafkaSink");;
        // execute program
        env.execute("dp-independent-thr");
    }
}
