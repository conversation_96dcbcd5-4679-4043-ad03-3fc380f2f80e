package com.unicom.rts.independent;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.unicom.rts.independent.source.KafkaSource;
import com.unicom.rts.independent.source.RdsSource;
import com.unicom.rts.independent.util.Util;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.io.IOException;
import java.sql.Connection;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutionException;

public class Main {

    public static void main(String[] args)  {

        String serverAddr = "10.177.141.164:8848";
        String namespace = "realtime-scene-web-local";
        if( args.length > 0 ){
            if("prod".equals(args[0])){
                serverAddr = "10.177.141.163:8848";
                namespace = "realtime-scene-web-prod";
            }
        }
        String dataId = "di-realtime-kafka-util";
        String group = "DEFAULT_GROUP";
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.SERVER_ADDR, serverAddr);
        properties.put(PropertyKeyConst.USERNAME,"service-rule-account");
        properties.put(PropertyKeyConst.PASSWORD,"Q5V87k3njTkfp");
        properties.put(PropertyKeyConst.NAMESPACE,namespace);
        ConfigService configService = null;
        try {
            configService = NacosFactory.createConfigService(properties);
        } catch (NacosException e) {
            System.out.println("NacosFactory createConfigService error= " + e.toString());
        }
        String content = null;
        try {
            content = configService.getConfig(dataId, group, 5000);
            System.out.println("nacos content = " +content);
        } catch (NacosException e) {
            System.out.println("NacosException = " + e.getErrMsg());
        }
        Properties props = new Properties();
        try {
            props.load(new java.io.StringReader(content));
        } catch (IOException e) {
            System.out.println("StringReader  error= " + e.toString());
        }
        System.out.println("开始执行.......");
        long start = System.currentTimeMillis();
        // 获取rds信息
        Connection connection = RdsSource.getConnection(props);
        Map<String, Map<String,Object>> rule = null;
        try {
            rule = RdsSource.getRdsData(connection,RdsSource.RULESQL);
        } catch (Exception e) {
            System.out.println("获取规则失败 : " + e.getMessage());
        }
        Map<String, Map<String,Object>> conn = null;
        try {
            conn = RdsSource.getRdsData(connection,RdsSource.CONNSQL);
        } catch (Exception e) {
            System.out.println("获取连接信息失败 : " + e.getMessage());
        }

        Map<String,Map<String,Object>> aloneTopic = null;
        try {
            aloneTopic = RdsSource.getRdsData(connection,RdsSource.ALONETOPICSQL);
        }
        catch (Exception e) {
            System.out.println("获取独立任务TOPIC信息 : " + e.getMessage());
        }

        System.out.println("获取RDS信息成功......");
        // 释放rds连接
        RdsSource.release(connection);

        Producer<String,String> producer = null;
        // 加个开关
        String sendTopic = props.getProperty("send.kfk.topic", "send_data_servive_topic" );
        boolean isSendOut = Boolean.parseBoolean(props.getProperty("send.kfk.flag", "false" ));
        String  sendAddressId = props.getProperty("send.kfk.address", "");
        if(isSendOut){
            producer = KafkaSource.getProducer(props);
        }else {
            producer = KafkaSource.getProducer(conn.get(sendAddressId),props);
        }
        for(String key:conn.keySet()){
            Map<String,Object> value = conn.get(key);
            AdminClient client = KafkaSource.getAdminClient(value,props);
            System.out.println("获取AdminClient成功.......");
            // modify by wangjh : 这里就从配置里获取了， 不要从表里查了。不管有多少下发集群都要下发到对端kafka里
            /*
            String sendTopic = (String) value.get("SEND_TOPIC");
            if(sendTopic == null){
                sendTopic = "send_data_servive_topic"; //主题名称
                int partitions = 1; //分区数
                short rep = 3; //副本数
                NewTopic newTopic = new NewTopic((String) sendTopic,partitions,rep);
                try {
                    client.createTopics(Collections.singletonList(newTopic)).all().get();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                }
                System.out.println("create topic : [" + sendTopic + "] , success !");
            }*/
            Set<String> topics = KafkaSource.getTopics(client);
                for (String topic:topics){
                boolean flag = false;
                long offset = 0L;
                try{
                    if(aloneTopic.containsKey(topic)){
                        Map<String,Long> cgMap  = KafkaSource.getCgOffset(client,topic);
                        for(String cg:cgMap.keySet()){
                            offset = cgMap.get(cg);
                            JSONObject jsonObject = Util.getJsonDataAlone(offset,topic,aloneTopic,cg);
                            producer.send(new ProducerRecord<>(sendTopic,jsonObject.toJSONString()));
                        }
                    }else{
                        offset = KafkaSource.getOffset(client,topic);
                        // 封装结果
                        JSONObject jsonObject = Util.getJsonData(offset,topic,rule);
                        // Sink kafka
                        producer.send(new ProducerRecord<String,String>(sendTopic,jsonObject.toJSONString()));
                    }
                }catch (Exception e){
                    flag = true ;
                    System.out.println("集群:" + key + "获取topic:"+ topic + " 的offset异常" + e);
                    if (flag){
                        continue;
                    }
                }
            }
            KafkaSource.release(client,null);
        }
        KafkaSource.release(null,producer);
        long end = System.currentTimeMillis();
        System.out.println("整体耗时 : " + (end-start));
        System.out.println("执行完成.......");
    }
}