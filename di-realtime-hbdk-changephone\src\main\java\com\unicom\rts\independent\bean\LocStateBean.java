package com.unicom.rts.independent.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocStateBean {
    // String deviceNumber;
    String inMonth;
    Long time;
    String imei;
    String lac;
    String ci;
    Double lon;
    Double lat;
    String eparchyCode;

    public LocStateBean(LocData loc, Double lon, Double lat) {
        this.inMonth = loc.getInTime();
        this.time = loc.getTime();
        this.imei = loc.getImei();
        this.lac = loc.getLac();
        this.ci = loc.getCi();
        this.eparchyCode = loc.getUserTagStateBean().getEparchyCode();
        this.lon = lon;
        this.lat = lat;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullBooleanAsFalse,
                SerializerFeature.UseISO8601DateFormat);
    }
}

