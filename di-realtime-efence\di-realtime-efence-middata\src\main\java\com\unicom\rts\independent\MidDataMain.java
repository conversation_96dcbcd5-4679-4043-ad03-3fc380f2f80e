package com.unicom.rts.independent;

import com.unicom.rts.independent.beans.MidDataBean;
import com.unicom.rts.independent.function.LocTradeFlatMapFunction;
import com.unicom.rts.independent.function.RelateDimFlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.state.hashmap.HashMapStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.concurrent.TimeUnit;

import static com.unicom.rts.independent.sink.KafkaProducer.getKafkaProducer;
import static com.unicom.rts.independent.source.KafkaSource.getKafkaConsumer;


public class MidDataMain {

    public static void main(String[] args) throws Exception {
        ParameterTool parameters = ParameterTool.fromArgs(args);
        ParameterTool appConf = ParameterTool.fromPropertiesFile(parameters.get("app_config_path"));
        String checkpointPath = appConf.get("checkpoint.path");
        long checkPointInterval = Long.parseLong(appConf.get("checkpoint.interval"));


        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        //设置状态后端为hashmap状态后端
        env.setStateBackend(new HashMapStateBackend());
        env.getCheckpointConfig().setCheckpointStorage(checkpointPath);
        //设置检查点时间间隔和模式
        env.enableCheckpointing(checkPointInterval, CheckpointingMode.EXACTLY_ONCE);
        //同一时间只允许进行一个检查点
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        //任务取消后保存最近一次检查点
        env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 最小间隔 5分钟
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(5));
        // 超时时间 10分钟
        env.getCheckpointConfig().setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));

        //分发省份地市码表
        env.registerCachedFile(appConf.get("path.dim.provarea"), "dim_provarea");
        //失败后尝试重启5次，每次间隔3分钟
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(5, Time.minutes(3)));

        DataStream<MidDataBean> dataStream = env.addSource(getKafkaConsumer(appConf)).setParallelism(appConf.getInt("parallelism.source")).name("消费原始信令数据")
            .flatMap(new LocTradeFlatMapFunction()).setParallelism(appConf.getInt("parallelism.loctrademap")).name("原始数据转换对象")
            .flatMap(new RelateDimFlatMapFunction()).setParallelism(appConf.getInt("parallelism.filter")).name("关联维表，过滤数据");

        dataStream.addSink(getKafkaProducer(appConf)).setParallelism(appConf.getInt("parallelism.sink")).name("中间数据写入kafka");

        env.execute("xinanMidData");


    }
}
