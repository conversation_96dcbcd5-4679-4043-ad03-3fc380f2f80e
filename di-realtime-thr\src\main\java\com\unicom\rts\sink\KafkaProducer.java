package com.unicom.rts.sink;

import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.common.header.Header;

import java.util.Properties;

public class KafkaProducer {

    public static FlinkKafkaProducer<Tuple3<String, String, Iterable<Header>>> getFlinkKafkaProducer(ParameterTool conf) {
        Properties producerProp = new Properties();
        producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
        producerProp.put("acks", "0");//Must set acks to all in order to use the idempotent producer
        producerProp.put("retries", "3");
        producerProp.put("batch.size", conf.get("batch.size","16384"));
        producerProp.put("linger.ms", "50");
        producerProp.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        producerProp.put("request.timeout.ms", conf.get("request.timeout.ms","120000"));

        boolean sinkSecurity = conf.getBoolean("sink.kafka.security");

        //下发kafka开启认证
        if (sinkSecurity) {
            String mechanism = conf.get("sasl.mechanism");
            String protocol = conf.get("security.protocol");
            String jaasConfig = conf.get("sasl.jaas.config");
//            producerProp.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
            producerProp.setProperty("sasl.jaas.config", jaasConfig + " required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
            producerProp.setProperty("security.protocol", protocol);
            producerProp.setProperty("sasl.mechanism", mechanism);
        }

        return new FlinkKafkaProducer<>(
                "all",      // target topic
                new KafkaMultiSinkSerializationSchema(),    // serialization schema
                producerProp,                // producer config
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE, 5);
    }
}