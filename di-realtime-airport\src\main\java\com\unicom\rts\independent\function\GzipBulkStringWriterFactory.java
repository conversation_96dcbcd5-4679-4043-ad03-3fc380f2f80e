package com.unicom.rts.independent.function;

import org.apache.flink.api.common.serialization.BulkWriter;
import org.apache.flink.core.fs.FSDataOutputStream;

import java.io.IOException;
import java.util.zip.GZIPOutputStream;

public class GzipBulkStringWriterFactory implements BulkWriter.Factory<String> {
	@Override
	public BulkWriter<String> create(FSDataOutputStream out) throws IOException {
		GZIPOutputStream gzipOutputStream = new GZIPOutputStream(out,true);
		return new GzipStringBulkWriter(gzipOutputStream);
	}
}