package com.unicom.rts.independent.utils;

import com.unicom.rts.independent.beans.RecordBean;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.unicom.rts.independent.utils.MapUtil.recordToMap;

@Slf4j
public class FilterUtil {

    //参数过滤
    public static Object filterByGroovy(RecordBean recordBean, String groovyScript) {
        Map<String,Object> param = recordToMap(recordBean);
        Object result = GroovyUtil.runGroovyScript(groovyScript,param);
//        System.out.println("判断结果为" + result);
        return result != null ? result : false;
    }

    public static Object getTopicByGroovy(RecordBean recordBean, String groovyScript) {
        Map<String,Object> param = recordToMap(recordBean);
        return GroovyUtil.runGroovyScript(groovyScript,param);
    }
}
