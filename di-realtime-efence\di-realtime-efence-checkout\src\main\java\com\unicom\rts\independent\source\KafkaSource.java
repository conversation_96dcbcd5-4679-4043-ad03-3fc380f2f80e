package com.unicom.rts.independent.source;

import com.unicom.rts.independent.schema.CustomDeserializationSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Arrays;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/11
 **/
public class KafkaSource {

    public static FlinkKafkaConsumer<ConsumerRecord<String, String>> getKafkaConsumerForTopicList(ParameterTool conf) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", conf.get("source.bootstrap"));
        properties.put("group.id", conf.get("group.id"));
        properties.put("enable.auto.commit", conf.get("enable.auto.commit"));
        properties.put("auto.commit.interval.ms", "100000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset"));
        properties.put("session.timeout.ms", "300000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        FlinkKafkaConsumer<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer<ConsumerRecord<String, String>>(
                Arrays.asList(conf.get("source.topic").split(",")), new CustomDeserializationSchema(), properties);
        consumer.setCommitOffsetsOnCheckpoints(true);
        return consumer;
    }
}
