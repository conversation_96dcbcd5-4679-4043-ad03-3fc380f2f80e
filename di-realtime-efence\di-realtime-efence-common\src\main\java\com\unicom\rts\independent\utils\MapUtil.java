package com.unicom.rts.independent.utils;

import com.unicom.rts.independent.beans.RecordBean;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MapUtil {

    public static HashMap<String, String> fileToMap(File file, String separator, int keyIndex, int valueIndex) {
        HashMap<String, String> map = new HashMap<>();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new FileReader(file));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                map.put(line.split(separator)[keyIndex], line.split(separator)[valueIndex]);
            }
            return map;
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
            }
        }
    }

    public static Map<String, Object> entityToMap(Object object) {
        Map<String, Object> map = new HashMap<>();
        for (Field field : object.getClass().getDeclaredFields()) {
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(), o);
                field.setAccessible(flag);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return map;
    }

    public static Map<String, Object> recordToMap(RecordBean recordBean) {
        Map<String, Object> map = new HashMap<>();
        map.putAll(entityToMap(recordBean));
        return map;
    }
}
