package com.unicom.realtime.bean;


import lombok.Data;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 */
@Data
public class FloData {

    private String deviceNumber;
    private String totalUseFlux;
    private String totalUseLocalFlux;
    private String totalUseProvFlux;
    private String totalUseContFlux;
    private String totalUseIdleProvFlux;
    private String provSaturation;
    private String diffprovSaturation;
    private String countrySaturation;
    private String diffprovIdleSaturation;
    private long dealTime;
    private String provId;
    private String inTime;
    private String datasource;
    private String provBalance;
    private String diffprovBalance;
    private String contBalance;
    private String restIdleProvFlux;
    private String cdhTime;
    private String overFlow;
    private String userId;
    private String cityCode;
    private boolean window;

    private String receiveTime;
    private String integrationTime;
    private String rootTime;
    private String processTime;
    // private FloData floData;
    // private String guishusheng;

    // private UserTagStateBean userTagBean;



    // @Override
    public String toString(long outTime) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String SEPARATOR = "\u0001";
        // 下发字段
        StringBuilder sb = new StringBuilder();
        sb.append(deviceNumber);
        sb.append(SEPARATOR);sb.append(totalUseFlux);
        sb.append(SEPARATOR);sb.append(totalUseLocalFlux);
        sb.append(SEPARATOR);sb.append(totalUseProvFlux);
        sb.append(SEPARATOR);sb.append(totalUseContFlux);
        sb.append(SEPARATOR);sb.append(totalUseIdleProvFlux);
        sb.append(SEPARATOR);sb.append(provSaturation);
        sb.append(SEPARATOR);sb.append(diffprovSaturation);
        sb.append(SEPARATOR);sb.append(countrySaturation);
        sb.append(SEPARATOR);sb.append(diffprovIdleSaturation);
        sb.append(SEPARATOR);sb.append(dealTime);
        sb.append(SEPARATOR);sb.append(provId);
        sb.append(SEPARATOR);sb.append(inTime);
        sb.append(SEPARATOR);sb.append(datasource);
        sb.append(SEPARATOR);sb.append(simpleDateFormat.format(outTime));
        sb.append(SEPARATOR);sb.append(provBalance);
        sb.append(SEPARATOR);sb.append(diffprovBalance);
        sb.append(SEPARATOR);sb.append(contBalance);
        sb.append(SEPARATOR);sb.append(restIdleProvFlux);
        sb.append(SEPARATOR);sb.append(cdhTime);
        sb.append(SEPARATOR);sb.append(overFlow);
        sb.append(SEPARATOR);sb.append(userId);
        sb.append(SEPARATOR);sb.append(cityCode);
        // sb.append(SEPARATOR);sb.append(floData);
        // sb.append(SEPARATOR);sb.append(guishusheng);

        return sb.toString();
    }
}
