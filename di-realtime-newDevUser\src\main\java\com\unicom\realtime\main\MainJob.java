package com.unicom.realtime.main;

import com.unicom.realtime.bean.UnionBean;
import com.unicom.realtime.process.NewUserDevProcess;
import com.unicom.realtime.source.BHTradeDeserializationSchema;
import com.unicom.realtime.source.CustPersonPsptSource;
import com.unicom.realtime.source.KafkaStream;
import com.unicom.realtime.source.UserSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.common.header.Header;

import java.util.concurrent.TimeUnit;

public class MainJob {
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String topic = conf.get("new_dev_user.topic");
        ParameterTool kafkaConf = KafkaStream.mergeConf(conf, "new_dev_user.");
        Integer checkpointInterval = conf.getInt("checkpointInterval", 5);
        Integer checkpointTimeout = conf.getInt("checkpointTimeout", 10);
        Integer minPauseBetweenCheckpoints = conf.getInt("minPauseBetweenCheckpoints", 6);
        String checkPointDir = conf.get("checkpointDataUri");
        boolean changelogEnable = conf.getBoolean("checkpoint.changelog.enable", false);
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // backend
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.enableChangelogStateBackend(changelogEnable);
        env.getCheckpointConfig().setCheckpointStorage(checkPointDir);
        // checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(checkpointInterval));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetweenCheckpoints));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        // 保存checkpoint
        checkpointConf.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        // 配置 Checkpoint
        // 消费bal kafka数据
        DataStream<UnionBean> bhTradeStream = KafkaStream.addSource(env, kafkaConf, topic,
                new BHTradeDeserializationSchema()).filter((FilterFunction<UnionBean>) unionBean -> {
            boolean okUserId = StringUtils.isNotBlank(unionBean.getUserId());
            boolean okCustId = StringUtils.isNotBlank(unionBean.getCustId());
            boolean okSerialNumber = StringUtils.isNotBlank(unionBean.getSerialNumber());
            boolean okTradeTypeCode = "10".equals(unionBean.getTradeTypeCode()) || "592".equals(unionBean.getTradeTypeCode());
            boolean okSubscribeState = "9".equals(unionBean.getSubscribeState());
            boolean okNextDealTag = "0".equals(unionBean.getNextDealTag());
            boolean okNetTypeCode = "30".equals(unionBean.getNetTypeCode()) || "50".equals(unionBean.getNetTypeCode());
            boolean okFinishDate = StringUtils.isNotBlank(unionBean.getFinishDate());
            boolean okInsert = "Insert".equalsIgnoreCase(unionBean.getOpt());
            if (okFinishDate && okNetTypeCode && okNextDealTag && okSubscribeState && okTradeTypeCode && okUserId && okCustId && okSerialNumber && okInsert) {
                return true;
            }
            return false;
        }).setParallelism(env.getParallelism());

        // 用户标签数据源
        DataStream<UnionBean> userInfoStream = new UserSource().build(env, conf);

        // 客户证件数据源
        DataStream<UnionBean> psptInfoStream = new CustPersonPsptSource().build(env, conf);

        SingleOutputStreamOperator<Tuple3<String, String, Iterable<Header>>> outStream = bhTradeStream
                .union(userInfoStream).union(psptInfoStream).keyBy(UnionBean::getCustId).process(new NewUserDevProcess(conf)).setParallelism(conf.getInt("dev.parallelism", env.getParallelism()));

//        outStream.print().setParallelism(1);

        // sink
        KafkaStream.addSink(outStream, conf);

        // execute program
        env.execute("di-realtime-newDevUser");
    }
}
