package com.unicom.rts.independent.constant;

public class CommonConstant {

    public final static String DATASOURCE_ID = "DatasourceId";

    public final static String GROUP_ID = "group.id";

    public final static String SINK_PARALLELISM = "sinkPara";

    public final static String SOURCE_PARALLELISM = "soucePara";

    public final static String PROCESS_PARALLELISM = "processParallelism";

    public final static String MYSQL_URL = "MYSQL_URL";
    public final static String MYSQL_USER = "MYSQL_USER";
    public final static String MYSQL_PASSWD = "MYSQL_PASSWD";

    public final static String SFTP_HOST = "SFTP_HOST";
    public final static String SFTP_USER = "SFTP_USER";
    public final static String START_DATE = "StartDate";

    public final static String PATH_HDFS = "pathHdfs";

    public final static String CONSUMER_SQL = "SELECT " +
            "  c.name, " +
            "  c.IP_CODE, " +
            "  c.CONNECT_TYPE," +
            "  c.CONNECT_MESSAGE," +
            "  d.topic " +
            "FROM" +
            "  t_datasource d," +
            "  t_connect c " +
            "WHERE d.CONNECT_ID = c.id " +
            "  AND d.id = '#DatasourceId'";

}
