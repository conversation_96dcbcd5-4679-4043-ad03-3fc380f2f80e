package com.unicom.realtime.function;


import com.unicom.realtime.bean.OrderTag;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class LogComputeProcess extends KeyedProcessFunction<String, OrderTag, OrderTag> {
    private final static Logger logger = LoggerFactory.getLogger(LogComputeProcess.class);
    private final ParameterTool conf;
    private MapState<String, OrderTag> orderTagLogStateMap;

    public LogComputeProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(60))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        MapStateDescriptor<String, OrderTag> mapStateDescriptor = new MapStateDescriptor<>("orderTagLogMapState", Types.STRING, Types.POJO(OrderTag.class));
        mapStateDescriptor.enableTimeToLive(ttlConfig);
        orderTagLogStateMap = getRuntimeContext().getMapState(mapStateDescriptor);
    }

    @Override
    public void processElement(OrderTag orderTag, Context context, Collector<OrderTag> collector) throws Exception {
        if (orderTag.getDatasource().toUpperCase().equals("OC_ORDER_LOG")) {
            orderTagLogStateMap.put(orderTag.getTagValue(), orderTag);
        }
        final OrderTag outTag = new OrderTag();
        outTag.setSerialNumber(orderTag.getSerialNumber());
        orderTagLogStateMap.keys().forEach(k-> outTag.setTagValue(outTag.getTagValue() + "|" + k));
        outTag.setTagValue(outTag.getTagValue().substring(1));
        collector.collect(outTag);
    }
}