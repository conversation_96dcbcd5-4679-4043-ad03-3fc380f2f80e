package com.unicom.realtime.function;


import com.unicom.realtime.bean.OrderTag;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ComputeProcess extends KeyedProcessFunction<String, OrderTag, OrderTag> {
    private final static Logger logger = LoggerFactory.getLogger(ComputeProcess.class);
    private final ParameterTool conf;
    private MapState<String, OrderTag> orderTagStateMap;

    public ComputeProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(60))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        MapStateDescriptor<String, OrderTag> mapStateDescriptor = new MapStateDescriptor<>("orderTagMapState", Types.STRING, Types.POJO(OrderTag.class));
        mapStateDescriptor.enableTimeToLive(ttlConfig);
        orderTagStateMap = getRuntimeContext().getMapState(mapStateDescriptor);
    }

    @Override
    public void processElement(OrderTag orderTag, Context context, Collector<OrderTag> collector) throws Exception {
        if (orderTag.getDatasource().toUpperCase().equals("ORDER_STREAM")) {
            if (orderTag.getOrderStatus().toUpperCase().equals("CA")) {
                orderTagStateMap.put("CA", orderTag);
            }else if (orderTag.getOrderStatus().toUpperCase().equals("00")) {
                orderTagStateMap.put("00", orderTag);
            } else if (orderTag.getOrderStatus().toUpperCase().equals("XX")) {
                orderTagStateMap.put("XX", orderTag);
            } else if (orderTag.getOrderStatus().toUpperCase().equals("X1")) {
                orderTagStateMap.put("X1", orderTag);
            } else if (orderTag.getOrderStatus().toUpperCase().equals("RSD")) {
                orderTagStateMap.put("RSD", orderTag);
            } else if (orderTag.getOrderStatus().toUpperCase().equals("JJ")) {
                orderTagStateMap.put("JJ", orderTag);
            }
        } else if (orderTag.getDatasource().toUpperCase().equals("OC_ORDER_DELIVERY")) {
            orderTagStateMap.put("DELIVERY", orderTag);
        } else if (orderTag.getDatasource().toUpperCase().equals("OC_ORDER_ITEM")) {
            orderTagStateMap.put("ITEM", orderTag);
        } else if (orderTag.getDatasource().toUpperCase().equals("OC_ORDER_LINE")) {
            orderTagStateMap.put("LINE", orderTag);
        }

        OrderTag ca = orderTagStateMap.contains("CA") ? orderTagStateMap.get("CA") : null;
        if (ca == null) {
            return;
        }
        OrderTag x1 = orderTagStateMap.contains("X1") ? orderTagStateMap.get("X1") : null;
        OrderTag xx = orderTagStateMap.contains("XX") ? orderTagStateMap.get("XX") : null;
        OrderTag zero = orderTagStateMap.contains("00") ? orderTagStateMap.get("00") : null;
        OrderTag rsd = orderTagStateMap.contains("RSD") ? orderTagStateMap.get("RSD") : null;
        OrderTag jj = orderTagStateMap.contains("JJ") ? orderTagStateMap.get("JJ") : null;
        OrderTag delivery = orderTagStateMap.contains("DELIVERY") ? orderTagStateMap.get("DELIVERY") : null;
        OrderTag item = orderTagStateMap.contains("ITEM") ? orderTagStateMap.get("ITEM") : null;
        OrderTag line = orderTagStateMap.contains("LINE") ? orderTagStateMap.get("LINE") : null;

        String tagStr = "QT";
        if (ca != null && line != null && !line.getOpt().toUpperCase().equals("DELETE")) {
            tagStr = "SMZ";
        }
        if (item != null && !item.getOpt().toUpperCase().equals("DELETE")
                && line != null && !line.getOpt().toUpperCase().equals("DELETE")) {
            tagStr = "DJH";
        }
        if (delivery != null) {
            tagStr = "DJH";
        }
        if (x1 != null || xx != null || zero != null || rsd != null || jj != null) {
            tagStr = "QT";
        }
        OrderTag outTag = new OrderTag();
        outTag.setOrderId(orderTag.getOrderId());
        outTag.setSerialNumber(ca.getSerialNumber());
        outTag.setTagValue(tagStr);
        collector.collect(outTag);
    }
}