<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.unicom.rts.independent</groupId>
    <artifactId>rts-data-independent-process</artifactId>
    <version>1.0</version>
    <modules>
        <module>di-realtime-efence</module>
        <module>di-realtime-wisdnp</module>
        <module>di-realtime-newuser2redis</module>
        <module>di-realtime-hbdk-changephone</module>
        <module>di-realtime-newuser-notused</module>
        <module>di-realtime-airport</module>
        <module>di-realtime-payment</module>
        <module>di-realtime-vcf</module>
        <module>di-realtime-debt</module>
        <module>di-realtime-thr</module>
        <module>di-realtime-numberPortabilityDetailed</module>
        <module>di-realtime-flo</module>
        <module>di-realtime-xz</module>
        <module>di-realtime-dmd5</module>
        <module>di-realtime-fttr</module>
        <module>di-realtime-utils</module>
        <module>di-realtime-newDevUser</module>
        <module>di-realtime-vcnbb</module>
        <module>di-realtime-billDueRemind</module>
        <module>di-realtime-ocorder</module>
        <module>di-realtime-order-line</module>
        <module>di-realtime-label-lcdata</module>
    </modules>
    <packaging>pom</packaging>

    <name>parent</name>


</project>
