package com.unicom.rts.bean;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;

@Builder
public class RequestParams {
    // 来电号码
    @JSONField(name = "phoneNum")
    private String phoneNum;
    // 省分编码
    @JSONField(name = "proCode")
    private String proCode;
    // 地市编码
    @JSONField(name = "cityCode")
    private String cityCode;
    // 业务类型 4016 - 新装宽带
    @JSONField(name = "businessType")
    private String businessType;
    // 来话时间
    @JSONField(name = "callTime")
    private String callTime;
    // 接触id
    @JSONField(name = "touchId")
    private String touchId;
    // 来话网别（本网、移网）
    @JSONField(name = "netType")
    private String netType;
    // 来话号码业务类型（移网、固话）
    @JSONField(name = "phoneType")
    private String phoneType;
    // 渠道（QYY：全语音 SSCJ：实时场景）
    @JSONField(name = "orderChannel")
    private String orderChannel;
    // 1或不传，默认西咸，2是无锡
    @JSONField(name = "routeValue")
    private String routeValue;

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getProCode() {
        return proCode;
    }

    public void setProCode(String proCode) {
        this.proCode = proCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCallTime() {
        return callTime;
    }

    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    public String getTouchId() {
        return touchId;
    }

    public void setTouchId(String touchId) {
        this.touchId = touchId;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public String getRouteValue() {
        return routeValue;
    }

    public void setRouteValue(String routeValue) {
        this.routeValue = routeValue;
    }
}
