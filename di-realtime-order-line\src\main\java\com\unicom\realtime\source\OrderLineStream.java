package com.unicom.realtime.source;

import com.unicom.realtime.bean.OrderLine;
import com.unicom.realtime.bean.OrderUnion;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class OrderLineStream {
    public DataStream<OrderUnion> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(OC_ORDER_LINE + options + " \n where depart_id in ('11b2g4m','11b2gcy','11b1xh6','11a0399')");
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, OrderUnion>) (row, collector) -> {
            OrderLine orderLine = new OrderLine();
            orderLine.setOrderId((String) row.getField("order_id"));
            orderLine.setOpt((String) row.getField("opt"));
            orderLine.setDatasource("OC_ORDER_LINE");
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderLine.getOrderId());
            orderUnion.setOrderLine(orderLine);
            orderUnion.setDatasource("OC_ORDER_LINE");
            collector.collect(orderUnion);
        }).returns(OrderUnion.class).uid("Oc_Order_Line_Full_TagFlatMap").name("Oc_Order_Line_Full_TagFlatMap")
                .setParallelism(conf.getInt("Oc_Order_Line_Full_TagFlatMap.parallelism", 8));
    }

    private static final String OC_ORDER_LINE = "SELECT\n" +
            "    order_id order_id,\n" +
            "    opt opt \n" +
            "    FROM dwd_r_paimon_oc_order_line_full";
}
