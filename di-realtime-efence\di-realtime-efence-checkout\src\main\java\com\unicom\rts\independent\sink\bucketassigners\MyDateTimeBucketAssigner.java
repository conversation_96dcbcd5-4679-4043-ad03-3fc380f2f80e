package com.unicom.rts.independent.sink.bucketassigners;

import org.apache.flink.streaming.api.functions.sink.filesystem.bucketassigners.DateTimeBucketAssigner;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class MyDateTimeBucketAssigner<IN> extends DateTimeBucketAssigner<IN> {
	private static final long serialVersionUID = 1L;
    
	private final String formatString;

	private final ZoneId zoneId;
    
	private transient DateTimeFormatter dateTimeFormatter;

	public MyDateTimeBucketAssigner(String formatString){
		this.formatString = formatString;
		this.zoneId = ZoneId.systemDefault();
	}

	@Override
	public String getBucketId(IN element, Context context) {
	    if (dateTimeFormatter == null) {
		dateTimeFormatter = DateTimeFormatter.ofPattern(formatString).withZone(zoneId);
	    }
	    return "date_id="+dateTimeFormatter.format(Instant.ofEpochMilli(context.currentProcessingTime()));
	}
}
