package com.unicom.rts.independent.functions;

import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;

public class FilterProcessFunction extends KeyedBroadcastProcessFunction<String, Tuple3<String, String, String>, MidDataBean, String> {


    public FilterProcessFunction() {
    }

    @Override
    public void open(Configuration parameters) throws Exception {

    }

    @Override
    public void close() throws Exception {
    }

    @Override
    public void processElement(Tuple3<String, String, String> value, ReadOnlyContext ctx, Collector<String> out) throws Exception {

    }

    @Override
    public void processBroadcastElement(MidDataBean value, Context ctx, Collector<String> out) throws Exception {

    }
}
