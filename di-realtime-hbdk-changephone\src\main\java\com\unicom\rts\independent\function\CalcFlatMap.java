// package com.unicom.rts.independent.function;
//
// import com.unicom.rts.independent.bean.LacciInfo;
// import com.unicom.rts.independent.bean.LocData;
// import com.unicom.rts.independent.bean.LocStateBean;
// import com.unicom.rts.independent.bean.OutDataBean;
// import com.unicom.rts.independent.util.DistanceUtil;
// import com.unicom.rts.independent.util.HbaseUtil;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.flink.api.common.functions.RichFlatMapFunction;
// import org.apache.flink.api.common.state.*;
// import org.apache.flink.api.common.time.Time;
// import org.apache.flink.api.java.tuple.Tuple2;
// import org.apache.flink.api.java.utils.ParameterTool;
// import org.apache.flink.configuration.Configuration;
// import org.apache.flink.metrics.Gauge;
// import org.apache.flink.util.Collector;
// import org.apache.hadoop.hbase.TableName;
// import org.apache.hadoop.hbase.client.Connection;
// import org.apache.hadoop.hbase.client.HTable;
// import org.apache.hadoop.hbase.client.Put;
// import org.apache.hadoop.hbase.client.Result;
// import org.apache.hadoop.hbase.util.Bytes;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.apache.flink.api.common.state.ValueStateDescriptor;
//
// import java.io.IOException;
// import java.nio.charset.StandardCharsets;
//
//
// /**
//  * <AUTHOR>
//  */
// public class CalcFlatMap extends RichFlatMapFunction<LocData, Tuple2<String, String>> {
//     private final static Logger logger = LoggerFactory.getLogger(TagJoinFlatMap.class);
//     private HbaseUtil hbaseUtil = null;
//     private HTable table = null;
//     private static final byte[] FAMILYNAME = "f".getBytes();
//     ParameterTool conf;
//     ValueState<LocStateBean> locState;
//     String topic;
//     private final transient long valueToExpose = 0L;
//     int locDataSecondDiff = 0;
//
//     public CalcFlatMap(ParameterTool conf) {
//         this.conf = conf;
//     }
//
//     @Override
//     public void open(Configuration parameters) throws Exception {
//         super.open(parameters);
//
//         topic = conf.get("sink.topic");
//         locDataSecondDiff = conf.getInt("locDataSecondDiff", 3600);
//         //初始化hbase链接
//         hbaseUtil = new HbaseUtil();
//         Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
//         table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));
//         //状态ttl
//         StateTtlConfig locTtlConfig = StateTtlConfig
//                 .newBuilder(Time.days(7))
//                 .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
//                 .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
//                 .cleanupInRocksdbCompactFilter(6000)
//                 .build();
//
//         //驻留触发
//         ValueStateDescriptor<LocStateBean> locStateDes = new ValueStateDescriptor<>("StayState", LocStateBean.class);
//         locStateDes.enableTimeToLive(locTtlConfig);
//         locState = getRuntimeContext().getState(locStateDes);
//
//         //时延统计metric
//         getRuntimeContext()
//                 .getMetricGroup()
//                 .gauge("proDelay", new Gauge<Long>() {
//                     @Override
//                     public Long getValue() {
//                         return valueToExpose;
//                     }
//                 });
//     }
//
//
//     @Override
//     public void flatMap(LocData locData, Collector<Tuple2<String, String>> collector) throws Exception {
//         try {
//             // logger.info("locData >>>>>>>>>>>>> " + locData.toString());
//
//             LocStateBean lastState = locState.value();
//
//             double lon = Double.parseDouble(locData.getLon());
//             double lat = Double.parseDouble(locData.getLat());
//             if (lastState == null) {
//                 locState.update(new LocStateBean(locData, lon, lat));
//                 return;
//             } else {
//                 if (!locData.getLacci().equals(lastState.getLac() + "_" + lastState.getCi())) {
//                     System.out.println("locData >>>>>>>>>>>>>> " + locData);
//                     System.out.println("lastState >>>>>>>>>>>>>> " + lastState);
//                 }
//                 String lastImei = lastState.getImei();
//                 String newImei = locData.getImei();
//                 if (lastImei.equals(newImei)) {
//                     locState.update(new LocStateBean(locData, lon, lat));
//                     return;
//                 }
//                 long lastTime = lastState.getTime();
//                 long newTime = locData.getTime();
//                 long timeDiff = Math.abs(newTime - lastTime) / 1000;
//                 if (timeDiff < locDataSecondDiff) {
//                     locState.update(new LocStateBean(locData, lon, lat));
//                     return;
//                 }
//                 String key = "hbdk_" + locData.getDeviceNumber() + locData.getUserTagStateBean().getInDateMonth();
//                 if (notSendOut(key)) {
//                     // 下发数据
//                     long distance = (long) DistanceUtil.getDistance(lon, lat, lastState.getLon(), lastState.getLat());
//
//                     OutDataBean out = new OutDataBean(locData, lastState, timeDiff, distance);
//                     // logger.info("collector.collect >>>>>>>>>>>>> {}", out);
//                     collector.collect(new Tuple2<>(topic, out.toString()));
//                 } else {
//                     locState.update(new LocStateBean(locData, lon, lat));
//                 }
//             }
//         } catch (Exception e) {
//             e.printStackTrace();
//             logger.error(locData.toString());
//         }
//     }
//
//     public boolean notSendOut(String key) throws IOException {
//         //先查询，是否下发过，下发过result isEmpty true
//         Result result = hbaseUtil.getDataFromHbase(key, table);
//         //未下发过，需要下发，且向hbase中存储
//         if (result.isEmpty()) {
//             //负载均衡
//             Put put = new Put(Bytes.toBytes(key));
//             put.addColumn(FAMILYNAME, "key".getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8));
//             if (!put.isEmpty()) {
//                 table.put(put);
//             }
//         }
//         return result.isEmpty();
//     }
//
//     // public static void main(String[] args) throws Exception {
//     //     SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
//     //     String inDate = df.format(new Date(1665554798000L));
//     //     logger.info(calculateDurationDays(inDate) + "");
//     //     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//     //     Date date = simpleDateFormat.parse("20221018202233");
//     //     String inDate2 = df.format(date);
//     //     logger.info(calculateDurationDays(inDate2) + "");
//     //     Date date2 = simpleDateFormat.parse("20221018202233");
//     //     logger.info(df.format(date2));
//     // }
// }
