package com.unicom.realtime.source;

import org.apache.flink.annotation.PublicEvolving;
import org.apache.kafka.common.header.Header;

import java.io.Serializable;
import java.util.function.Function;

/**
 * Selects a Header for the incoming record.
 *
 * @param <IN> type of the incoming record
 */
@PublicEvolving
public interface HeaderSelector<IN> extends Function<IN, Iterable<Header>>, Serializable {}
