package com.unicom.realtime.process;

import com.unicom.realtime.bean.UnionBean;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class NewUserDevProcess extends KeyedProcessFunction<String, UnionBean, Tuple3<String, String, Iterable<Header>>> {
    private final static Logger log = LoggerFactory.getLogger(NewUserDevProcess.class);

    private ValueState<UnionBean> custPsptState;
    private MapState<String, UnionBean> userState;
    private MapState<String, List<UnionBean>> tradeState;
    private StateTtlConfig ttlConfig;
    private MapStateDescriptor tradeStateDesc;
    private ParameterTool conf;

    public NewUserDevProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ttlConfig = StateTtlConfig.newBuilder(Time.minutes(30))
                .setUpdateType(StateTtlConfig.UpdateType.OnReadAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        custPsptState = getRuntimeContext().getState(new ValueStateDescriptor<>("custValueState", UnionBean.class));
        userState = getRuntimeContext().getMapState(new MapStateDescriptor<>("userMapState", BasicTypeInfo.STRING_TYPE_INFO, TypeInformation.of(UnionBean.class)));
        tradeStateDesc = new MapStateDescriptor<>("tradeMapState", BasicTypeInfo.STRING_TYPE_INFO, Types.LIST(TypeInformation.of(UnionBean.class)));
        tradeStateDesc.enableTimeToLive(ttlConfig);
        tradeState = getRuntimeContext().getMapState(tradeStateDesc);
    }

    @Override
    public void processElement(UnionBean unionBean, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        String table = unionBean.getTableName();
        String opt = unionBean.getOpt();
        RowKind rowKind = unionBean.getRowKind();
        if ("TF_F_BH_TRADE".equalsIgnoreCase(table)) {
            if (tradeState.contains(unionBean.getUserId())) {
                List<UnionBean> trades = tradeState.get(unionBean.getUserId());
                trades.add(unionBean);
            } else {
                List<UnionBean> listTrades = new ArrayList<>();
                listTrades.add(unionBean);
                tradeState.put(unionBean.getUserId(), listTrades);
            }
            doProcess(collector);
        } else if ("TF_F_USER".equalsIgnoreCase(table)) {
            if ("Delete".equalsIgnoreCase(opt) || rowKind.equals(RowKind.DELETE)) {
                userState.remove(unionBean.getUserId());
            } else {
                userState.put(unionBean.getUserId(), unionBean);
            }
            doProcess(collector);
        } else if ("TF_F_CUST_PERSON_PSPT".equalsIgnoreCase(table)) {
            if ("Delete".equalsIgnoreCase(opt) || rowKind.equals(RowKind.DELETE)) {
                custPsptState.clear();
            } else {
                custPsptState.update(unionBean);
            }
            doProcess(collector);
        }
    }

    private void doProcess(Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        if (!tradeState.isEmpty()) {
            //遍历trade状态中暂存的userId
            tradeState.keys().forEach((k) -> {
                try {
                    if (userState.contains(k) && null != custPsptState.value()) {
                        tradeState.get(k).forEach((t) -> {
                            UnionBean user = null;
                            try {
                                user = userState.get(k);
                            } catch (Exception e) {
                                log.error("NewUserDevProcess doProcess userState.get error", e);
                            }
                            UnionBean cust = null;
                            try {
                                cust = custPsptState.value();
                            } catch (IOException e) {
                                log.error("NewUserDevProcess doProcess custPsptState.value error", e);
                            }
                            UnionBean out = new UnionBean();
                            out.setUserId(user.getUserId());
                            out.setSerialNumber(user.getSerialNumber());
                            out.setCustId(user.getCustId());
                            out.setPsptTypeCode(cust.getPsptTypeCode());
                            out.setPsptId(DigestUtils.md5Hex(cust.getPsptId()));
//                            out.setPsptId(cust.getPsptId());
                            out.setDepartId(user.getDepartId());
                            out.setProductId(t.getProductId());
                            boolean validePsptTypeCode = "0".equals(cust.getPsptTypeCode()) ||
                                    "1".equals(cust.getPsptTypeCode()) || "F".equals(cust.getPsptTypeCode());
                            if (validePsptTypeCode) {
                                int psptLen = cust.getPsptId().length();
                                String psptYear = null;
                                String psptMonth = null;
                                int year = LocalDate.now().getYear();
                                int month = LocalDate.now().getMonthValue();
                                if (!"F".equals(cust.getPsptTypeCode())) {
                                    if (psptLen == 15) {
                                        psptYear = "19" + cust.getPsptId().substring(6, 8);
                                        psptMonth = cust.getPsptId().substring(8, 10);
                                    } else if (psptLen == 18) {
                                        psptYear = cust.getPsptId().substring(6, 10);
                                        psptMonth = cust.getPsptId().substring(10, 12);
                                    }
                                } else {
                                    //外国人永久居留身份证
                                    if (psptLen == 15) {
                                        psptYear = "19" + cust.getPsptId().substring(7, 9);
                                        psptMonth = cust.getPsptId().substring(9, 11);
                                    } else if (psptLen == 18) {
                                        psptYear = cust.getPsptId().substring(6, 10);
                                        psptMonth = cust.getPsptId().substring(10, 12);
                                    }
                                }
                                if ((Integer.valueOf(month) - Integer.valueOf(psptMonth)) > 0) {
                                    out.setAge(String.valueOf(year - Integer.parseInt(psptYear)));
                                } else {
                                    out.setAge(String.valueOf(year - Integer.parseInt(psptYear) - 1));
                                }
                            } else {
                                out.setAge("");
                            }
                            collector.collect(new Tuple3<>(conf.get("sink.kafka.topic"), out.toStringSm4(), out.getHeaders()));
                        });
                        tradeState.remove(k);
                    }
                } catch (Exception e) {
                    log.error("NewUserDevProcess doProcess error", e);
                }
            });
        }
    }

}
