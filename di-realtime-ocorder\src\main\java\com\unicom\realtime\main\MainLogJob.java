package com.unicom.realtime.main;

import com.unicom.realtime.bean.OrderTag;
import com.unicom.realtime.function.ComputeProcess;
import com.unicom.realtime.function.LogComputeProcess;
import com.unicom.realtime.sink.KafkaSink;
import com.unicom.realtime.source.OrderDeliveryStream;
import com.unicom.realtime.source.OrderItemStream;
import com.unicom.realtime.source.OrderLogStream;
import com.unicom.realtime.source.OrderStream;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.bridge.java.StreamStatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

public class MainLogJob {
    private final static Logger logger = LoggerFactory.getLogger(MainLogJob.class);

    public static void main(String[] args) throws Exception {
        StreamTableEnvironment tableEnv;
        StreamStatementSet stmtSet;
        ParameterTool conf = ParameterTool.fromArgs(args);
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(conf);
        //状态后端
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.disableOperatorChaining();

        //checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 5分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(5));
        // 超时时间 10 分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(20));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        DataStream<OrderTag> ocOrderLogStreamDataStream = new OrderLogStream().source(env, conf);

        DataStream<OrderTag> outStream = ocOrderLogStreamDataStream
                .keyBy(OrderTag::getSerialNumber)
                .process(new LogComputeProcess(conf)).uid("logComputeProcess").name("logComputeProcess")
                .setParallelism(conf.getInt("logComputeProcess.paralleism", 8));
        outStream.addSink(new KafkaSink()).setParallelism(conf.getInt("sink.Parallelism", 1));
        logger.info("stream start >>>>>>>>>>>>> ");

        env.execute("OrderTagSend");
    }
}
