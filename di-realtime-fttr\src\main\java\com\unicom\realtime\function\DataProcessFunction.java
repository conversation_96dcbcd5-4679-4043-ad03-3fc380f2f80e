package com.unicom.realtime.function;

import com.unicom.realtime.bean.TfBhTradeBean;
import com.unicom.realtime.bean.Rsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DataProcessFunction extends ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, TfBhTradeBean>> {

    private final static Logger logger = LoggerFactory.getLogger(DataProcessFunction.class);

    @Override
    public void processElement(ConsumerRecord<Object, Object> consumerRecord, ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, TfBhTradeBean>>.Context ctx, Collector<Tuple2<Rsp, TfBhTradeBean>> out) throws Exception {

        //logger.info("=========DataProcessFunction===================================processElement=============");
        SimpleDateFormat DF = new SimpleDateFormat("yyyy-MM-dd");
        Rsp rsp = new Rsp();
        rsp.setReceiveTime(String.valueOf(consumerRecord.timestamp()));
        Date now = new Date();

        String integrationTime = "";
        for (Header header : consumerRecord.headers()) {
            //logger.info("---------------------------for----");
            if("integration_time".equalsIgnoreCase(header.key())){
                //logger.info("---------------------------if----");
                integrationTime = new String(header.value());
                //logger.info("---------------------------integrationTime--"+integrationTime);
            }
        }
        rsp.setIntegrationTime(integrationTime);
        String value = new String((byte[]) consumerRecord.value());
        if (!StringUtils.isBlank(value)) {
            String[] arr = value.split("\u0001");
            TfBhTradeBean tfBhTradeBean = new TfBhTradeBean();

            Field[] fields = TfBhTradeBean.class.getDeclaredFields();

            if (arr.length == fields.length) {
                for (int i = 0; i < fields.length; i++) {
                    boolean flag = fields[i].isAccessible();
                    fields[i].setAccessible(true);
                    fields[i].set(tfBhTradeBean, arr[i]);
                    fields[i].setAccessible(flag);
                }
            }
            logger.info("============================================arr:{}", arr[30]);
            logger.info("============================================tfBhTradeBean:{}", tfBhTradeBean.toString());
            if ("013".equals(tfBhTradeBean.getProvinceCode()) &&
                    "9".equals(tfBhTradeBean.getSubscribeState()) &&
                    "0".equals(tfBhTradeBean.getNextDealTag()) &&
                    "0".equals(tfBhTradeBean.getCancelTag()) && !StringUtils.isBlank(tfBhTradeBean.getFinishDate())
            ) {
                String finihDate = DF.format(new Date(Long.valueOf(tfBhTradeBean.getFinishDate())));
                String today = DF.format(new Date());
                logger.info("==============finihDate:{}=========================today:{}", finihDate, today);
                    logger.info("=====DataProcessFunction================processElement===================有输出");
                if (today.equals(finihDate)){
                    rsp.setKeyBy(tfBhTradeBean.getTradeId());
                    out.collect(new Tuple2<>(rsp, tfBhTradeBean));
                }

            }

        }


    }
}
