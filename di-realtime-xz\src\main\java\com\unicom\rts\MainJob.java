package com.unicom.rts;

import com.unicom.rts.bean.Xz;
import com.unicom.rts.common.KafkaStream;
import com.unicom.rts.fun.XzClean;
import com.unicom.rts.fun.XzProcess;
import com.unicom.rts.sink.KafkaProducer;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.common.header.Header;

import java.util.concurrent.TimeUnit;

public class MainJob{
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String xzTopic = conf.get("xz.topic");
        ParameterTool xzKafkaConf = KafkaStream.mergeConf(conf, "xz.");
        // 创建流式的执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);// 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        // checkpoint
        // 开启Checkpoint，间隔为 3 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 4分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(6));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(10));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(5, Time.of(10, TimeUnit.SECONDS)));
        // 消费tb kafka数据
        DataStream<Xz> xzStream = env.addSource(KafkaStream.getRootConsumer(xzKafkaConf, xzTopic)).uid("getRootConsumer").name("getRootConsumer")
                .flatMap(new XzClean()).uid("xzClean").name("xzClean")
                .filter(new FilterFunction<Xz>() {
                    @Override
                    public boolean filter(Xz xz) throws Exception {
                        if("Delete".equals(xz.getOpt())
                            && "50".equals(xz.getNetTypeCode())
                                && "090".equals(xz.getProvinceCode())
                        && "0".equals(xz.getSubscribeState()) && "0".equals(xz.getNextDealTag()) && "0".equals(xz.getCancelTag())
                        ) {
                            return true;
                        }else {
                            return false;
                        }
                    }
                }).filter(new FilterFunction<Xz>() {
                    @Override
                    public boolean filter(Xz xz) throws Exception {
                        if("197".equals(xz.getTradeTypeCode())
                                || "148".equals(xz.getTradeTypeCode())
                        ) {
                            return false;
                        }else {
                            return true;
                        }
                    }
                });

        DataStream<Tuple3<String, String, Iterable<Header>>> outStream = xzStream.keyBy(Xz::getSerialNumber)
                .process(new XzProcess(conf)).uid("XzProcess").name("XzProcess");
        FlinkKafkaProducer<Tuple3<String , String, Iterable<Header>>> flinkKafkaProducer = KafkaProducer.getFlinkKafkaProducer(conf);
        // sink
        outStream.addSink(flinkKafkaProducer).setParallelism(Integer.parseInt(conf.get("sink.parallelism"))).name("kafkaSink");;
        // execute program
        env.execute("dp-independent-xz");
    }
}
