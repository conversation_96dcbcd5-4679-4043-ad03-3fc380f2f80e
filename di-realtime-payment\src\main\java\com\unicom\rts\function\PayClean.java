package com.unicom.rts.function;

import com.unicom.rts.bean.Pay;
import com.unicom.rts.bean.enums.PayEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

public class PayClean extends RichFlatMapFunction<ConsumerRecord<String, String>, Pay> {
    private final static Logger logger = LoggerFactory.getLogger(PayClean.class);
    private transient long delay = 0L;
    private transient long filter = 0L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        getRuntimeContext()
                .getMetricGroup()
                .gauge("delay", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return delay;
                    }
                });
        getRuntimeContext()
                .getMetricGroup()
                .gauge("filter", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return filter;
                    }
                });
    }


    @Override
    public void flatMap(ConsumerRecord<String, String> record,Collector<Pay> out) throws Exception {
        Pay pay = new Pay();
        parse(pay, record);
        //增加头
        pay.parseHeader(record);
        try {
            if (!"".equals(pay.getCharge_id()) && !"null".equals(pay.getCharge_id()) && !pay.getRecv_fee().startsWith("-")) {
                long delayMs = System.currentTimeMillis() - Long.parseLong(pay.getRecv_time());
                delay = delayMs;
                if (delayMs < 48 * 60 * 60 * 1000) {
                    out.collect(pay);
                }
            } else {
                filter++;
            }

        } catch (Exception e) {
            logger.error("{} PayClean {} ", pay.toString(), e);
        }
    }

    public void parse(Pay pay, ConsumerRecord<String,String> record) {
        String str = record.value();
        String[] columns = StringUtils.splitPreserveAllTokens(str, "\001");
        pay.setCharge_id(columns[PayEnum.charge_id.ordinal()]);
        pay.setPartition_id(columns[PayEnum.partition_id.ordinal()]);
        pay.setEparchy_code(columns[PayEnum.eparchy_code.ordinal()]);
        pay.setCity_code(columns[PayEnum.city_code.ordinal()]);
        pay.setCust_id(columns[PayEnum.cust_id.ordinal()]);
        pay.setUser_id(columns[PayEnum.user_id.ordinal()]);
        pay.setSerial_number(columns[PayEnum.serial_number.ordinal()]);
        pay.setNet_type_code(columns[PayEnum.net_type_code.ordinal()]);
        pay.setAcct_id(columns[PayEnum.acct_id.ordinal()]);
        pay.setChannel_id(columns[PayEnum.channel_id.ordinal()]);
        pay.setPayment_id(columns[PayEnum.payment_id.ordinal()]);
        pay.setPay_fee_mode_code(columns[PayEnum.pay_fee_mode_code.ordinal()]);
        pay.setPayment_op(columns[PayEnum.payment_op.ordinal()]);
        pay.setRecv_fee(columns[PayEnum.recv_fee.ordinal()]);
        pay.setLimit_money(columns[PayEnum.limit_money.ordinal()]);
        pay.setRecv_time(columns[PayEnum.recv_time.ordinal()]);
        pay.setRecv_eparchy_code(columns[PayEnum.recv_eparchy_code.ordinal()]);
        pay.setRecv_city_code(columns[PayEnum.recv_city_code.ordinal()]);
        pay.setRecv_depart_id(columns[PayEnum.recv_depart_id.ordinal()]);
        pay.setRecv_staff_id(columns[PayEnum.recv_staff_id.ordinal()]);
        pay.setPayment_reason_code(columns[PayEnum.payment_reason_code.ordinal()]);
        pay.setInput_no(columns[PayEnum.input_no.ordinal()]);
        pay.setInput_mode(columns[PayEnum.input_mode.ordinal()]);
        pay.setOuter_trade_id(columns[PayEnum.outer_trade_id.ordinal()]);
        pay.setAct_tag(columns[PayEnum.act_tag.ordinal()]);
        pay.setExtend_tag(columns[PayEnum.extend_tag.ordinal()]);
        pay.setAction_code(columns[PayEnum.action_code.ordinal()]);
        pay.setAction_event_id(columns[PayEnum.action_event_id.ordinal()]);
        pay.setPayment_rule_id(columns[PayEnum.payment_rule_id.ordinal()]);
        pay.setRemark(columns[PayEnum.remark.ordinal()]);
        pay.setCancel_tag(columns[PayEnum.cancel_tag.ordinal()]);
        pay.setCancel_staff_id(columns[PayEnum.cancel_staff_id.ordinal()]);
        pay.setCancel_depart_id(columns[PayEnum.cancel_depart_id.ordinal()]);
        pay.setCancel_city_code(columns[PayEnum.cancel_city_code.ordinal()]);
        pay.setCancel_eparchy_code(columns[PayEnum.cancel_eparchy_code.ordinal()]);
        pay.setCancel_time(columns[PayEnum.cancel_time.ordinal()]);
        pay.setCancel_charge_id(columns[PayEnum.cancel_charge_id.ordinal()]);
        pay.setRsrv_fee1(columns[PayEnum.rsrv_fee1.ordinal()]);
        pay.setRsrv_fee2(columns[PayEnum.rsrv_fee2.ordinal()]);
        pay.setRsrv_info1(columns[PayEnum.rsrv_info1.ordinal()]);
        pay.setProvince_code(columns[PayEnum.province_code.ordinal()]);
        pay.setRsrv_info2(columns[PayEnum.rsrv_info2.ordinal()]);
        pay.setStandard_kind_code(columns[PayEnum.standard_kind_code.ordinal()]);
        pay.setIn_time(columns[PayEnum.in_time.ordinal()]);
        pay.setDatasource(columns[PayEnum.datasource.ordinal()]);
        pay.setCdhtime(columns[PayEnum.cdhtime.ordinal()]);
    }
}
