package com.unicom.rts.independent.sink;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;

public class KafkaMultiSinkSerializationSchema implements KafkaSerializationSchema<Tuple2<String, String>> {

    @Override
    public ProducerRecord<byte[], byte[]> serialize(Tuple2<String, String> element, @Nullable Long aLong) {

        return new ProducerRecord<>(element.f0, element.f1.getBytes(StandardCharsets.UTF_8));

    }
}
