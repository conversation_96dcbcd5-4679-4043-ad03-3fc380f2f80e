package com.unicom.rts.independent.sink;

import com.unicom.rts.independent.beans.MidDataBean;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;

public class xinanKafkaSerializationSchema implements KafkaSerializationSchema<MidDataBean> {
    private String dstTopic;

    public xinanKafkaSerializationSchema(String dstTopic){
        this.dstTopic = dstTopic;
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(MidDataBean midDataBean, @Nullable Long timestamp) {
        return new ProducerRecord<>(dstTopic, midDataBean.toString().getBytes(StandardCharsets.UTF_8));
    }
}
