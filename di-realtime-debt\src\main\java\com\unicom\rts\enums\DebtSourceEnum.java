package com.unicom.rts.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2023-01-30 15:16
 */

@AllArgsConstructor
@Getter
public enum DebtSourceEnum {

    /**
     * hal 数据源的枚举信息
     * device_number,partition_id,user_id,service_id,main_tag,state_code,start_date,end_date,update_time,provid,cityid,intime,datasource,cdh_time
     */
    DEVICENUMBER(0, "手机号"),
    PARTITIONID(1, "分区标识"),
    USERID(2, "用户标识"),
    SERVICEID(3, "服务标识"),
    MAINTAG(4, "主体服务标志"),
    STATECODE(5, "服务状态编码"),
    STARTDATE(6, "开始时间"),
    ENDDATE(7, "结束时间"),
    UPDATETIME(8, "修改时间"),
    PROVID(9, "发生省分"),
    CITYID(10, "发生地市"),
    INTIME(11, "上行kafka时间"),
    DATASOURCE(12, "数据源"),
    CDHTIME(13,"数据接收时间");

    private final Integer code;

    private final String msg;

    /**
     *  获取枚举类
     */
    public static DebtSourceEnum getByCode(Integer code) {
        return Arrays.stream(DebtSourceEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .orElse(null);

    }

    /**
     * 根据描述获取枚举类
     */
    public static DebtSourceEnum getByMsg(String msg) {
        return Arrays.stream(DebtSourceEnum.values())
                .filter(item -> item.getMsg().equals(msg))
                .findAny()
                .orElse(null);
    }
}
