package com.unicom.rts.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-02-13 20:40
 */
public class StringUtil {


    public static String get3yys(String str){
        String yys = "";
        String  substring = str.substring(0, 3);
        if (("001").equals(substring)){
            yys = "中国电信";
        }else if (("002").equals(substring)){
            yys = "中国移动";
        }else if (("003").equals(substring)){
            yys = "中国联通";
        }
        return yys;
    }

    public  static String revosdishi(String str){
        List<String> strList = new ArrayList<>();
        for(int i=0;i<str.length();i++){
            String value = String.valueOf(str.charAt(i));
            strList.add(value);
        }
        String newDishi=strList.get(3)+strList.get(0)+strList.get(1)+strList.get(2);

        return newDishi;
    }

    public static String get14Imei(String str){
        String substring = "";
            if (str.length() == 16) {
                substring = str.substring(0, 14);

            }else if (str.length() == 15){
                substring = str.substring(0, 14);
            }
            else if (str.length() == 14) {
                substring = str;
            }
            return substring;
        }


}