package com.unicom.rts.independent.sink.writer;

import org.apache.flink.api.common.serialization.BulkWriter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPOutputStream;

public class GzipStringBulkWriter implements BulkWriter<String> {

	private GZIPOutputStream gzipOutputStream;

	public GzipStringBulkWriter(GZIPOutputStream gzipOutputStream){
		this.gzipOutputStream = gzipOutputStream;
	}

	@Override
	public void addElement(String element) throws IOException {
		// TODO Auto-generated method stub
		this.gzipOutputStream.write(element.getBytes((StandardCharsets.UTF_8)));
		this.gzipOutputStream.write("\n".getBytes((StandardCharsets.UTF_8)));
	}

	@Override
	public void finish() throws IOException {
		// TODO Auto-generated method stub
		this.flush();
		this.gzipOutputStream.close();
	}
	
	@Override
	public void flush() throws IOException {
		// TODO Auto-generated method stub
		this.gzipOutputStream.flush();
	}
	
}