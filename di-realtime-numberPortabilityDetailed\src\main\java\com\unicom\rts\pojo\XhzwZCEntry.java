package com.unicom.rts.pojo;

/**
 * <AUTHOR>
 * @date 2021-04-07 19:06
 */
public class XhzwZCEntry {


    private String zhangqi;
    private String prov;
    private String dishi;
    private String qdlx;
    private String rwqdbm;
    private String rwqdmc;
    private String slgh;
    private String shoujihao;
    private String yhbm;
    private String rwsj;
    private String wlday;
    private String sqxcsj;
    private String sxsj;
    private String ysyys;
    private String xcyys;
    private String xryys;
    private String xcdycplx;
    private String cpmc;
    private String syzdwllx;
    private String xcsyyhczsr;
    private String sfcg;
    private String sbyy;

    public String getZhangqi() {
        return zhangqi;
    }

    public void setZhangqi(String zhangqi) {
        this.zhangqi = zhangqi;
    }

    public String getProv() {
        return prov;
    }

    public void setProv(String prov) {
        this.prov = prov;
    }

    public String getDishi() {
        return dishi;
    }

    public void setDishi(String dishi) {
        this.dishi = dishi;
    }

    public String getQdlx() {
        return qdlx;
    }

    public void setQdlx(String qdlx) {
        this.qdlx = qdlx;
    }

    public String getRwqdbm() {
        return rwqdbm;
    }

    public void setRwqdbm(String rwqdbm) {
        this.rwqdbm = rwqdbm;
    }

    public String getRwqdmc() {
        return rwqdmc;
    }

    public void setRwqdmc(String rwqdmc) {
        this.rwqdmc = rwqdmc;
    }

    public String getSlgh() {
        return slgh;
    }

    public void setSlgh(String slgh) {
        this.slgh = slgh;
    }

    public String getShoujihao() {
        return shoujihao;
    }

    public void setShoujihao(String shoujihao) {
        this.shoujihao = shoujihao;
    }

    public String getYhbm() {
        return yhbm;
    }

    public void setYhbm(String yhbm) {
        this.yhbm = yhbm;
    }

    public String getRwsj() {
        return rwsj;
    }

    public void setRwsj(String rwsj) {
        this.rwsj = rwsj;
    }

    public String getWlday() {
        return wlday;
    }

    public void setWlday(String wlday) {
        this.wlday = wlday;
    }

    public String getSqxcsj() {
        return sqxcsj;
    }

    public void setSqxcsj(String sqxcsj) {
        this.sqxcsj = sqxcsj;
    }

    public String getSxsj() {
        return sxsj;
    }

    public void setSxsj(String sxsj) {
        this.sxsj = sxsj;
    }

    public String getYsyys() {
        return ysyys;
    }

    public void setYsyys(String ysyys) {
        this.ysyys = ysyys;
    }

    public String getXcyys() {
        return xcyys;
    }

    public void setXcyys(String xcyys) {
        this.xcyys = xcyys;
    }

    public String getXryys() {
        return xryys;
    }

    public void setXryys(String xryys) {
        this.xryys = xryys;
    }

    public String getXcdycplx() {
        return xcdycplx;
    }

    public void setXcdycplx(String xcdycplx) {
        this.xcdycplx = xcdycplx;
    }

    public String getCpmc() {
        return cpmc;
    }

    public void setCpmc(String cpmc) {
        this.cpmc = cpmc;
    }

    public String getSyzdwllx() {
        return syzdwllx;
    }

    public void setSyzdwllx(String syzdwllx) {
        this.syzdwllx = syzdwllx;
    }

    public String getXcsyyhczsr() {
        return xcsyyhczsr;
    }

    public void setXcsyyhczsr(String xcsyyhczsr) {
        this.xcsyyhczsr = xcsyyhczsr;
    }

    public String getSfcg() {
        return sfcg;
    }

    public void setSfcg(String sfcg) {
        this.sfcg = sfcg;
    }

    public String getSbyy() {
        return sbyy;
    }

    public void setSbyy(String sbyy) {
        this.sbyy = sbyy;
    }

    @Override
    public String toString() {
        return "XhzwZCEntry{" +
                "zhangqi='" + zhangqi + '\'' +
                ", prov='" + prov + '\'' +
                ", dishi='" + dishi + '\'' +
                ", qdlx='" + qdlx + '\'' +
                ", rwqdbm='" + rwqdbm + '\'' +
                ", rwqdmc='" + rwqdmc + '\'' +
                ", slgh='" + slgh + '\'' +
                ", shoujihao='" + shoujihao + '\'' +
                ", yhbm='" + yhbm + '\'' +
                ", rwsj='" + rwsj + '\'' +
                ", wlday='" + wlday + '\'' +
                ", sqxcsj='" + sqxcsj + '\'' +
                ", sxsj='" + sxsj + '\'' +
                ", ysyys='" + ysyys + '\'' +
                ", xcyys='" + xcyys + '\'' +
                ", xryys='" + xryys + '\'' +
                ", xcdycplx='" + xcdycplx + '\'' +
                ", cpmc='" + cpmc + '\'' +
                ", syzdwllx='" + syzdwllx + '\'' +
                ", xcsyyhczsr='" + xcsyyhczsr + '\'' +
                ", sfcg='" + sfcg + '\'' +
                ", sbyy='" + sbyy + '\'' +
                '}';
    }
}
