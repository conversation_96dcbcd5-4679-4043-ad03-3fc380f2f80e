package com.unicom.realtime.bean;

import lombok.Data;

@Data
public class BTradeProductBean {

    private String subscribeId;
    private String tradeId;
    private String acceptMonth;
    private String userId;
    private String productMode;
    private String productId;
    private String brandCode;
    private String prodItemId;
    private String modifyTag;
    private String startDate;
    private String endDate;
    private String primaryUserId;
    private String eparchyCode;
    private String provinceCode;
    private String opt;
    private String optTime;
    private String inTime;
    private String dataSource;
    private String cdhtime;
    private String databaseTag;

}
