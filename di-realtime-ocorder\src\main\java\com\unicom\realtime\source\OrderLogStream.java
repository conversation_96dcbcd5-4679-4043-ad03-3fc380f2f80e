package com.unicom.realtime.source;

import com.unicom.realtime.bean.OrderTag;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class OrderLogStream {
    public DataStream<OrderTag> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(OC_ORDER_LOG + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, OrderTag>) (row, collector) -> {
            OrderTag orderTag = new OrderTag();
            orderTag.setOrderId((String) row.getField("order_id"));
            orderTag.setSerialNumber((String) row.getField("serial_number"));
            orderTag.setTagValue((String) row.getField("order_tag"));
            orderTag.setDatasource("OC_ORDER_LOG");
            collector.collect(orderTag);
        }).returns(OrderTag.class).uid("Oc_Order_Tag_LogFlatMap").name("Oc_Order_Tag_LogFlatMap")
                .setParallelism(conf.getInt("Oc_Order_Tag_LogFlatMap.parallelism", 8));
    }

    private static final String OC_ORDER_LOG = "SELECT\n" +
            "    order_id order_id,\n" +
            "    serial_number serial_number,\n" +
            "    order_tag order_tag\n" +
            "    FROM dwa_r_paimon_oc_order_tag_log";
}
