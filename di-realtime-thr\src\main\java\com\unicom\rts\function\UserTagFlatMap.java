package com.unicom.rts.function;

import com.unicom.rts.entity.ThrData;
import com.unicom.rts.entity.UserTagBean;
import com.unicom.rts.enums.UserTagEnum;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @create 2023-06-19 17:16
 */
public class UserTagFlatMap extends RichFlatMapFunction<RowData, ThrData> {

    private final static Logger logger = LoggerFactory.getLogger(UserTagFlatMap.class);
    private static final String DATETIME_FORMAT = "yyyyMMdd";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(RowData record, Collector<ThrData> out) {
        ThrData thrData = new ThrData();
        thrData.setDeviceNumber(record.getString(UserTagEnum.device_number.getCode()).toString());
        UserTagBean userTagBean = new UserTagBean();
        userTagBean.setUserId(record.getString(UserTagEnum.rt_b_user_id.getCode()) == null ? null : record.getString(UserTagEnum.rt_b_user_id.getCode()).toString());
        userTagBean.setProvId(record.getString(UserTagEnum.rt_b_province_code.getCode()) == null ? null : record.getString(UserTagEnum.rt_b_province_code.getCode()).toString());
        userTagBean.setCityCode(record.getString(UserTagEnum.rt_b_city_code.getCode()) == null ? null : record.getString(UserTagEnum.rt_b_city_code.getCode()).toString());

        userTagBean.setAreaId(record.getString(UserTagEnum.rt_b_eparchy_code.getCode()) == null ? null : record.getString(UserTagEnum.rt_b_eparchy_code.getCode()).toString());
        thrData.setUserTagBean(userTagBean);
        thrData.setDatasource("tag");

        out.collect(thrData);
    }
}
