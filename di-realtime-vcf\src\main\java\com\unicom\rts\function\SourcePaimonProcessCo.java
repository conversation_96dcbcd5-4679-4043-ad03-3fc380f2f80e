package com.unicom.rts.function;

import com.unicom.rts.entity.Consume;
import com.unicom.rts.entity.PaimonBean;
import com.unicom.rts.entity.VcfData;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @create 9/13/23 4:22 PM
 */
public class SourcePaimonProcessCo extends KeyedCoProcessFunction<String, VcfData,PaimonBean , Tuple3<String, String, Iterable<Header>>> {

    private final static Logger logger = LoggerFactory.getLogger(SourcePaimonProcessCo.class);
    ParameterTool conf;

    private ValueState<PaimonBean> paimonInBeanValueState;
    private String outPutTopic;

    public SourcePaimonProcessCo(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        outPutTopic = conf.get("outPut.topic");
        //数据湖表状态
        ValueStateDescriptor<PaimonBean> payInBeanValueStateDes = new ValueStateDescriptor<>("PaimoonBeanValue", PaimonBean.class);
        paimonInBeanValueState = getRuntimeContext().getState(payInBeanValueStateDes);
    }

    @Override
    public void processElement1(VcfData data, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        //System.out.println("payInBeanValueState.value()"+payInBeanValueState.value());
        PaimonBean paimonBeanvalue = paimonInBeanValueState.value();

        //标准场景1.匹配上下发 2.匹配不上扔掉
        if(paimonBeanvalue!=null && paimonBeanvalue.getPhoneNum()!=null && !"".equals(paimonBeanvalue.getPhoneNum())) {

            //饱和度计算值大于75小于85

            String currFirst = "75";
            String currLast = "85";
            double currPercentTotal = 0;
            double valueTotal = 0;
            double addupupperTotal = 0;

            List<Consume> consumes = data.getVcfJson().getConsume();
            if (!consumes.isEmpty()) {
                //累计的关系
                for (Consume consume : consumes) {
                    if (consume != null && !"".equals(consume.getFeetype())) {
                        if ("".equals(consume.getCurrPercent()) || consume.getCurrPercent() == null) {
                            consume.setCurrPercent("0");
                        }
                        if ("".equals(consume.getValue()) || consume.getValue() == null) {
                            consume.setValue("0");
                        }
                        if ("".equals(consume.getAddupupper()) || consume.getAddupupper() == null) {
                            consume.setAddupupper("0");
                        }
                        double value = Double.parseDouble(consume.getValue());
                        double addupupper = Double.parseDouble(consume.getAddupupper());
                        double maxvalue = Double.parseDouble(consume.getMaxvalue());
                        double minvalue = Double.parseDouble(consume.getMinvalue());
                        if (!getLastMonth().equals(consume.getMincycle())) { //无转结
                            if (addupupper != 0.0d && !"31".equals(consume.getFeetype())) {

                            } else if (maxvalue != 0.0d && !"31".equals(consume.getFeetype())) {
                                addupupper = maxvalue;
                            } else {
                                value = 0.0d;
                                addupupper = 0.0d;
                            }
                        } else { //结转
                            if (addupupper != 0.0d) {
                                addupupper = addupupper + minvalue;
                            } else if (maxvalue != 0.0d) {
                                addupupper = maxvalue;
                            } else {
                                value = 0.0d;
                                addupupper = 0.0d;
                            }
                        }
                        valueTotal += value;
                        addupupperTotal += addupupper;
                    }
                }
                if (addupupperTotal == 0) {
                    currPercentTotal = 0;
                } else {
                    currPercentTotal = valueTotal / addupupperTotal;
                }
                //语音饱和度重新计算
                if (!(currPercentTotal >= Double.parseDouble(currFirst) && currPercentTotal <= Double.parseDouble(currLast))) {
                    System.out.println("下发数据输出"+data.toString()+"header"+data.getHeaders());
                    collector.collect(new Tuple3<>(outPutTopic, data.toString(), data.getHeaders()));
                }
            }
        }
    }

    @Override
    public void processElement2(PaimonBean paimonBean, Context context, Collector<Tuple3<String, String, Iterable<Header>>> collector) throws Exception {
        paimonInBeanValueState.update(paimonBean);
    }

    /**
     * 获得上个月月份 yyyyMM
     *
     * @return
     */
    public static String getLastMonth() {
        LocalDate today = LocalDate.now();
        today = today.minusMonths(1);
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyyMM");
        return formatters.format(today);
    }
}
