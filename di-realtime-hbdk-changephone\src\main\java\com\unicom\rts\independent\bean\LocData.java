package com.unicom.rts.independent.bean;


// import com.unicom.rts.independent.bean.UserTagBean;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocData {
    private final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
    private final String separator = "\u0001";

    String deviceNumber;
    Long time;
    String imei;
    String imsi;
    String lac;
    String ci;
    String lacci;
    String lon;
    String lat;
    String provId;
    String inTime;
    String datasource;
    String city;
    String area;

    String cdhTime;
    long joinTradeTime;
    UserTagStateBean userTagStateBean;
    LacciInfo lacciInfo;

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullBooleanAsFalse,
                SerializerFeature.UseISO8601DateFormat);
    }

}
