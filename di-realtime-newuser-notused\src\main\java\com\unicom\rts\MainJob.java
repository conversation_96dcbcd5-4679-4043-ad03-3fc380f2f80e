package com.unicom.rts;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.function.*;
import com.unicom.rts.source.KafkaStream;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class MainJob {
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String outTopic = conf.get("sink.topic");

        // 获取checkpoint 参数
        Integer checkpointInterval = conf.getInt("checkpointInterval", 5);
        Integer checkpointTimeout = conf.getInt("checkpointTimeout", 10);
        Integer minPauseBetweenCheckpoints = conf.getInt("minPauseBetweenCheckpoints", 6);
        String checkPointDir = conf.get("checkpointDataUri");

        // bus数据源参数
        Map<String, String> busSourceMap = new HashMap<>();
        busSourceMap.put("source.parallelism", conf.get("bus.source.parallelism"));
        busSourceMap.put("source.bootstrap", conf.get("bus.source.bootstrap"));
        busSourceMap.put("kafka.user", conf.get("bus.kafka.user", ""));
        busSourceMap.put("kafka.password", conf.get("bus.kafka.password", ""));
        busSourceMap.put("group.id", conf.get("bus.group.id"));
        busSourceMap.put("source.kafka.security", conf.get("bus.source.kafka.security", "false"));
        ParameterTool busConf = ParameterTool.fromMap(busSourceMap);
        busConf = conf.mergeWith(busConf);
        String busTopic = conf.get("bus.source.topic");

        // udp数据源参数
        Map<String, String> udpSourceMap = new HashMap<>();
        udpSourceMap.put("source.parallelism", conf.get("udp.source.parallelism"));
        udpSourceMap.put("source.bootstrap", conf.get("udp.source.bootstrap"));
        udpSourceMap.put("kafka.user", conf.get("udp.kafka.user", ""));
        udpSourceMap.put("kafka.password", conf.get("udp.kafka.password", ""));
        udpSourceMap.put("group.id", conf.get("udp.group.id"));
        udpSourceMap.put("source.kafka.security", conf.get("udp.source.kafka.security", "false"));
        ParameterTool udpConf = ParameterTool.fromMap(udpSourceMap);
        udpConf = conf.mergeWith(udpConf);
        String udpTopic = conf.get("udp.source.topic");

        // 漫游
        Map<String, String> roamSourceMap = new HashMap<>();
        roamSourceMap.put("source.parallelism", conf.get("roam.source.parallelism"));
        roamSourceMap.put("source.bootstrap", conf.get("roam.source.bootstrap"));
        roamSourceMap.put("kafka.user", conf.get("roam.kafka.user", "signal5g"));
        roamSourceMap.put("kafka.password", conf.get("roam.kafka.password", "jDs41MhOuZQ"));
        roamSourceMap.put("group.id", conf.get("roam.group.id"));
        roamSourceMap.put("source.kafka.security", conf.get("roam.source.kafka.security", "false"));
        roamSourceMap.put("source.sasl.jaas.config", conf.get("roam.source.sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule"));
        roamSourceMap.put("source.security.protocol", conf.get("roam.source.security.protocol", "SASL_PLAINTEXT"));
        roamSourceMap.put("source.sasl.mechanism", conf.get("roam.source.sasl.mechanism", "PLAIN"));
        ParameterTool roamConf = ParameterTool.fromMap(roamSourceMap);
        roamConf = conf.mergeWith(roamConf);
        String roamTopic = conf.get("roam.source.topic");

        // 4G和5G 信令
        Map<String, String> saConfMap = new HashMap<>();
        saConfMap.put("source.parallelism", conf.get("5gSa.source.parallelism"));
        saConfMap.put("source.bootstrap", conf.get("5gSa.source.kafka.properties.bootstrap"));
        saConfMap.put("group.id", conf.get("sa.group.id"));
        saConfMap.put("source.kafka.security", conf.get("5gSa.source.kafka.properties.security", "false"));
        ParameterTool saConf = ParameterTool.fromMap(saConfMap);
        saConf = conf.mergeWith(saConf);
        String saTopic = conf.get("5gSa.topic", "");

        boolean isSa = conf.getBoolean("is.access.5gSa", true);

        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // backend
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.getCheckpointConfig().setCheckpointStorage(checkPointDir);
        // checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(checkpointInterval));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetweenCheckpoints));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        // 保存checkpoint
        checkpointConf.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        // bus数据源
        SingleOutputStreamOperator<FormatData> bus = KafkaStream.addSourceOld(env, busConf, busTopic)
                .flatMap(new BusFlatMap())
                .uid("BusFlatMapUID")
                .name("BusFlatMap");

        SingleOutputStreamOperator<FormatData> udp = null;
        if(isSa){
            //45G信令数据
            udp = KafkaStream.addSourceOld(env, saConf, saTopic)
                    .flatMap(new SaFlatMap())
                    .uid("UdpFlatMapMapUID")
                    .name("UdpFlatMap");
        }else{
            // UDP数据源
            udp = KafkaStream.addSourceOld(env, udpConf, udpTopic)
                    .flatMap(new UdpFlatMap())
                    .uid("UdpFlatMapUID")
                    .name("UdpFlatMap");
        }

        // 漫游信令数据
        SingleOutputStreamOperator<FormatData> roam = KafkaStream.addSourceOld(env, roamConf, roamTopic)
                .flatMap(new RoamFlatMap())
                .uid("RoamFlatMapUID")
                .name("RoamFlatMap");

        // 合流数据
        DataStream<Tuple2<String, String>> outStream = bus.union(udp).union(roam)
                .keyBy(FormatData::getDeviceNumber)
                .flatMap(new ComputeFlatMap(outTopic, conf))
                .uid("ComputeFlatMapUID")
                .name("ComputeFlatMap");

        // sink
        KafkaStream.addSink(outStream, conf);

        // execute program
        env.execute("newuser-notused-heb");
    }
}
