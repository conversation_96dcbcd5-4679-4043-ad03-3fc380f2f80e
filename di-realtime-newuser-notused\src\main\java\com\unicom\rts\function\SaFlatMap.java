package com.unicom.rts.function;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.enums.TesEnum;
import com.unicom.rts.enums.UdpEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

public class SaFlatMap implements FlatMapFunction<String, FormatData> {
    private final static Logger logger = LoggerFactory.getLogger(SaFlatMap.class);

    @Override
    public void flatMap(String locStr, Collector<FormatData> collector) throws Exception {
        try {
            String[] lines = StringUtils.splitPreserveAllTokens(locStr, "|");
            String device = lines[TesEnum.MSISDN.ordinal()];
            if (device.length() != 11) {
                return;
            }
            FormatData formatData = new FormatData();
            formatData.setDeviceNumber(device);
            long startTime = Timestamp.valueOf(lines[TesEnum.START_TIME.ordinal()]).getTime();
            formatData.setInDateTime(startTime);
            formatData.setDataSource(1);
            collector.collect(formatData);
        } catch (Exception e) {
            logger.error("UdpClean Exception:{} locStr:{}", e, locStr);
        }
    }
}
