package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocData;
import com.unicom.rts.independent.enums.TesEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SaClean extends RichFlatMapFunction<String, LocData> {
    private final static Logger logger = LoggerFactory.getLogger(SaClean.class);
    private final Long delayMinute;

    public SaClean(Long delayMinute) {
        this.delayMinute = delayMinute;
    }

    @Override
    public void flatMap(String locStr, Collector<LocData> out) throws Exception {
        try {
            String[] line = StringUtils.splitPreserveAllTokens(locStr, "|");
            String device = line[TesEnum.MSISDN.ordinal()];
            if (StringUtils.isBlank(line[TesEnum.CURRENT_LAC.ordinal()]) || StringUtils.isBlank(line[TesEnum.CURRENT_CI.ordinal()])) {
                return;
            }
            if (device.length() != 11) {
                return;
            }
            if (System.currentTimeMillis() - Long.parseLong(line[TesEnum.START_TIME.ordinal()]) > delayMinute * 60 * 1000) {
                return;
            }
            LocData loc = new LocData();
            loc.setDeviceNumber(device);
            loc.setTime(Long.parseLong(line[TesEnum.START_TIME.ordinal()])); // 取信令开始时间 start_time
            loc.setImei(line[TesEnum.IMEI.ordinal()]);
            loc.setImsi(line[TesEnum.IMSI.ordinal()]);
            loc.setLac(line[TesEnum.CURRENT_LAC.ordinal()]);
            loc.setCi(line[TesEnum.CURRENT_CI.ordinal()]);
            loc.setProvId(line[TesEnum.PROV_ID.ordinal()]);  // 发生地
            //loc.setInTime(line[9]); //todo  当前时间
            loc.setDatasource("B38");//B38
            loc.setLacci(line[TesEnum.CURRENT_LAC.ordinal()] + "_" + line[TesEnum.CURRENT_CI.ordinal()]);
            //cdh 时间
            //loc.setCdhTime(line[11]); // todo  当前时间

            out.collect(loc);
        } catch (Exception e) {
            logger.error("LocClean Exception:{} locStr:{}", e, locStr);
        }
    }
}
