package com.unicom.rts.function;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.enums.BusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BusFlatMap implements FlatMapFunction<String, FormatData> {
    private final static Logger logger = LoggerFactory.getLogger(BusFlatMap.class);

    @Override
    public void flatMap(String busStr, Collector<FormatData> collector) throws Exception {
        try {
            String[] col = StringUtils.splitPreserveAllTokens(busStr, "\u0001");
            String deviceNumber = col[BusEnum.DEVICE_NUMBER.ordinal()];
            Long inDateTime = Long.parseLong(col[BusEnum.FINISH_DATE.ordinal()]);
            // 过滤河北用户
            if (!"018".equals(col[BusEnum.PROV_ID.ordinal()])) {
                return;
            }
            // 过滤掉非新入网用户
            if (!("9".equals(col[BusEnum.SUBSCRIBE_STATE.ordinal()])
                    && "0".equals(col[BusEnum.NEXT_DEAL_TAG.ordinal()])
                    && ("10".equals(col[BusEnum.TRADE_TYPE_CODE.ordinal()]) || "592".equals(col[BusEnum.TRADE_TYPE_CODE.ordinal()])))) {
                return;
            }
            // 号码长度限制
            if (deviceNumber.length() != 11) {
                return;
            }
            FormatData formatData = new FormatData();
            formatData.setDeviceNumber(deviceNumber);
            formatData.setEparchyCode(col[BusEnum.EPARCHY_CODE.ordinal()]);
            formatData.setInDateTime(inDateTime);
            formatData.setDataSource(0);// bus 0 信令 1
            collector.collect(formatData);
        } catch (Exception e) {
            logger.error("BusClean Exception:{} busStr:{}", e, busStr);
        }
    }
}
