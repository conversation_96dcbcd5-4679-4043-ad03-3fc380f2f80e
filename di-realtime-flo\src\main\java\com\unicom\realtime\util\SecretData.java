package com.unicom.realtime.util;

import com.blebase.utils.StringUtils;
import com.sec.asiainfo.simkey.kms.kmssdk.ClientLocalEncryption;
import com.sec.asiainfo.simkey.kms.kmssdk.constant.SimkeyFpeAlphabet;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class SecretData {

    /**
     * 加解密方法
     * @param localsoft  本地加密客户端
     * @param tweak
     * @param data  加密字符串
     * @param type  1:FPE加密  2:FPE压缩加密 3:FPE非压缩加密
     * @param isEncrypt true 加密 ; false 解密
     * @return 加解密数据
     * @throws Exception
     */
    public static String enDecrypt(ClientLocalEncryption localsoft, String tweak, String data, String type, boolean isEncrypt)  {


        System.out.println("===============");
        System.out.println("localsoft = " + localsoft + ", tweak = " + tweak + ", data = " + data + ", type = " + type + ", isEncrypt = " + isEncrypt);
       /* if(StringUtils.isEmpty(type)){
            throw new Exception("type不能为空");
        }
        if(StringUtils.isEmpty(tweak)){
            throw new Exception("tweak不能为空");
        }*/
        List<SimkeyFpeAlphabet> list = null;
        if(type.equals("1")){
            list = new ArrayList<>();
            list.add(SimkeyFpeAlphabet.Numeric);
            list.add(SimkeyFpeAlphabet.LowerCaseLetter);
            list.add(SimkeyFpeAlphabet.UpperCaseLetter);
            list.add(SimkeyFpeAlphabet.GB2312);
            list.add(SimkeyFpeAlphabet.SpecialCharacter);
        }
        byte[] bTweak = tweak.getBytes(StandardCharsets.UTF_8);
        String returnData = null;
        if (isEncrypt) {
            if (type.equals("1")) {
                try {
                    returnData = localsoft.fpeEncrypt(list, bTweak, data);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (type.equals("2")) {
                try {
                    returnData = localsoft.fpeCompressedEncrypt(bTweak, data, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (type.equals("3")) {
                try {
                    returnData = localsoft.fpeCompressedEncrypt(bTweak, data, false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            if (type.equals("1")) {
                try {
                    returnData = localsoft.fpeDecrypt(list,bTweak,data);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (type.equals("2")) {
                try {
                    returnData = localsoft.fpeCompressedDecrypt(bTweak, data, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (type.equals("3")) {
                try {
                    returnData = localsoft.fpeCompressedDecrypt(bTweak, data, false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return returnData;
    }
}
