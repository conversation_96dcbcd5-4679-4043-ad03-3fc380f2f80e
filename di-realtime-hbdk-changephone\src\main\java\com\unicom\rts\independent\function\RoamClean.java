package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.LocData;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

public class Roam<PERSON>lean extends RichFlatMapFunction<String, LocData> {
    private final static Logger logger = LoggerFactory.getLogger(RoamClean.class);

    @Override
    public void flatMap(String roamStr, Collector<LocData> out) throws Exception {
        try {
            String[] line = StringUtils.splitPreserveAllTokens(roamStr, "|");
            if (19 != line.length) {
                logger.error("roamStr Exception: roamStr: {}", roamStr);
                return;
            }
            if (!"018".equals(line[17])) {
                return;
            }
            if (StringUtils.isBlank(line[7]) || StringUtils.isBlank(line[8]) || StringUtils.isBlank(line[14]) || StringUtils.isBlank(line[15])) {
                return;
            }
            if (line[0].length() != 11) {
                return;
            }

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            LocData loc = new LocData();

            loc.setDeviceNumber(line[0]);
            loc.setTime(simpleDateFormat.parse(line[3]).getTime());
            loc.setImei(line[4]);
            loc.setImsi(line[1]);
            loc.setLac(line[7]);
            loc.setCi(line[8]);
            loc.setProvId(line[11]);
            loc.setLon(line[15]);
            loc.setLat(line[14]);
            loc.setLacci(line[7] + "_" + line[8]);

            loc.setDatasource("roam");
            out.collect(loc);
        } catch (Exception e) {
            logger.error("roamStr Exception:{} roamStr:{}", e, roamStr);
        }
    }
}
