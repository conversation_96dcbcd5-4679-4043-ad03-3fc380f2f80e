package com.unicom.rts.source;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @create 2022-10-06 09:42
 */
public class BroadbandVoiceKafkaSource {

    public static FlinkKafkaConsumerBase<ConsumerRecord<String, String>> getFlinkKafkaConsumer(ParameterTool conf, List<String> topicList) {

//        String kafkaUser = conf.get("kafka.user");
//        String kafkaPassword = conf.get("kafka.password");

        //kafka配置
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, conf.get("source.broker"));
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, conf.get("group.id"));
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, conf.get("enable.auto.commit"));
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,"1000");
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, conf.get("offset.reset"));
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,"30000");
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        boolean sourceSecurity = conf.getBoolean("source.kafka.security");

        if (sourceSecurity) {
            String mechanism = conf.get("source.sasl.mechanism");
            //String protocol = conf.get("source.security.protocol");
            String jaasConfig = conf.get("source.sasl.jaas.config");
//            producerProp.setProperty("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
            properties.setProperty("sasl.jaas.config", jaasConfig+" required username=\"" + conf.get("source.kafka.user") + "\" password=\"" + conf.get("source.kafka.password") + "\";");
            properties.setProperty("security.protocol", "SASL_PLAINTEXT");
            properties.setProperty("sasl.mechanism", mechanism);
        }

        FlinkKafkaConsumerBase<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer(topicList, new CustomDeSerializationSchema(), properties);


        consumer.setCommitOffsetsOnCheckpoints(true);

        return consumer;
    }
}
