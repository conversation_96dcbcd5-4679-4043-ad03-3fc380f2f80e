package com.unicom.rts.independent.source;


import com.google.gson.Gson;
import com.unicom.rts.independent.bean.LacciInfo;
import com.unicom.rts.independent.bean.LocData;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class MySqlSource extends RichSourceFunction<LocData> {
    private final static Logger logger = LoggerFactory.getLogger(MySqlSource.class);
    private PreparedStatement ps;
    private Connection connection;
    private volatile boolean isRunning = true;
    private final ParameterTool conf;
    private final String provId;
    private long sleepMillis = 0L;

    public MySqlSource(ParameterTool conf, String provId) {
        this.conf = conf;
        this.provId = provId;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        logger.info("get jdbcUrl:{}", conf.get("jdbcUrl"));
        sleepMillis = conf.getLong("mysql.sleepMillis", 24 * 60 * 60 * 1000L);
        connection = getConnection(conf.get("jdbcUrl"), conf.get("mysql.user"), conf.get("mysql.password"));

        String sql = "select * from region_point where prov_id = " + provId + ";";
        logger.info("sql :{}", sql);
        if (connection != null) {
            ps = this.connection.prepareStatement(sql);
        }
    }

    @Override
    public void run(SourceContext<LocData> ctx) throws InterruptedException {
        List<LacciInfo> list = new ArrayList<>();
        while (true) {
            try (ResultSet resultSet = ps.executeQuery()) {
                while (resultSet.next()) {
                    String lacci = resultSet.getString("lacci");
                    String lon = resultSet.getString("wgs84_lon");
                    String lat = resultSet.getString("wgs84_lat");
                    String area = resultSet.getString("area_id");
                    String city = resultSet.getString("district_id");
                    LacciInfo lacciInfo = new LacciInfo(lacci, lon, lat, area, city);
                    LocData loc = new LocData();
                    loc.setLacci(lacci);
                    loc.setLacciInfo(lacciInfo);
                    loc.setDatasource("lacci");
                    ctx.collect(loc);
                    list.add(lacciInfo);
                }
                log.info("lacci list size = {}", list.size());
                list.clear();
            } catch (Exception e) {
                // e.printStackTrace();
                log.error("error >>>>>>>>>>> " + e.getCause().getMessage());
            }
            Thread.sleep(sleepMillis);
        }
    }

    @Override
    public void cancel() {
        try {
            super.close();
            if (connection != null) {
                connection.close();
            }
            if (ps != null) {
                ps.close();
            }
        } catch (Exception e) {
            // e.printStackTrace();
            log.error("runException:{}", e.getCause().getMessage());
        }
        isRunning = false;
    }

    private Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.jdbc.Driver");
            // 注意，改成配置参数 数据库地址和用户名、密码
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            log.error("-----------mysql get connection has exception , msg = {}", e.toString());
        }
        return con;
    }
}
