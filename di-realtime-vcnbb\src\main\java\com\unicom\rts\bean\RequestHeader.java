package com.unicom.rts.bean;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;
import lombok.Builder;

@Builder
public class RequestHeader {

    @J<PERSON><PERSON><PERSON>(name = "APP_ID")
    private String appId;
    @J<PERSON><PERSON>ield(name = "TIMESTAMP")
    private String timestamp;
    @J<PERSON><PERSON><PERSON>(name = "TRANS_ID")
    private String transId;
    @JSONField(name = "TOKEN")
    private String token;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
