package com.unicom.rts.function;


import com.unicom.rts.entity.DebtData;
import com.unicom.rts.util.HbaseUtil;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class RelationUserTag extends RichFlatMapFunction<DebtData, DebtData> {
    ParameterTool conf;
    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private static String FAMILYNAME = "f";
    private String[] columns = ("rt_b_user_state_codeset").split(",");

    public RelationUserTag(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        //初始化hbase链接
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));
    }

    @Override
    public void flatMap(DebtData debt, Collector<DebtData> collector) throws Exception {
        HashMap<String, String> userTag = this.getUserTag(debt.getDeviceNumber());
        String oldStateCode = "";
        if(userTag!=null && !"".equals(userTag)) {
            oldStateCode = userTag.get("rt_b_user_state_codeset");
//            System.out.println("oldStateCode====>"+oldStateCode);
        }
        if(oldStateCode!=null && !"".equals(oldStateCode)) {
            debt.setOldStateCode(oldStateCode);
            collector.collect(debt);
        }

    }

    @Override
    public void close() throws Exception {
        if (table != null) {
            table.close();
        }
    }

    public HashMap<String, String> getUserTag(String deviceNumber) throws IOException, InterruptedException {
        List<String> columnList = Arrays.stream(columns).collect(Collectors.toList());
        HashMap<String, String> userTagBeanMap = new HashMap<>();
        List<Cell> userTagCells = hbaseUtil.getValuesWithColumnsByDeviceNumber(table,deviceNumber, FAMILYNAME, columnList);
        if(null != userTagCells && !userTagCells.isEmpty()) {
            for (Cell ce : userTagCells) {
                String colValue = new String(CellUtil.cloneValue(ce), StandardCharsets.UTF_8);
                String colName = new String(CellUtil.cloneQualifier(ce), StandardCharsets.UTF_8);
                userTagBeanMap.put(colName,colValue);
            }
            return userTagBeanMap;
        }else {
            return null;
        }
    }
}
