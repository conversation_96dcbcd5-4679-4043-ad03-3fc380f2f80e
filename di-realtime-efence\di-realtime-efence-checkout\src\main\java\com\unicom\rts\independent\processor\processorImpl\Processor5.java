package com.unicom.rts.independent.processor.processorImpl;

import com.unicom.rts.independent.enums.TagEnum;
import com.unicom.rts.independent.processor.AbstractProcessor;
import com.unicom.rts.independent.utils.HbaseUtil;
import org.apache.flink.api.java.tuple.Tuple2;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/27
 **/

/**
 * 过滤条件，groovy（groovy中仅过滤）
 * 输出 record +
 * 广东漫游、广东IMEI
 */
public class Processor5 extends AbstractProcessor {
    @Override
    public void subProcess() throws Exception {
        String productName = HbaseUtil.getValuesWithOneColumn(hTableRtsUserTag,
                recordBean.getDeviceNum(), userTagColumnFamily, TagEnum.PRODUCTNAME.getCode());

        if ("\\N".equals(productName)) {
            productName = "null";
        }
        String powerOffInd = "其他";

        out.collect(new Tuple2<>(ruleBean.getSinkTopic(), recordBean.getRecord() + "\1" + productName + "\1" + powerOffInd));
    }
}
