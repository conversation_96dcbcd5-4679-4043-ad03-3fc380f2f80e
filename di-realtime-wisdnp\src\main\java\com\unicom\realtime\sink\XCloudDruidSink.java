package com.unicom.realtime.sink;

import com.alibaba.druid.pool.DruidDataSource;
import com.unicom.realtime.bean.WisdNpData;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
public class XCloudDruidSink extends RichSinkFunction<List<WisdNpData>> {
    private final static Logger logger = LoggerFactory.getLogger(XCloudDruidSink.class);

    private DruidDataSource ds;
    private Connection conn = null;
    private PreparedStatement ps = null;

    // 生产：ITSY_DWD ，测试：RTS_TEST
    private String sqlStart = "insert into DWD_D_CUS_XZ_SS(province_code,eparchy_code,user_id,serial_number,parent_chnl_kind_name,chnl_code,chnl_name,home_net,port_out_net,port_in_net,port_time,open_date,product_id,product_name,net_type,port_date) " ;
    private String sqlEnd = " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

//    private static ResultSet rs = null;
    private ParameterTool conf;

    private Long executeSqlDateStart;
    private Long executeSqlDateEnd;



    public XCloudDruidSink(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 设置连接池参数
        ds = new DruidDataSource();
        ds.setDriverClassName("com.bonc.xcloud.jdbc.XCloudDriver");
        final String jdbcUrl = getCirroDataDriverUrl();
        ds.setUrl(jdbcUrl);
        ds.setUsername(this.conf.get("sink.xcloud.username"));
        ds.setPassword(this.conf.get("sink.xcloud.password"));


        ds.setInitialSize(Integer.parseInt(conf.get("druid.inital-size")));   //初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
        ds.setMinIdle(Integer.parseInt(conf.get("druid.min-idle")));  //最小连接池数量
        ds.setMaxActive(Integer.parseInt(conf.get("druid.max-active")));  //最大连接池数量
        ds.setMaxWait(Integer.parseInt(conf.get("druid.max-wait")));//获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置useUnfairLock属性为true使用非公平锁。
        ds.setTimeBetweenEvictionRunsMillis(Integer.parseInt(conf.get("druid.time-between-eviction-runs-millis")));  //有两个含义：1) Destroy线程会检测连接的间隔时间2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明
        ds.setMinEvictableIdleTimeMillis(Integer.parseInt(conf.get("druid.min-evictable-idle-time-millis")));  //<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        ds.setMaxEvictableIdleTimeMillis(Integer.parseInt(conf.get("druid.max-evictable-idle-time-millis")));  //<!-- 配置一个连接在池中最大生存的时间，单位是毫秒 -->
        ds.setValidationQuery("SELECT /*+HEARTBEAT*/ 1 FROM DUAL");
        ds.setTestWhileIdle(true);
        ds.setTestOnBorrow(false);
        ds.setTestOnReturn(false);
        ds.setKeepAlive(true);

//        conn = ds.getConnection();
        logger.info("wisd_np->>get jdbcUrl:{}", jdbcUrl);
//        ps = conn.prepareStatement(sql);
    }

    @Override
    public void invoke(List<WisdNpData> values, Context context) {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        executeSqlDateStart = System.currentTimeMillis();
        Date startDate = new Date(executeSqlDateStart);
        logger.info("wisd_np->executeSqlDateStart：{} ", sd.format(startDate));
        logger.info("wisd_np->executeDataSize：{} ", values.size());

        try {
            String sqlMidle = "";
            // 增加 分区设置
            if(values!=null && values.size()>0 && values.get(0).getPort_date()!=null&&""!=values.get(0).getPort_date()){
                sqlMidle = "PARTITION ON (port_date="+values.get(0).getPort_date().trim()+")";
                String sql = sqlStart + sqlMidle + sqlEnd;
                conn = ds.getConnection();
                ps = conn.prepareStatement(sql);
            /*if(conn.isClosed()){
                logger.info("wisd_np->重新获取了连接 ");
                conn = ds.getConnection();
            }

            if(ps.isClosed()){
                logger.info("wisd_np->重新获取ps ");
                ps = conn.prepareStatement(sql);
            }*/

                // 每 3000 条 执行一次
                for(int i = 0 ; i < values.size(); i++){
                    WisdNpData value = values.get(i);
//                logger.info("wisd_np->values值：{} ", value.toString());
                    ps.setString(1,value.getProvince_code());
                    ps.setString(2,value.getEparchy_code());
                    ps.setLong(3,value.getUser_id());
                    ps.setString(4,value.getSerial_number());
                    ps.setString(5,value.getParent_chnl_kind_name());
                    ps.setString(6,value.getChnl_code());
                    ps.setString(7,value.getChnl_name());
                    ps.setString(8,value.getHome_net());
                    ps.setString(9,value.getPort_out_net());
                    ps.setString(10,value.getPort_in_net());
                    ps.setLong(11,value.getPort_time());
                    ps.setLong(12,value.getOpen_date());
                    ps.setLong(13,value.getProduct_id());
                    ps.setString(14,value.getProduct_name());
                    ps.setString(15,value.getNet_type());
                    ps.setString(16,value.getPort_date());

                    ps.addBatch();
                    if((i !=0 && i % Integer.parseInt(conf.get("slink.xcloud.split.num")) == 0) || i == (values.size() - 1)){
                        logger.info("wisd_np->执行条数：{} ", i+1);
                        ps.executeBatch();
                        ps.clearBatch();
                    }
                }
            }

            executeSqlDateEnd = System.currentTimeMillis();
            Date endDate = new Date(executeSqlDateEnd);
            logger.info("wisd_np->executeSqlDateEnd：{} ", sd.format(endDate));
            logger.info("wisd_np->during Date：{} ", executeSqlDateEnd - executeSqlDateStart);

        } catch (SQLException e) {
//            e.printStackTrace();
            log.error("wisd_np->>xCloud invokeException:{} "+e.getMessage());
        }finally {
            try {
                if (!ps.isClosed() || ps != null){
                    ps.close();
                }
                if (!conn.isClosed() || conn != null){
                    conn.close();
                }
            } catch (SQLException e) {
//                e.printStackTrace();
                log.error("wisd_np->>PS SQLException "+e.getMessage());
            }

        }
    }

    @Override
    public void close(){
        try {
            super.close();
//            if (rs != null) {
//                rs.close();
//            }
            if (!ps.isClosed() || ps != null){
                ps.close();
            }
            if (!conn.isClosed() || conn != null){
                conn.close();
            }
            if(!ds.isClosed() || ds != null){
                ds.close();
            }
        } catch (Exception e) {
            logger.error("wisd_np->>xCloud runException:{} "+e.getMessage());
        }
    }

    /**
     * 获取行云连接串
     * @return 返回行云连接串
     */
    private String getCirroDataDriverUrl() {
        String url = "";
        final String iplist = conf.get("sink.xcloud.ipList");
        final String port = conf.get("sink.xcloud.portList");
        final String dbName = conf.get("sink.xcloud.dbname");
        String connectRetry = conf.get("sink.xcloud.connect.retry");
        if (connectRetry == null) {
            connectRetry = "3";
        }
        String socketTimeOut = conf.get("sink.xcloud.socket.timeout");
        if (socketTimeOut == null) {
            socketTimeOut = "7200000";
        }
        String connectDirect = conf.get("sink.xcloud.connect.direct");
        if (connectDirect == null) {
            connectDirect = "true";
        }
        String buffMemory = conf.get("sink.xcloud.buffmemory");;
        if (buffMemory != null) {
            final String[] buffM = buffMemory.split("\\*");
            int n = 1;
            for (int x = 0; x < buffM.length; ++x) {
                n *= Integer.parseInt(buffM[x]);
            }
            buffMemory = new StringBuilder(String.valueOf(n)).toString();
        }
        else {
            buffMemory = "16777216";
        }
        final String[] hostSplit = iplist.split(",");
        final StringBuilder sb = new StringBuilder();
        sb.append("jdbc:xcloud:");
        String[] array;
        for (int length = (array = hostSplit).length, i = 0; i < length; ++i) {
            final String hostStr = array[i];
            sb.append("@").append(hostStr).append(":").append(port).append("/").append(dbName);
        }
        sb.append("?");
        sb.append("connectRetry=").append(connectRetry);
        sb.append("&socketTimeOut=").append(socketTimeOut);
        sb.append("&connectDirect=").append(connectDirect);
        sb.append("&buffMemory=").append(buffMemory);
        url = sb.toString();
        return url;
    }

}

