//package com.unicom.rts.util;
//
//import org.apache.flink.api.java.utils.ParameterTool;
//import org.apache.hudi.common.model.HoodieTableType;
//import org.apache.hudi.configuration.FlinkOptions;
//import org.apache.hudi.util.HoodiePipeline;
//
//import java.util.HashMap;
//
///**
// * <AUTHOR> Jiang
// * @create 2023-06-19 17:14
// */
//public class HudiUtil {
//
//    private final ParameterTool conf;
//    public HudiUtil(ParameterTool conf) {
//        this.conf = conf;
//    }
//    public HoodiePipeline.Builder getBuilder () {
//        String targetTable = conf.get("hudi.table");
//        String basePath = conf.get("hudi.table.path");
//        HashMap<String, String> options = new HashMap<>();
//        options.put(FlinkOptions.PATH.key(), basePath);
//        options.put(FlinkOptions.TABLE_TYPE.key(), HoodieTableType.MERGE_ON_READ.name());
//        options.put(FlinkOptions.READ_AS_STREAMING.key(), "true"); // this option enable the streaming read
//        options.put(FlinkOptions.READ_START_COMMIT.key(), "earliest"); // specifies the start commit instant time
//
//        options.put(FlinkOptions.INSERT_CLUSTER.key(), "true");
//        options.put(FlinkOptions.READ_TASKS.key(), conf.get("hudi.read.tasks", "8"));
//        options.put(FlinkOptions.PAYLOAD_CLASS_NAME.key(),"org.apache.hudi.common.model.PartialUpdateAvroPayload");
//        return HoodiePipeline.builder(targetTable)
//                .column("device_number string")
//                .column("rt_b_user_id string")
//                .column("rt_b_province_code string")
//                .column("rt_b_eparchy_code string")
//                .column("rt_b_city_code string")
//                .pk("device_number")
//                .options(options);
//    }
//
//}
