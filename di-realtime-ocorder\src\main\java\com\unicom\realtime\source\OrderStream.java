package com.unicom.realtime.source;

import com.unicom.realtime.bean.OrderTag;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class OrderStream {

    public DataStream<OrderTag> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(OC_ORDER + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, OrderTag>) (row, collector) -> {
            try {
                OrderTag orderTag = new OrderTag();
                orderTag.setOrderId((String) row.getField("order_id"));
                orderTag.setOrderStatus((String) row.getField("order_status"));
                if(orderTag.getOrderStatus().toUpperCase().equals("CA")) {
                    String content = (String) row.getField("content");
                    orderTag.setSerialNumber(content.split(",")[14]);
                } else if(orderTag.getOrderStatus().toUpperCase().equals("CM")) {
                    String content = (String) row.getField("content");
                    orderTag.setOrderStatusValue(content.split(",")[5]);
                }
                orderTag.setDatasource("ORDER_STREAM");
                collector.collect(orderTag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).returns(OrderTag.class).uid("OrderTagFlatMap").name("OrderTagFlatMap")
                .setParallelism(conf.getInt("OderTagFlatMap.parallelism", 8));
    }

    private static final String OC_ORDER = "SELECT\n" +
            "    order_id order_id,\n" +
            "    order_status order_status,\n" +
            "    content content\n" +
            "    FROM dwa_r_paimon_oc_order_cal";
}
