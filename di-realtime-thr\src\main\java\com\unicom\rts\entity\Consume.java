package com.unicom.rts.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-19 20:34
 */

@Data
public class Consume {
    private String tpobjid;
    private String value;
    private String maxvalue;
    private String addupupper;
    private String feepolicyid;
    private String feepolicyname;
    private String elemtype;
    private String feeregion;
    private String feetype;
    private String mincycle;
    private String minvalue;
    private String leftvalue;
    private String currPercent;

    private String remainVoiceflow;

    private String usedVoiceflow;



    @Override
    public String toString() {
        return "{" +

                "\"tpobjid\":\"" + tpobjid + '\"' +
                ", \"value\":\"" + value + '\"' +
                ", \"maxvalue\":\"" + maxvalue + '\"' +
                ", \"addupupper\":\"" + addupupper + '\"' +
                ", \"feepolicyid\":\"" + feepolicyid + '\"' +
                ", \"feepolicyname\":\"" + feepolicyname + '\"' +
                ", \"elemtype\":\"" + elemtype + '\"' +
                ", \"feeregion\":\"" + feeregion + '\"' +
                ", \"feetype\":\"" + feetype + '\"' +
                ", \"mincycle\":\"" + mincycle + '\"' +
                ", \"minvalue\":\"" + minvalue + '\"' +
                ", \"leftvalue\":\"" + leftvalue + '\"' +
                ", \"currPercent\":\"" + currPercent + '\"' +
                ", \"remainVoiceflow\":\"" + remainVoiceflow + '\"' +
                ", \"usedVoiceflow\":\"" + usedVoiceflow + '\"' +

                '}';
    }

}
