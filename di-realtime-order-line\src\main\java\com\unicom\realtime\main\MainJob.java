package com.unicom.realtime.main;

import com.unicom.realtime.bean.*;
import com.unicom.realtime.function.OrderDevelopComputeProcess;
import com.unicom.realtime.function.OrderPayRelationComputeProcess;
import com.unicom.realtime.function.OrderPressComputeProcess;
import com.unicom.realtime.function.OrderRelationUUComputeProcess;
import com.unicom.realtime.source.KafkaSource;
import com.unicom.realtime.source.OrderLineStream;
import com.unicom.sink.KafkaSink;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class MainJob {
    private final static Logger logger = LoggerFactory.getLogger(MainJob.class);

    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        String topicSource = conf.get("source.topic");
        String sceneName = conf.get("scene.name");
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(conf);
        //状态后端
        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(conf.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.disableOperatorChaining();

        //checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(5));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 5分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(5));
        // 超时时间 10 分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(20));
        // 保存checkpoint
        checkpointConf.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        DataStream<OrderUnion> outStream = null;
        if(sceneName.toUpperCase().equals("OC_ORDER_PRESS")) {
            DataStream<OrderUnion> orderLineDataStream = new OrderLineStream().source(env, conf);
            DataStream<OrderUnion> orderPressDataStream = getOcOrderPressDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPressDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPressComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_PAYRELATION")) {
            DataStream<OrderUnion> orderLineDataStream = new OrderLineStream().source(env, conf);
            DataStream<OrderUnion> orderPayRelationDataStream = getOcOrderPayrelationDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPayRelationDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPayRelationComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_RELATION_UU")) {
            DataStream<OrderUnion> orderLineDataStream = new OrderLineStream().source(env, conf);
            DataStream<OrderUnion> orderPayRelationDataStream = getOcOrderRelationUUDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPayRelationDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderRelationUUComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_DEVELOP")) {
            DataStream<OrderUnion> orderLineDataStream = new OrderLineStream().source(env, conf);
            DataStream<OrderUnion> orderPayRelationDataStream = getOcOrderDevelopDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPayRelationDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderDevelopComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_PRESS_H")) {
            DataStream<OrderUnion> orderLineDataStream =getOcOrderLineHDataStream(conf, env);
            DataStream<OrderUnion> orderPressDataStream = getOcOrderPressDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPressDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPressComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_PAYRELATION_H")) {
            DataStream<OrderUnion> orderLineDataStream =getOcOrderLineHDataStream(conf, env);
            DataStream<OrderUnion> orderPressDataStream = getOcOrderPayrelationDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPressDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPressComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_RELATION_UU_H")) {
            DataStream<OrderUnion> orderLineDataStream =getOcOrderLineHDataStream(conf, env);
            DataStream<OrderUnion> orderPressDataStream = getOcOrderRelationUUDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPressDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPressComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        } else if(sceneName.toUpperCase().equals("OC_ORDER_DEVELOP_H")) {
            DataStream<OrderUnion> orderLineDataStream =getOcOrderLineHDataStream(conf, env);
            DataStream<OrderUnion> orderPressDataStream = getOcOrderDevelopDataStream(conf, topicSource, env);
            outStream = orderLineDataStream
                    .union(orderPressDataStream)
                    .keyBy(OrderUnion::getOrderId)
                    .process(new OrderPressComputeProcess()).uid("computeProcess").name("computeProcess")
                    .setParallelism(conf.getInt("computeProcess.paralleism", 8));
        }
        outStream.addSink(new KafkaSink());
        env.execute("Order " + sceneName + " Compute");
    }

    private static SingleOutputStreamOperator<OrderUnion> getOcOrderDevelopDataStream(ParameterTool conf, String topicSource, StreamExecutionEnvironment env) throws Exception {
        return env.addSource(KafkaSource.getRootKafkaConsumer(conf, topicSource)).map((MapFunction<String, OrderUnion>) s -> {
            String[] record = StringUtils.splitPreserveAllTokens(s, "\u0001");
            OrderDevelop orderDevelop = new OrderDevelop();
            orderDevelop.setOrderId(record[0]);
            orderDevelop.setOrderLineId(record[1]);
            orderDevelop.setSubTypeCode(record[2]);
            orderDevelop.setSubTypeValue(record[3]);
            orderDevelop.setDevelopDate(record[4]);
            orderDevelop.setDevelopManagerId(record[5]);
            orderDevelop.setDevelopManagerName(record[6]);
            orderDevelop.setDevelopStaffId(record[7]);
            orderDevelop.setDevelopStaffName(record[8]);
            orderDevelop.setDevelopNickName(record[9]);
            orderDevelop.setDevelopNickId(record[10]);
            orderDevelop.setDevelopContact(record[11]);
            orderDevelop.setProvinceCode(record[12]);
            orderDevelop.setDevelopProvinceCode(record[13]);
            orderDevelop.setDevelopCity(record[14]);
            orderDevelop.setDevelopDepartName(record[15]);
            orderDevelop.setDevelopDepartId(record[16]);
            orderDevelop.setStandardKindCode(record[17]);
            orderDevelop.setModifyTag(record[18]);
            orderDevelop.setDevelopType(record[19]);
            orderDevelop.setChannelId(record[20]);
            orderDevelop.setCodeTypeCode(record[21]);
            orderDevelop.setChannelType(record[22]);
            orderDevelop.setOpt(record[23]);
            orderDevelop.setOpttime(record[24]);
            orderDevelop.setCdhtime(record[25]);
            orderDevelop.setDataReceiveTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderDevelop.getOrderId());
            orderUnion.setOrderDevelop(orderDevelop);
            orderUnion.setDatasource("OC_ORDER_DEVELOP");
            return orderUnion;
        });
    }

    private static SingleOutputStreamOperator<OrderUnion> getOcOrderRelationUUDataStream(ParameterTool conf, String topicSource, StreamExecutionEnvironment env) throws Exception {
        return env.addSource(KafkaSource.getRootKafkaConsumer(conf, topicSource)).map((MapFunction<String, OrderUnion>) s -> {
            String[] record = StringUtils.splitPreserveAllTokens(s, "\u0001");
            OrderRelationUU orderRelationUU = new OrderRelationUU();
            orderRelationUU.setOrderId(record[0]);
            orderRelationUU.setShortCode(record[1]);
            orderRelationUU.setModifyTag(record[2]);
            orderRelationUU.setSerialNumberB(record[3]);
            orderRelationUU.setSerialNumberA(record[4]);
            orderRelationUU.setRelationTypeCode(record[5]);
            orderRelationUU.setItemId(record[6]);
            orderRelationUU.setRoleCodeA(record[7]);
            orderRelationUU.setRoleCodeB(record[8]);
            orderRelationUU.setStartDate(record[9]);
            orderRelationUU.setEndDate(record[10]);
            orderRelationUU.setOrderno(record[11]);
            orderRelationUU.setIdB(record[12]);
            orderRelationUU.setIdA(record[13]);
            orderRelationUU.setProvinceCode(record[14]);
            orderRelationUU.setOpt(record[15]);
            orderRelationUU.setOpttime(record[16]);
            orderRelationUU.setCdhtime(record[17]);
            orderRelationUU.setDataReceiveTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderRelationUU.getOrderId());
            orderUnion.setOrderRelationUU(orderRelationUU);
            orderUnion.setDatasource("OC_ORDER_RELATION_UU");
            return orderUnion;
        });
    }

    private static SingleOutputStreamOperator<OrderUnion> getOcOrderPayrelationDataStream(ParameterTool conf, String topicSource, StreamExecutionEnvironment env) throws Exception {
        return env.addSource(KafkaSource.getRootKafkaConsumer(conf, topicSource)).map((MapFunction<String, OrderUnion>) s -> {
            String[] record = StringUtils.splitPreserveAllTokens(s, "\u0001");
            OrderPayRelation orderPayrelation = new OrderPayRelation();
            orderPayrelation.setOrderId(record[0]);
            orderPayrelation.setOrderLineId(record[1]);
            orderPayrelation.setUserId(record[2]);
            orderPayrelation.setAcctId(record[3]);
            orderPayrelation.setPayitemCode(record[4]);
            orderPayrelation.setAcctPriority(record[5]);
            orderPayrelation.setUserPriority(record[6]);
            orderPayrelation.setAddupMethod(record[7]);
            orderPayrelation.setAddupMonths(record[8]);
            orderPayrelation.setBindType(record[9]);
            orderPayrelation.setStartAcycId(record[10]);
            orderPayrelation.setEndAcycId(record[11]);
            orderPayrelation.setDefaultTag(record[12]);
            orderPayrelation.setActTag(record[13]);
            orderPayrelation.setLimitType(record[14]);
            orderPayrelation.setLimitValue(record[15]);
            orderPayrelation.setComplementTag(record[16]);
            orderPayrelation.setProvinceCode(record[17]);
            orderPayrelation.setOpt(record[18]);
            orderPayrelation.setOpttime(record[19]);
            orderPayrelation.setCdhtime(record[20]);
            orderPayrelation.setDataReceiveTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderPayrelation.getOrderId());
            orderUnion.setOrderPayRelation(orderPayrelation);
            orderUnion.setDatasource("OC_ORDER_PAYRELATION");
            return orderUnion;
        });
    }

    private static SingleOutputStreamOperator<OrderUnion> getOcOrderPressDataStream(ParameterTool conf, String topicSource, StreamExecutionEnvironment env) throws Exception {
        return env.addSource(KafkaSource.getRootKafkaConsumer(conf, topicSource)).map((MapFunction<String, OrderUnion>) s -> {
            String[] record = StringUtils.splitPreserveAllTokens(s, "\u0001");
            OrderPress orderPress = new OrderPress();
            orderPress.setOrderId(record[0]);
            orderPress.setOrderLineId(record[1]);
            orderPress.setUpdateTime(record[2]);
            orderPress.setOrderState(record[3]);
            orderPress.setTradeId(record[4]);
            orderPress.setSubscribeId(record[5]);
            orderPress.setOpt(record[6]);
            orderPress.setOpttime(record[7]);
            orderPress.setCdhtime(record[8]);
            orderPress.setDataReceiveTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderPress.getOrderId());
            orderUnion.setOrderPress(orderPress);
            orderUnion.setDatasource("OC_ORDER_PRESS");
            return orderUnion;
        });
    }

    private static SingleOutputStreamOperator<OrderUnion> getOcOrderLineHDataStream(ParameterTool conf, StreamExecutionEnvironment env) throws Exception {
        return env.addSource(KafkaSource.getRootKafkaConsumer(conf, "OC_ORDER_LINE_H")).map((MapFunction<String, OrderUnion>) s -> {
            String[] record = StringUtils.splitPreserveAllTokens(s, "\u0001");
            OrderLine orderLine = new OrderLine();
            orderLine.setOrderId(record[1]);
            orderLine.setOpt(record[67]);
            OrderUnion orderUnion = new OrderUnion();
            orderUnion.setOrderId(orderLine.getOrderId());
            orderUnion.setOrderLine(orderLine);
            orderUnion.setDatasource("OC_ORDER_LINE_H");
            return orderUnion;
        });
    }
}
