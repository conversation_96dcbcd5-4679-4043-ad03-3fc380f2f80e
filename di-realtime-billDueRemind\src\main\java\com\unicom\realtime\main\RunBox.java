package com.unicom.realtime.main;

import com.unicom.realtime.bean.PhoneBill;
import com.unicom.realtime.process.PhoneBillProcessFunction;
import com.unicom.realtime.sink.KafkaSink;
import com.unicom.realtime.source.PaimonUtil;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.concurrent.TimeUnit;

public class RunBox {
    public static void main(String[] args) throws Exception {
        ParameterTool conf = ParameterTool.fromArgs(args);
        Integer checkpointInterval = conf.getInt("checkpointInterval", 5);
        Integer checkpointTimeout = conf.getInt("checkpointTimeout", 10);
        Integer minPauseBetweenCheckpoints = conf.getInt("minPauseBetweenCheckpoints", 6);
        String checkPointDir = conf.get("checkpointDataUri");
        boolean changelogEnable = conf.getBoolean("checkpoint.changelog.enable", false);
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(conf);
        // backend
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        env.enableChangelogStateBackend(changelogEnable);
        env.getCheckpointConfig().setCheckpointStorage(checkPointDir);
        // checkpoint
        // 开启Checkpoint，间隔为 5 分钟
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(checkpointInterval));
        // 配置 Checkpoint
        CheckpointConfig checkpointConf = env.getCheckpointConfig();
        checkpointConf.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // 最小间隔 6分钟
        checkpointConf.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetweenCheckpoints));
        // 超时时间 10分钟
        checkpointConf.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        // 保存checkpoint
        checkpointConf.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // 尝试重启的次数10次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10, Time.of(10, TimeUnit.SECONDS)));

        DataStream<PhoneBill> phoneBillDataStream = new PaimonUtil().source(env, conf);

        SingleOutputStreamOperator<PhoneBill> outStream = phoneBillDataStream.keyBy((KeySelector<PhoneBill, String>) phoneBill -> phoneBill.getUserNumber()).process(new PhoneBillProcessFunction());

        outStream.addSink(new KafkaSink()).setParallelism(conf.getInt("sink.Parallelism", 4));

        // execute program
        env.execute("di-realtime-billDueRemind");
    }
}
