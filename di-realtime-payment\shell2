
--pay.source.broker 10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092
--pay.enable.auto.commit true
--pay.offset.reset latest
--pay.source.kafka.security false
--pay.kafka.user sscjzx-kfk-wzgj-cluster
--pay.kafka.password tianGong#123


--group.id pay_inde_test

--pay.topic pay
--pay.source.parallelism 20

--checkpointDataUri hdfs://slfn2/user/hh_slfn2_sschj_gray/flink/checkpoints/dp-independent-payment/

--hbase.zookeeper 10.177.140.164,10.177.140.165,10.177.140.166
--hbase.zookeeper.port 2181
--hbase.user hh_slfn2_sschj_gray
--hbaseTable.duplicate hh_slfn2_sschj_gray:rts_independent_out_data_distinct

--batch.size 16384
--request.timeout.ms 120000
--sink.kafka.bootstrap 10.162.235.6:9092,10.162.235.7:9092,10.162.235.8:9092
--sink.kafka.security false
--sasl.mechanism PLAIN
--security.protocol SASL_PLAINTEXT
--sasl.jaas.config org.apache.kafka.common.security.plain.PlainLoginModule
--sink.kafka.user context
--sink.kafka.password context
--sink.parallelism 16

--outPut.topic CZJF_GZH_XKF_TEST
--kafkaConsumerStartFrom latest


--warehouse hdfs:///user/hh_slfn2_sschj/paimon
--default-database ubd_sscj_prod_flink
--paimon.options properties.group.id'='payment_prod','scan.startup.mode'='latest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000'




/**************************************************
--group.id prod_sscj_pay_independent
--pay.topic pay
--pay.source.parallelism 60
--pay.source.kafka.properties.bootstrap 10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092
--pay.source.kafka.properties.security false

--checkpointDataUri hdfs://slfn2/user/hh_slfn2_sschj/flink/checkpoints/dp-independent-payment/
--sink.parallelism 60

--hbase.zookeeper 10.177.140.164,10.177.140.165,10.177.140.166
--hbase.zookeeper.port 2181
--hbase.user hh_slfn2_sschj
--hbaseTable.duplicate hh_slfn2_sschj:rts_out_independent_data_distinct_new
--sink.kafka.properties.bootstrap ${internal.sscj.kafka.out.brokers}
--sink.kafka.properties.security true
--sink.kafka.properties.sasl.mechanism PLAIN
--sink.kafka.properties.security.protocol SASL_PLAINTEXT
--sink.kafka.properties.sasl.jaas.config org.apache.kafka.common.security.plain.PlainLoginModule
--sink.kafka.properties.user context
--sink.kafka.properties.password context
--batch.size
--request.timeout.ms
--outPut.topic CZJF_GZH_XKF
--checkpointTimeout 20

--warehouse hdfs:///user/hh_slfn2_sschj/paimon
--default-database ubd_sscj_prod_flink
--paimon.options properties.group.id'='payment_prod','scan.startup.mode'='latest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000'



create 'hh_slfn2_sschj_gray:rts_independent_out_data_distinct' ,{NAME => 'f', VERSIONS => '1', EVICT_BLOCKS_ON_CLOSE => 'false', NEW_VERSION_BEHAVIOR => 'false', KEEP_DELETED_CELLS => 'FALSE', CACHE_DATA_ON_WRITE => 'false', DATA_BLOCK_ENCODING => 'NONE', TTL => '172800 SECONDS (2 DAYS)', MIN_VERSIONS => '0', REPLICATION_SCOPE => '0', BLOOMFILTER => 'ROW', CACHE_INDEX_ON_WRITE => 'false',IN_MEMORY => 'false', CACHE_BLOOMS_ON_WRITE => 'false', PREFETCH_BLOCKS_ON_OPEN => 'false', COMPRESSION => 'NONE', BLOCKCACHE => 'true', BLOCKSIZE => '65536'}




/home/<USER>/TDH-Client/kafka/bin/kafka-topics.sh --zookeeper 10.162.235.6:2181,10.162.235.7:2181,10.162.235.8:2181 --create --topic CZJF_GZH_XKF_TEST --if-not-exists --replication-factor 2 --partitions 10