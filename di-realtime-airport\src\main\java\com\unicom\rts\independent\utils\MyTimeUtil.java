package com.unicom.rts.independent.utils;

import java.util.Calendar;
import java.util.Date;

public class MyTimeUtil {

    /**
     * 获取次日00:00时间
     * @return
     */
    public static Date getNextDate() {
        Calendar ca=Calendar.getInstance();
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        ca.add(Calendar.DAY_OF_MONTH, 1);
        return ca.getTime();
    }

    /**
     * 获取昨日00:00时间
     * @return
     */
    public static Date getYesterdayDate() {
        Calendar ca=Calendar.getInstance();
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        ca.add(Calendar.DAY_OF_MONTH, -1);
        return ca.getTime();
    }
}
