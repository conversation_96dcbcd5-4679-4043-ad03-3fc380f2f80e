package com.unicom.realtime;

import com.alibaba.fastjson.JSONObject;
import com.unicom.realtime.bean.FttrOrderBean;
import com.unicom.realtime.bean.Rsp;
import com.unicom.realtime.bean.TConnect;
import com.unicom.realtime.bean.TfBhTradeBean;
import com.unicom.realtime.constant.CommonConstant;
import com.unicom.realtime.function.BusinessProcessFunction;
import com.unicom.realtime.function.DataProcessFunction;
import com.unicom.realtime.function.InitBeanFunction;
import com.unicom.realtime.function.InitConfiguration;
import com.unicom.realtime.sink.TencentEKafkaSink;
import com.unicom.realtime.source.TencentEKafkaSource;
import com.unicom.realtime.util.DistinctJedisPoolUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import redis.clients.jedis.JedisCluster;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class FttrMain {

    public static void main(String[] args) throws Exception {

        ParameterTool parameters = ParameterTool.fromArgs(args);

        //获取flink 环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        String flinkId = "Fttr";
        String sourceParallelism = parameters.get(CommonConstant.SOURCE_PARALLELISM, "1");
        String sinkParallelism = parameters.get(CommonConstant.SINK_PARALLELISM, "1");
        String processParallelism = parameters.get(CommonConstant.PROCESS_PARALLELISM, "1");
        String businessParallelism = parameters.get(CommonConstant.BUSINESS_PARALLELISM, "1");

        String distinctKeyPrefix = parameters.get("distinctKeyPrefix", "");
        if ("".equals(distinctKeyPrefix)) {
            throw new RuntimeException("--distinctKeyPrefix参数必须输入!!!");
        }

        RocksDBStateBackend rocksDBStateBackend = new RocksDBStateBackend(parameters.get("checkpointDataUri"), true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM);//设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);


        //checkpoint
        int enableCheckpoint = parameters.getInt("enableCheckpoint", 2);
        env.enableCheckpointing(TimeUnit.MINUTES.toMillis(enableCheckpoint));

        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        int minPauseBetween = parameters.getInt("minPauseBetween", 2);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.MINUTES.toMillis(minPauseBetween));
        String startDate =  parameters.get(CommonConstant.START_DATE, null);

        int checkpointTimeout = parameters.getInt("checkpointTimeout", 10);
        checkpointConfig.setCheckpointTimeout(TimeUnit.MINUTES.toMillis(checkpointTimeout));
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // 尝试重启的次数1000次,重启间隔10秒
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(10,
                Time.of(10, TimeUnit.SECONDS)));

        //SELECT * from t_connect where id = 'c2e0dbc13c7242dc9c27d5041d2751e3'
        TConnect hbaseTConnect = InitConfiguration.initFunc(parameters);
        Configuration hbaseConfiguration = new Configuration();
        hbaseConfiguration.setString("hbase.zookeeper", hbaseTConnect.getIpCode());
        JSONObject connectMessage = JSONObject.parseObject(hbaseTConnect.getConnectMessage());
        String hbaseUser = connectMessage.getString("hbase_user");
        if (StringUtils.isBlank(hbaseUser)) {
            hbaseUser = parameters.get("hbaseUser", "hh_slfn2_sschj_gray");
        }
        hbaseConfiguration.setString("hbase.zookeeper.port", "2181");
        hbaseConfiguration.setString("hbase.user", hbaseUser);
        hbaseConfiguration.setString("hbase.table", hbaseUser + ":rts_user_tag");
        hbaseConfiguration.setString("terminated", parameters.get("terminated", "\u0001"));
        hbaseConfiguration.setString("zookeeper.znode.parent", connectMessage.getString("hbase_zookeeper_znode_parent"));

        hbaseConfiguration.setString("dst_topic_name", parameters.get("dst_topic_name", "tianjin_fttr_product_order"));
        hbaseConfiguration.setString("hbase_trade_product",hbaseUser+":"+parameters.get("hbase.trade.product", CommonConstant.HBASE_TRADE_PRODUCT));
                env.getConfig().setGlobalJobParameters(hbaseConfiguration);

        FlinkKafkaConsumer<ConsumerRecord<Object, Object>> source = TencentEKafkaSource.getFlinkKafkaConsumer(parameters);
        if(!StringUtils.isBlank(startDate)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = simpleDateFormat.parse(startDate);
            source.setStartFromTimestamp(date.getTime());
        }

        SingleOutputStreamOperator<ConsumerRecord<Object, Object>> inputStream = env.addSource(source)
                .uid(flinkId + "-Source-Kafka")
                .name(flinkId + "-Source-Kafka")
                .setParallelism(Integer.valueOf(sourceParallelism));

        MapStateDescriptor<Void, Map<String, String>> broadcastMap = new MapStateDescriptor<>(
                "initBeanBroadcast",
                Types.VOID,
                Types.MAP(Types.STRING, Types.STRING));

        BroadcastStream<HashMap> mapBroadcast = env.addSource(new InitBeanFunction(parameters))
                .uid(flinkId + "-Source-broadcastUid")
                .name(flinkId + "-Source-broadcast")
                .broadcast(broadcastMap);

        KeyedStream<Tuple2<Rsp, TfBhTradeBean>, String> proStream = inputStream
                .process(new DataProcessFunction())
                .uid(flinkId + "-DataProcessFunction")
                .name(flinkId + "-DataProcessFunction")
                .setParallelism(Integer.valueOf(processParallelism))
                .keyBy(new KeySelector<Tuple2<Rsp, TfBhTradeBean>, String>() {

                    @Override
                    public String getKey(Tuple2<Rsp, TfBhTradeBean> value) throws Exception {
                        return value.f0.getKeyBy();
                    }
                });

        //查hbase
        KeyedStream<Tuple2<Rsp, FttrOrderBean>, String> dealKafka = proStream.connect(mapBroadcast)
                .process(new BusinessProcessFunction(parameters))
                .uid(flinkId + "-BusinessProcessFunction")
                .name(flinkId + "-BusinessProcessFunction")
                .setParallelism(Integer.valueOf(businessParallelism))
                .keyBy(new KeySelector<Tuple2<Rsp, FttrOrderBean>, String>() {
                    @Override
                    public String getKey(Tuple2<Rsp, FttrOrderBean> value) throws Exception {
                        return value.f0.getKeyBy();
                    }
                });

        //去重
        KeyedStream<Tuple2<Rsp, FttrOrderBean>, String> redisFilterStream = dealKafka.filter(data -> {

            String user_id = data.f1.getUserId();
            String trade_id = data.f1.getTradeId();

            String distinct_redis_key = distinctKeyPrefix + user_id + trade_id;

            JedisCluster DistinctJedisCluster = DistinctJedisPoolUtil.getJedisCluster(parameters);
            String value = DistinctJedisCluster.get(distinct_redis_key);

            if (value != null) {
                return false;
            } else {
                DistinctJedisCluster.setex(distinct_redis_key, 86400, "1");
                return true;
            }
        }).keyBy(new KeySelector<Tuple2<Rsp, FttrOrderBean>, String>() {
            @Override
            public String getKey(Tuple2<Rsp, FttrOrderBean> value) throws Exception {
                return value.f0.getKeyBy();
            }
        });
        //下发
        FlinkKafkaProducer<Tuple2<Rsp, FttrOrderBean>> producer = TencentEKafkaSink.getFlinkKafkaProducer(parameters);
        redisFilterStream.addSink(producer)
                .uid(flinkId + "-sinkUid")
                .name(flinkId + "-sink").setParallelism(Integer.valueOf(sinkParallelism));
        env.execute();


    }

}
