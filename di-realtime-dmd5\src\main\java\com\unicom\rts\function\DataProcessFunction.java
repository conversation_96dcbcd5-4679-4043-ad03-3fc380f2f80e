package com.unicom.rts.function;

import com.unicom.rts.bean.AreaInfo;
import com.unicom.rts.bean.Rsp;
import com.unicom.rts.bean.TConnect;
import com.unicom.rts.util.HbaseUtil;
import com.unicom.rts.util.JdbcUtil;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataProcessFunction extends ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, String>> {

    private final static Logger logger = LoggerFactory.getLogger(DataProcessFunction.class);

    private Connection connection = null;

    private HashMap<String,String> map = new HashMap<String,String>();

    private String dst_topic_name="";

    private  String hbase_table = "";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        ExecutionConfig.GlobalJobParameters globalJobParameters = getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        Map<String, String> confMap = globalJobParameters.toMap();

        this.dst_topic_name = confMap.get("dst_topic_name");
        this.hbase_table = confMap.get("hbase.table");
        if (connection == null || connection.isClosed()) {
            org.apache.hadoop.conf.Configuration hbaseConfig = HBaseConfiguration.create();

            hbaseConfig.set("hbase.zookeeper.quorum", confMap.get("hbase.zookeeper"));
            hbaseConfig.set("hbase.zookeeper.property.clientPort", confMap.get("hbase.zookeeper.port"));// zookeeper端口
            hbaseConfig.set("zookeeper.znode.parent",  confMap.get("zookeeper.znode.parent"));
            hbaseConfig.set(HConstants.HBASE_RPC_READ_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_RPC_WRITE_TIMEOUT_KEY, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_OPERATION_TIMEOUT, "1800000");
            hbaseConfig.set(HConstants.HBASE_CLIENT_SCANNER_TIMEOUT_PERIOD, "1800000");
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(confMap.get("hbase.user"));
            connection = ConnectionFactory.createConnection(hbaseConfig, User.create(userGroupInformation));
            this.map = queryData(confMap);
        }
    }

    private HashMap<String, String> queryData(Map<String, String> confMap) {

        JdbcUtil jdbcUtil = new JdbcUtil();
        jdbcUtil.openMySqlConnection(confMap.get("db.url"), confMap.get("db.user"),confMap.get("db.passwd"));
        List<AreaInfo>tableArr = jdbcUtil.select("select id,areacode from tbl_contextdb_city", AreaInfo.class);
        HashMap<String, String> ret = new HashMap<>();
        tableArr.forEach(one->{
            ret.put(one.getId(),one.getAreacode());
        });
        return  ret;
    }

    @Override
    public void processElement(ConsumerRecord<Object, Object> record, ProcessFunction<ConsumerRecord<Object, Object>, Tuple2<Rsp, String>>.Context ctx, Collector<Tuple2<Rsp, String>> out) throws Exception {
        {
            String[] pp1 = new String[0];
            String[] pp2 = new String[0];
            String deviceOne="";
            String deviceTwo="";
            String values = new String((byte[]) record.value(), "GBK");
            logger.info("values :"+values);
            String[] str= values.split("\u0001", -1);
            if(!"".equals(str[9]) && str[9].startsWith("+") && str[9].length()>11){
                deviceOne=str[9].substring(3);
            }

            if(!"".equals(str[18]) && str[18].startsWith("+") && str[18].length()>11){
                deviceTwo=str[18].substring(3);
            }
            executeProcess(deviceOne);
            //卡1或卡2有至少一个为联通卡
            if(("46001".equals(str[10]) || "46006".equals(str[10]) || "46009".equals(str[10])) || ("46001".equals(str[19]) || "46006".equals(str[19]) || "46009".equals(str[19]))){
                // 卡1联通卡
                if( ("46001".equals(str[10]) || "46006".equals(str[10]) || "46009".equals(str[10])) && (!"null".equals(str[9]) && !"".equals(str[9]) && !"nodata".equals(str[9])) ){
                    logger.info("str[10]:"+str[10]+"##################"+"str[9]:"+str[9]);
                    pp1 = executeProcess(deviceOne).split(",", -1);
                    if(map.containsKey(pp1[1])){
                        pp1[1]=map.get(pp1[1]);
                    }
                }

                // 卡2联通卡
                if(("46001".equals(str[19]) || "46006".equals(str[19]) || "46009".equals(str[19])) && (!"null".equals(str[18]) && !"".equals(str[18]) && !"nodata".equals(str[18]))){
                    logger.info("str[19]:"+str[19]+"##################"+"str[18]:"+str[18]);
                    pp2 = executeProcess(deviceTwo).split(",", -1);
                    if(map.containsKey(pp2[1])){
                        pp2[1]=map.get(pp2[1]);
                    }
                }

                String integrationTime = "";
                for (Header header : record.headers()) {
                    logger.info("---------------------------for----");
                    if("integration_time".equalsIgnoreCase(header.key())){
                        logger.info("---------------------------if----");
                        integrationTime = new String(header.value());
                        logger.info("---------------------------integrationTime--"+integrationTime);
                    }
                }
                Rsp rsp = new Rsp();
                rsp.setKeyBy(deviceOne);
                rsp.setDestTopic(dst_topic_name);
                rsp.setIntegrationTime(integrationTime);
                rsp.setReceiveTime(String.valueOf(record.timestamp()));



                if(pp1.length>0 && pp2.length>0){		//两卡槽都为联通卡
                    StringBuilder stringBuilderMsisdn1 = new StringBuilder();
                    String valueMsisdn1=stringBuilderMsisdn1.append("2").append("\1"+str[0]).append("\1"+str[1]).append("\1"+str[6]).append("\1"+deviceOne).append("\1"+pp1[0]).append("\1"+pp1[1])
                            .append("\1"+str[15]).append("\1"+deviceTwo).append("\1"+pp2[0]).append("\1"+pp2[1]).append("\1"+str[33]).append("\1"+"").append("\1"+"").append("\1"+System.currentTimeMillis()).append("\1"+deviceOne).append("\1"+pp1[0]).append("\1"+pp1[1]).append("\1"+str[6]).toString();
                   // producer.send(new ProducerRecord<String,String>(dst_topic_name,valueMsisdn1));

                    out.collect(new Tuple2<>(rsp,valueMsisdn1));
                    StringBuilder stringBuilderMsisdn2 = new StringBuilder();
                    String valueMsisdn2=stringBuilderMsisdn2.append("2").append("\1"+str[0]).append("\1"+str[1]).append("\1"+str[6]).append("\1"+deviceOne).append("\1"+pp1[0]).append("\1"+pp1[1])
                            .append("\1"+str[15]).append("\1"+deviceTwo).append("\1"+pp2[0]).append("\1"+pp2[1]).append("\1"+str[33]).append("\1"+"").append("\1"+"").append("\1"+System.currentTimeMillis()).append("\1"+deviceTwo).append("\1"+pp2[0]).append("\1"+pp2[1]).append("\1"+str[15]).toString();
                   // producer.send(new ProducerRecord<String,String>(dst_topic_name,valueMsisdn2));
                    out.collect(new Tuple2<>(rsp,valueMsisdn2));
                    System.out.println("原始数据："+values);
                    System.out.println("双卡 卡1  生产  valueMsisdn1:"+valueMsisdn1);
                    System.out.println("双卡 卡2  生产  valueMsisdn2:"+valueMsisdn2);
                }else if(pp1.length>0 && pp2.length==0){  //卡1为联通卡，卡2异网或为空
                    logger.info("pp1.length:"+pp1.length);
                    StringBuilder stringBuilderMsisdn1 = new StringBuilder();
                    String valueMsisdn1=stringBuilderMsisdn1.append("2").append("\1"+str[0]).append("\1"+str[1]).append("\1"+str[6]).append("\1"+deviceOne).append("\1"+pp1[0]).append("\1"+pp1[1])
                            .append("\1"+str[15]).append("\1"+deviceTwo).append("\1"+"").append("\1"+"").append("\1"+str[33]).append("\1"+"").append("\1"+"").append("\1"+System.currentTimeMillis()).append("\1"+deviceOne).append("\1"+pp1[0]).append("\1"+pp1[1]).append("\1"+str[6]).toString();
                    logger.info("原始数据："+values);
                    logger.info("单卡 卡1  生产  valueMsisdn1:"+valueMsisdn1);
                   // producer.send(new ProducerRecord<String,String>(dst_topic_name,valueMsisdn1));
                    out.collect(new Tuple2<>(rsp,valueMsisdn1));
                }else if(pp2.length>0 && pp1.length==0){//卡2为联通卡，卡1异网或为空
                    logger.info("pp2.length:"+pp2.length);
                    StringBuilder stringBuilderMsisdn2 = new StringBuilder();
                    String valueMsisdn2=stringBuilderMsisdn2.append("2").append("\1"+str[0]).append("\1"+str[1]).append("\1"+str[6]).append("\1"+deviceOne).append("\1"+"").append("\1"+"")
                            .append("\1"+str[15]).append("\1"+deviceTwo).append("\1"+pp2[0]).append("\1"+pp2[1]).append("\1"+str[33]).append("\1"+"").append("\1"+"").append("\1"+System.currentTimeMillis()).append("\1"+deviceTwo).append("\1"+pp2[0]).append("\1"+pp2[1]).append("\1"+str[15]).toString();
                    logger.info("原始数据："+values);
                    logger.info("单卡 卡2  生产  valueMsisdn2:"+valueMsisdn2);
                    //producer.send(new ProducerRecord<String,String>(dst_topic_name,valueMsisdn2));
                    out.collect(new Tuple2<>(rsp,valueMsisdn2));
                }

            }

        }
    }

    public String executeProcess(String device_number) throws Exception {
        logger.info("-------------executeProcess-------------------"+device_number);
        Result results = HbaseUtil.getKeyValue(connection, hbase_table, device_number);
        String provCode = "null";
        String cityCode = "null";
        if (null != results && !results.isEmpty()) {
            for (Cell cell : results.rawCells()) {
                String cloumn = new String(CellUtil.cloneQualifier(cell));
                if (("off_k000046").equalsIgnoreCase(cloumn)) {
                    provCode = new String(CellUtil.cloneValue(cell));
                }
                if (("off_k003681").equalsIgnoreCase(cloumn)) {
                    cityCode = new String(CellUtil.cloneValue(cell));
                }
            }
        }
        logger.info("-------------provCode ---- cityCode--------------for----"+provCode + "," + cityCode);
        return provCode + "," + cityCode;
    }

}
