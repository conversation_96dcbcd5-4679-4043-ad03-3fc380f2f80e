package com.unicom.rts.independent.utils;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.util.*;

public class HbaseUtil {

    private final static Logger logger = Logger.getLogger(HbaseUtil.class);

    //获取hbase连接
    public static Connection connectHbase(String zkHost, String zkPort) {
        Configuration hbaseConfig = new Configuration();
        hbaseConfig.set("hbase.zookeeper.quorum", zkHost);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", zkPort);
        hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
        Configuration conf = HBaseConfiguration.create(hbaseConfig);
        try {
            Connection connection = ConnectionFactory.createConnection(conf);
            logger.info("成功连接Hbase {}" + conf.toString());
            return connection;
        } catch (IOException e) {
            logger.error("获取Hbase连接异常," + e.getClass() + "：" + e.getMessage());
            return null;
        }
    }

    //获取hbase连接
    public static Connection xinanOldConnectHbase(String zkHost, String zkPort) {
        Configuration hbaseConfig = new Configuration();
        hbaseConfig.set("hbase.zookeeper.quorum", zkHost);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", zkPort);
        hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
        Configuration conf = HBaseConfiguration.create(hbaseConfig);
        try {
            Connection connection = ConnectionFactory.createConnection(conf);
            logger.info("成功连接Hbase {}" + conf.toString());
            return connection;
        } catch (IOException e) {
            logger.error("获取Hbase连接异常," + e.getClass() + "：" + e.getMessage());
            return null;
        }
    }

    public static Connection gchConnectHbase(String zkHost, String zkPort, String user) {
        Configuration hbaseConfig = new Configuration();
        hbaseConfig.set("hbase.zookeeper.quorum", zkHost);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", zkPort);// zookeeper端口
        hbaseConfig.set("zookeeper.znode.parent", "/hbase-unsecure");
        Configuration conf = HBaseConfiguration.create(hbaseConfig);
        try {
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(user);
            Connection connection = ConnectionFactory.createConnection(conf, User.create(userGroupInformation));
            logger.info("成功连接Hbase {}" + conf.toString());
            return connection;
        } catch (IOException e) {
            logger.error("获取Hbase连接异常，" + e.getClass() + "：" + e.getMessage());
            return null;
        }
    }

    //获取HTable对象
    public static HTable getTable(Connection connection, String tableName) {
        try {
            HTable table = (HTable) connection.getTable(TableName.valueOf(tableName));
            logger.info("获取 HTable 对象成功 " + table.toString());
            return table;
        } catch (IOException e) {
            logger.error("获取 HTable 对象失败," + e.getMessage());
            return null;
        }
    }

    //获取指定列的数据
    public static List<Cell> getValuesWithColumns(HTable hTable, String rowKey, String columnFamily, List<String> columns) {
        try {
            Get get = new Get(Bytes.toBytes(rowKey));
            for (String column : columns) {
                get.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(column));
            }

            Result result = hTable.get(get);

            if (result.isEmpty()) {
                logger.info(" 查询rowKeyStr " + rowKey + " 在Table ：" + hTable.getName() + " 无这条记录 ");
                return null;
            }
            return result.listCells();

        } catch (IOException e) {
            logger.error("Table :" + hTable.getName() + " get" + rowKey + "exception: " + e);
            return null;
        }
    }

    public static String getCellListValue(List<Cell> cellList, String columnFamily, String column) {
        String cellValue = "";
        for (Cell cell : cellList) {
            String cloneFamilyValue = new String(CellUtil.cloneFamily(cell));
            String cloneQualifierValue = new String(CellUtil.cloneQualifier(cell));
            if (cloneFamilyValue.equals(columnFamily) && cloneQualifierValue.equalsIgnoreCase(column)) {
                cellValue = new String(CellUtil.cloneValue(cell));
                break;
            }
        }
        return cellValue;
    }


    //获取指定某列的数据
    public static String getValuesWithOneColumn(HTable hTable, String rowKey, String columnFamily, String column) {
        List<String> columnList = new ArrayList<>();
        columnList.add(column);
        List<Cell> listCell = getValuesWithColumns(hTable, rowKey, columnFamily, columnList);
        if (listCell != null) {
            String result = HbaseUtil.getCellListValue(listCell, columnFamily, column);
            if (!"".equals(result)) {
                return result;
            } else {
                return "";
            }
        } else {
            return "";
        }
    }


    //获取指定某列的数据，返回map结构
    public static HashMap<String, String> getValuesWithColumns(HTable hTable, String rowKey, String columnFamily, String columns) {
        List<String> columnList = Arrays.asList(columns.split(","));
        HashMap<String, String> resultMap = new HashMap<>();
        List<Cell> cellList = getValuesWithColumns(hTable, rowKey, columnFamily, columnList);
        if (cellList != null) {
            for (Cell cell : cellList) {
                String qualifier = new String(CellUtil.cloneQualifier(cell));
                String value = new String(CellUtil.cloneValue(cell));
                resultMap.put(qualifier, value);
            }
            return resultMap;
        } else {
            return null;
        }
    }


    //创建hbase put
    public static Put createPut(String rowKey, String columnFamily, String column, String value) {
        Put put = new Put(Bytes.toBytes(rowKey));
        put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(column), Bytes.toBytes(value));
        return put;

    }

    public static Put createPut(String rowKey, String columnFamily, HashMap<String,String> columnValueMap) {
        Put put = new Put(Bytes.toBytes(rowKey));
        for (Map.Entry<String, String> entry : columnValueMap.entrySet()) {
            put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(entry.getKey()), Bytes.toBytes(entry.getValue()));
        }
        return put;

    }

}

