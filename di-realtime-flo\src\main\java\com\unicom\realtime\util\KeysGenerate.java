package com.unicom.realtime.util;

import com.sec.asiainfo.simkey.kms.exception.KMSException;
import com.sec.asiainfo.simkey.kms.kmssdk.ClientLocalEncryption;
import com.sec.asiainfo.simkey.kms.kmssdk.ClientRemoteEncryption;
import com.sec.asiainfo.simkey.kms.kmssdk.KMSClient;
import com.sec.asiainfo.simkey.kms.kmssdk.entity.KMSBizKey;
import com.sec.asiainfo.simkey.kms.kmssdk.entity.KMSUserKey;
import com.sec.asiainfo.simkey.kms.kmssdk.impl.KMSClientImpl;
import tech.simkey.dove.security.HexStringConvert;

import java.util.HashMap;
import java.util.Map;

public class KeysGenerate {
    /**
     * 生成随机数
     * @param client
     * @return
     * @throws KMSException
     */
    public static String getRandom(KMSClient client) throws KMSException {
        //生成随机数
        return HexStringConvert.parseByte2HexStr(client.genRandom(16, false));
    }

    /**
     * 获取本地加密客户端
     * @param client
     * @param encKeyId
     * @param encKey
     * @param hmacKeyId
     * @param hmacKey
     * @return
     * @throws KMSException
     */
    public static ClientLocalEncryption getClientLocalEncryption(KMSClient client,String encKeyId,String encKey,String hmacKeyId,String hmacKey) throws KMSException {
        return client.useUserkeyLocalSoftEncryption(encKeyId,encKey,hmacKeyId,hmacKey);
    }
    /**
     * 本地获取KMSClient
     * @param appId
     * @param appSecret
     * @param kmsUrl
     * @return
     * @throws KMSException
     */
    public static KMSClient getLocalClient(String appId,String appSecret,String kmsUrl) throws KMSException{
        return createKMSClient(appId,appSecret,"local",kmsUrl);
    }
    /**
     * 获取硬件密钥
     * @param appId
     * @param appSecret
     * @param type
     * @param kmsUrl
     * @return
     * @throws KMSException
     */
    public static KMSClient createKMSClient(String appId,String appSecret,String type,String kmsUrl) throws KMSException {
        /*String libFileName = null;
        if (OSUtil.isWindows()) {
            libFileName = "libSimkeySDFClient.dll";
        } else {
            libFileName = "libSimkeySDFClient.so";
        }
        ClassLoader classLoader = KeysGenerate.class.getClassLoader();
        URL resource = classLoader.getResource(libFileName);
        String resourcePath = resource.getPath();
        File lib = new File(resourcePath);
        System.load(lib.getAbsolutePath());*/
        return new KMSClientImpl(appId, appSecret, type, kmsUrl);
    }
    /**
     * 获取业务密钥
     * @param client
     * @return
     * @throws Exception
     */
    public static Map<String,String> getUserKey(KMSClient client) throws Exception{
        KMSBizKey bizKey = client.genBizKey();
        ClientRemoteEncryption remote = client.useBizkeyRemoteEncryption(bizKey.getEncKeyId(), bizKey.getHmacKeyId());
        KMSUserKey encUserkey = remote.genUserKey();
        Map<String,String> map = new HashMap<>(4);
        map.put("EncKey", encUserkey.getEncKey());
        map.put("EncKeyId", encUserkey.getEncKeyId());
        map.put("HmacKey", encUserkey.getHmacKey());
        map.put("HmacKeyId", encUserkey.getHmacKeyId());
        return map;
    }
}
