package com.unicom.rts.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/7/4 16:20
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
public enum TesEnum {
    /**
     * desc
     */
    MSISDN,
    /**
     * desc
     */
    IMSI,
    /**
     * desc
     */
    START_TIME,
    /**
     * desc
     */
    END_TIME,
    /**
     * desc
     */
    IMEI,
    /**
     * desc
     */
    MCC,
    /**
     * desc
     */
    MNC,
    /**
     * desc
     */
    CURRENT_LAC,
    /**
     * desc
     */
    CURRENT_CI,
    /**
     * desc
     */
    ROAMTYPE,
    /**
     * desc
     */
    EVENTTYPE,
    /**
     * desc
     */
    RANTYPE,
    /**
     * desc
     */
    PROVINCE,
    /**
     * desc
     */
    FIRST_LAC,
    /**
     * desc
     */
    FIRST_CI,
    /**
     * desc
     */
    LATITUDE,
    /**
     * desc
     */
    LONGITUDE,
    /**
     * desc
     */
    AREA,
    /**
     * desc
     */
    HROV_ID,
    /**
     * desc
     */
    HAREA_ID,
    /**
     * desc
     */
    RMSISDN,
    /**
     * desc
     */
    RIMSI,
    /**
     * desc
     */
    SRVSTAT,
    /**
     * desc
     */
    CDRSTAT,
    /**
     * desc
     */
    DIRECTION,
    /**
     * desc
     */
    SRVORIG,
    /**
     * desc
     */
    SRVTYPE,
    /**
     * desc
     */
    POWEROFF_IND,
    /**
     * desc
     */
    CSFB_RES,
    /**
     * desc
     */
    ENODEBID,
    /**
     * desc
     */
    CI,
    /**
     * desc
     */
    DESTNET_TYPE,
    /**
     * desc
     */
    SOURCENET_TYPE,
    /**
     * desc
     */
    UENC,
    /**
     * desc
     */
    MSNC,
    /**
     * desc
     */
    RES_TIME,
    /**
     * desc
     */
    MSG_ID,
    /**
     * desc
     */
    CAUSE_CODE,
    /**
     * desc
     */
    DISTRICT_ID,
    /**
     * desc
     */
    HCOUNTRY,
    /**
     * desc
     */
    IS_FILLED,
    /**
     * desc
     */
    RECV_TIME,
    /**
     * desc
     */
    SUBEVTTYPE,
    /**
     * desc
     */
    INTERFACE_TYPE,
    /**
     * desc
     */
    VDP_UUS,
    /**
     * desc
     */
    REJECT_CAUSE,
    /**
     * desc
     */
    APN,
    /**
     * desc
     */
    CALLID,
    /**
     * desc
     */
    HLR,
    /**
     * desc
     */
    TALKLEN,
    /**
     * desc
     */
    CALLDROPFLAG,
    /**
     * desc
     */
    ERRORPHASEIND,
    /**
     * desc
     */
    MONTH_ID,
    /**
     * desc
     */
    DAY_ID,
    /**
     * desc
     */
    PROV_ID,
    /**
     * desc
     */
    SA_TYPE;
}
