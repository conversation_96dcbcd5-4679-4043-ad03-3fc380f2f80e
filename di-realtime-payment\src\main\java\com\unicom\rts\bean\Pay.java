package com.unicom.rts.bean;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Pay {
    private String charge_id;
    private String partition_id;
    private String eparchy_code;
    private String city_code;
    private String cust_id;
    private String user_id;
    private String serial_number;
    private String net_type_code;
    private String acct_id;
    private String channel_id;
    private String payment_id;
    private String pay_fee_mode_code;
    private String payment_op;
    private String recv_fee;
    private String limit_money;
    private String recv_time;
    private String recv_eparchy_code;
    private String recv_city_code;
    private String recv_depart_id;
    private String recv_staff_id;
    private String payment_reason_code;
    private String input_no;
    private String input_mode;
    private String outer_trade_id;
    private String act_tag;
    private String extend_tag;
    private String action_code;
    private String action_event_id;
    private String payment_rule_id;
    private String remark;
    private String cancel_tag;
    private String cancel_staff_id;
    private String cancel_depart_id;
    private String cancel_city_code;
    private String cancel_eparchy_code;
    private String cancel_time;
    private String cancel_charge_id;
    private String rsrv_fee1;
    private String rsrv_fee2;
    private String rsrv_info1;
    private String province_code;
    private String rsrv_info2;
    private String standard_kind_code;
    private String in_time;
    private String datasource;
    private String cdhtime;
    //从用户标签取
    private String cityCode;

    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap();

    public void parseHeader(ConsumerRecord<String,String> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, Bytes.toString(next.value()));
            }
        }
        headersMap.put("scene_id", "8");
        headersMap.put("root_time", String.valueOf((record.timestamp())));
        headersMap.put("process_time", String.valueOf(System.currentTimeMillis()));
        if (headers.lastHeader("receive_time") != null && headers.lastHeader("receive_time").value() != null) {
            headersMap.put("receive_time",new String(headers.lastHeader("receive_time").value()));
        }
        if (headers.lastHeader("integration_time") != null && headers.lastHeader("integration_time").value() != null) {
            headersMap.put("integration_time",new String(headers.lastHeader("integration_time").value()) );
        }
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", String.valueOf((System.currentTimeMillis())));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    @Override
    public String toString() {
        String s = "\001";
        StringBuilder sb = new StringBuilder();
        sb.append(charge_id);
        sb.append(s);
        sb.append(partition_id);
        sb.append(s);
        sb.append(eparchy_code);
        sb.append(s);
        sb.append(city_code);
        sb.append(s);
        sb.append(cust_id);
        sb.append(s);
        sb.append(user_id);
        sb.append(s);
        sb.append(serial_number);
        sb.append(s);
        sb.append(net_type_code);
        sb.append(s);
        sb.append(acct_id);
        sb.append(s);
        sb.append(channel_id);
        sb.append(s);
        sb.append(payment_id);
        sb.append(s);
        sb.append(pay_fee_mode_code);
        sb.append(s);
        sb.append(payment_op);
        sb.append(s);
        sb.append(recv_fee);
        sb.append(s);
        sb.append(limit_money);
        sb.append(s);
        sb.append(recv_time);
        sb.append(s);
        sb.append(recv_eparchy_code);
        sb.append(s);
        sb.append(recv_city_code);
        sb.append(s);
        sb.append(recv_depart_id);
        sb.append(s);
        sb.append(recv_staff_id);
        sb.append(s);
        sb.append(payment_reason_code);
        sb.append(s);
        sb.append(input_no);
        sb.append(s);
        sb.append(input_mode);
        sb.append(s);
        sb.append(outer_trade_id);
        sb.append(s);
        sb.append(act_tag);
        sb.append(s);
        sb.append(extend_tag);
        sb.append(s);
        sb.append(action_code);
        sb.append(s);
        sb.append(action_event_id);
        sb.append(s);
        sb.append(payment_rule_id);
        sb.append(s);
        sb.append(remark);
        sb.append(s);
        sb.append(cancel_tag);
        sb.append(s);
        sb.append(cancel_staff_id);
        sb.append(s);
        sb.append(cancel_depart_id);
        sb.append(s);
        sb.append(cancel_city_code);
        sb.append(s);
        sb.append(cancel_eparchy_code);
        sb.append(s);
        sb.append(cancel_time);
        sb.append(s);
        sb.append(cancel_charge_id);
        sb.append(s);
        sb.append(rsrv_fee1);
        sb.append(s);
        sb.append(rsrv_fee2);
        sb.append(s);
        sb.append(rsrv_info1);
        sb.append(s);
        sb.append(province_code);
        sb.append(s);
        sb.append(rsrv_info2);
        sb.append(s);
        sb.append(standard_kind_code);
        sb.append(s);
        sb.append(in_time);
        sb.append(s);
        sb.append(datasource);
        sb.append(s);
        sb.append(DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss.SSS"));
        sb.append(s);
        sb.append(cdhtime);
        sb.append(s);
        sb.append(StrUtil.nullToEmpty(city_code)); //区县编码

        return sb.toString();
    }
}
