package com.unicom.realtime.bean;

import lombok.Data;

@Data
public class FttrOrderBean {

    //业务流水号   TF_BH_TRADE
    private String tradeId;

    //用户标识     TF_BH_TRADE
    private String userId;

    //服务号码     TF_BH_TRADE
    private String  serialNumber;

    //受理时间     TF_BH_TRADE
    private String acceptDate;

    //完成时间     TF_BH_TRADE
    private String finishDate;

    //受理渠道     TF_BH_TRADE
    private String tradeDepartId;

    //受理员工     TF_BH_TRADE
    private String tradeStaffId;

    //受理业务区   TF_BH_TRADE
    private String tradeCityCode;

    //产品开始时间 TF_B_TRADE_PRODUCT
    private String startDate;

    //产品结束时间 TF_B_TRADE_PRODUCT
    private String endDate;

    //产品标识     TF_B_TRADE_PRODUCT
    private String productId;

    //修改标志     TF_B_TRADE_PRODUCT
    private String modifyTag;

    //接入方式编码 TF_BH_TRADE
    private String inModeCode;

    //订单号       TF_BH_TRADE
    private String subscribeId;

    //业务区编码   TF_F_USER
    private String cityCode;

    //建档时间     TF_F_USER
    private String inDate;

    //FTTR产品类型 FTTR产品码表
    private String svcType;

    public String sendString() {
        return  tradeId + '\u0001' +
                userId + '\u0001' +
                serialNumber + '\u0001' +
                acceptDate + '\u0001' +
                finishDate + '\u0001' +
                tradeDepartId + '\u0001' +
                tradeStaffId + '\u0001' +
                tradeCityCode + '\u0001' +
                startDate + '\u0001' +
                endDate + '\u0001' +
                productId + '\u0001' +
                modifyTag + '\u0001' +
                inModeCode + '\u0001' +
                subscribeId + '\u0001' +
                cityCode + '\u0001' +
                inDate + '\u0001' +
                svcType
                ;
    }
}
