package com.unicom.rts.bean.enums;

public enum PayEnum {
    /**
     * annotation
     */
    charge_id,
    /**
     * annotation
     */
    partition_id,
    /**
     * annotation
     */
    eparchy_code,
    /**
     * annotation
     */
    city_code,
    /**
     * annotation
     */
    cust_id,
    /**
     * annotation
     */
    user_id,
    /**
     * annotation
     */
    serial_number,
    /**
     * annotation
     */
    net_type_code,
    /**
     * annotation
     */
    acct_id,
    /**
     * annotation
     */
    channel_id,
    /**
     * annotation
     */
    payment_id,
    /**
     * annotation
     */
    pay_fee_mode_code,
    /**
     * annotation
     */
    payment_op,
    /**
     * annotation
     */
    recv_fee,
    /**
     * annotation
     */
    limit_money,
    /**
     * annotation
     */
    recv_time,
    /**
     * annotation
     */
    recv_eparchy_code,
    /**
     * annotation
     */
    recv_city_code,
    /**
     * annotation
     */
    recv_depart_id,
    /**
     * annotation
     */
    recv_staff_id,
    /**
     * annotation
     */
    payment_reason_code,
    /**
     * annotation
     */
    input_no,
    /**
     * annotation
     */
    input_mode,
    /**
     * annotation
     */
    outer_trade_id,
    /**
     * annotation
     */
    act_tag,
    /**
     * annotation
     */
    extend_tag,
    /**
     * annotation
     */
    action_code,
    /**
     * annotation
     */
    action_event_id,
    /**
     * annotation
     */
    payment_rule_id,
    /**
     * annotation
     */
    remark,
    /**
     * annotation
     */
    cancel_tag,
    /**
     * annotation
     */
    cancel_staff_id,
    /**
     * annotation
     */
    cancel_depart_id,
    /**
     * annotation
     */
    cancel_city_code,
    /**
     * annotation
     */
    cancel_eparchy_code,
    /**
     * annotation
     */
    cancel_time,
    /**
     * annotation
     */
    cancel_charge_id,
    /**
     * annotation
     */
    rsrv_fee1,
    /**
     * annotation
     */
    rsrv_fee2,
    /**
     * annotation
     */
    rsrv_info1,
    /**
     * annotation
     */
    province_code,
    /**
     * annotation
     */
    rsrv_info2,
    /**
     * annotation
     */
    standard_kind_code,
    /**
     * annotation
     */
    in_time,
    /**
     * annotation
     */
    datasource,
    /**
     * annotation
     */
    cdhtime;
}
