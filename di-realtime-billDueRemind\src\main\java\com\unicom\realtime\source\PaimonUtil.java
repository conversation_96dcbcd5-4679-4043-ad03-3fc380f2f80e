package com.unicom.realtime.source;

import com.unicom.realtime.bean.PhoneBill;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class PaimonUtil {

    public DataStream<PhoneBill> source(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(DWD_R_PAIMON_PHONE_BILL + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, PhoneBill>) (row, collector) -> {
            PhoneBill phoneBill = new PhoneBill();
            phoneBill.setId((String) row.getField("id"));
            phoneBill.setUserNumber((String) row.getField("userNumber"));
            phoneBill.setRightsType((String) row.getField("rightsType"));
            phoneBill.setState((String) row.getField("state"));
            phoneBill.setRightsPrice((String) row.getField("rightsPrice"));
            phoneBill.setEndTime((String) row.getField("endTime"));
            collector.collect(phoneBill);
        }).returns(PhoneBill.class).uid("PhoneBillFlatMap").name("PhoneBillFlatMap")
                .setParallelism(conf.getInt("PhoneBillFlatMap.parallelism", 4));
    }

    private static final String DWD_R_PAIMON_PHONE_BILL = "SELECT\n" +
            "    id id,\n" +
            "    userNumber userNumber,\n" +
            "    rightsType rightsType,\n" +
            "    state state,\n" +
            "    rightsPrice rightsPrice,\n" +
            "    endTime endTime\n" +
            "    FROM dwd_r_paimon_phone_bill";
}
