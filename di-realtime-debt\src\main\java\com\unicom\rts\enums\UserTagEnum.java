package com.unicom.rts.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum UserTagEnum {

    /**
     * 用户用量的枚举信息
     */

    device_number(0,"手机号码"),
    rt_b_user_id(1,"userId"),
    rt_b_province_code(2,"省分编码"),
    rt_b_eparchy_code(3,"地市编码"),
    rt_b_city_code(4,"区县编码");

    private final Integer code;

    private final String msg;


    /**
     *获取枚举类
     * @param code
     * @return
     */
    public static UserTagEnum getByCode(Integer code) {
        return Arrays.stream(UserTagEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .orElse(null);

    }

    /**
     * 根据描述获取枚举类
     * @param msg
     * @return
     */
    public static UserTagEnum getByMsg(String msg) {
        return Arrays.stream(UserTagEnum.values())
                .filter(item -> item.getMsg().equals(msg))
                .findAny()
                .orElse(null);
    }
}
