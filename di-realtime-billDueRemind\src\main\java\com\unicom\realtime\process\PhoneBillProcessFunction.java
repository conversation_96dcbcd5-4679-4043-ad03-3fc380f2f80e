package com.unicom.realtime.process;

import com.unicom.realtime.bean.PhoneBill;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class PhoneBillProcessFunction extends KeyedProcessFunction<String, PhoneBill, PhoneBill> {

    private MapState<String, PhoneBill> mapState;
    private SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(31))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 流量突增状态
        MapStateDescriptor<String, PhoneBill> mapStateDescriptor = new MapStateDescriptor<>("mapState", String.class, PhoneBill.class);
        mapStateDescriptor.enableTimeToLive(ttlConfig);
        mapState = getRuntimeContext().getMapState(mapStateDescriptor);
    }

    @Override
    public void processElement(PhoneBill phoneBill, Context context, Collector<PhoneBill> collector) throws Exception {
        List<PhoneBill> sendBillList = new ArrayList<>();
        mapState.put(phoneBill.getId() + phoneBill.getUserNumber(), phoneBill);
        mapState.values().forEach(phoneBill1 -> {
            if("0".equals(phoneBill1.getState())) {
                try {
                    if(format.parse(phoneBill1.getEndTime()).getTime() > System.currentTimeMillis()) {
                        sendBillList.add(phoneBill1);
                    }
                } catch (ParseException e) {
                    System.out.println("日期转换异常: " + phoneBill.getEndTime());
                }
            }
        });
        PhoneBill bill = new PhoneBill();
        bill.setUserNumber(phoneBill.getUserNumber());
        StringBuilder prices = new StringBuilder();
        StringBuilder endTime = new StringBuilder();
        sendBillList.forEach(bill1 -> {
            prices.append("|");
            prices.append(bill1.getRightsPrice());
            endTime.append("|");
            endTime.append(bill1.getEndTime());
        });
        if(sendBillList.size() > 0) {
            bill.setRightsPrice(prices.toString().substring(1));
            bill.setEndTime(endTime.toString().substring(1));
            collector.collect(bill);
        }
    }
}
