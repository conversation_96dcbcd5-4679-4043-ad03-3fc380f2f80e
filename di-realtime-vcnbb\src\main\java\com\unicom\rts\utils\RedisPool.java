package com.unicom.rts.utils;


import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.log4j.Logger;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.io.File;
import java.io.IOException;
import java.util.LinkedHashSet;
import java.util.Properties;
import java.util.Set;


/**
 * <AUTHOR>
 * @create 2021-06-11 19:58
 */
public class RedisPool {


	public final static Logger log = Logger.getLogger(RedisPool.class);

	private static JedisCluster jedisCluster = null;


	private RedisPool() {
	}

	public static synchronized JedisCluster getPool() {
		// 只有当jedisCluster为空时才实例化
		if (jedisCluster == null) {
			Properties pro;
			try {
				pro = Utilties.loadOutParams(File.separator+"config"+File.separator+"redis.properties");
                JedisPoolConfig config = new JedisPoolConfig();
				Integer timeout = Integer.valueOf(pro.getProperty("redis_client_timeout"));
				config.setMaxTotal(Integer.valueOf(pro.getProperty("redis_max_total")));//最大空闲连接数
				config.setMaxIdle(Integer.valueOf(pro.getProperty("redis_max_idle")));
				config.setMinIdle(Integer.valueOf(pro.getProperty("redis_min_idle")));
				config.setNumTestsPerEvictionRun(Integer.valueOf(pro.getProperty("redis_numTestsPerEvictionRun")));//每次释放连接的最大数目
				config.setTimeBetweenEvictionRunsMillis(Integer.valueOf(pro.getProperty("redis_timeBetweenEvictionRunsMillis"))); //释放连接的扫描间隔（毫秒）
				config.setMinEvictableIdleTimeMillis(Integer.valueOf(pro.getProperty("redis_minEvictableIdleTimeMillis")));//连接最小空闲时间
				config.setSoftMinEvictableIdleTimeMillis(Integer.valueOf(pro.getProperty("redis_softMinEvictableIdleTimeMillis")));//连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放
				config.setMaxWaitMillis(Integer.valueOf(pro.getProperty("redis_maxWaitMillis")));//获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
				config.setTestOnBorrow(Boolean.valueOf(pro.getProperty("redis_testOnBorrow")));//在获取连接的时候检查有效性, 默认false
				config.setTestWhileIdle(Boolean.valueOf(pro.getProperty("redis_testWhileIdle")));//在空闲时检查有效性, 默认false
				config.setBlockWhenExhausted(Boolean.valueOf(pro.getProperty("redis_blockWhenExhausted")));//连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true

				//redis集群，多个节点
				String serverip = pro.getProperty("redis_hosts").trim();
				String password = pro.getProperty("redis_auth_password");
				String[] serveripArray = serverip.split(",");

				Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
				for (String str : serveripArray) {
					String ip = str.split(":")[0];
					int port = Integer.parseInt(str.split(":")[1]);
					nodes.add(new HostAndPort(ip, port));
				}
				jedisCluster = new JedisCluster(nodes, timeout, timeout, 5, password,config);
			} catch (IOException e) {
                log.error(e.getMessage());
			}
		}
		return jedisCluster;
	}

	public static synchronized JedisCluster getJedisPool(ParameterTool conf) {
		// 只有当jedisCluster为空时才实例化
		if (jedisCluster == null) {
				JedisPoolConfig config = new JedisPoolConfig();
				Integer timeout = 5000;
				config.setMaxTotal(10000);//最大空闲连接数
				config.setMaxIdle(5);
				config.setMinIdle(1);
				config.setMaxWaitMillis(10000L);//获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
				config.setTestOnBorrow(false);//在获取连接的时候检查有效性, 默认false
				config.setTestWhileIdle(true);//在空闲时检查有效性, 默认false
				config.setTestOnReturn(false);

				//redis集群，多个节点
				String serverip = conf.get("redis.hosts").trim();
				String password = conf.get("redis.auth.password","Sscj#process$202304axef").trim();
				String[] serveripArray = serverip.split(",");

				Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
				for (String str : serveripArray) {
					String ip = str.split(":")[0];
					int port = Integer.parseInt(str.split(":")[1]);
					nodes.add(new HostAndPort(ip, port));
				}
				jedisCluster = new JedisCluster(nodes, timeout, timeout, 5, password,config);

		}
		return jedisCluster;
	}

}
