package com.unicom.rts.utils;


import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2021-06-11 19:58
 */
public class Utilties {

	
	/**
	 * 获取外部配置文件
	 * @param file
	 * @return
	 * @throws IOException
	 */
	public static Properties loadOutParams(String file) throws IOException {

		Properties prop = new Properties();
		String proFilePath = System.getProperty("user.dir")+File.separator + file;  
		InputStream in = new BufferedInputStream(new FileInputStream(proFilePath));  
		ResourceBundle bundle = new PropertyResourceBundle(in);

		String key = null;
		for (Enumeration<String> enuma = bundle.getKeys(); enuma
				.hasMoreElements();) {
			key = enuma.nextElement();
			prop.put(key, bundle.getObject(key));
		}
		return prop;
	}

	public static void main(String[] args) {
		String json = "{\n" +
				"    \"UNI_BSS_ATTACHED\": {\n" +
				"         \"MEDIA_INFO\": \"\"\n" +
				"    },\n" +
				"    \"UNI_BSS_BODY\": {\n" +
				"        \"SEND_BOOKING_ORDER_FOR_IVR_REQ\": {\n" +
				"            \"businessType\": \"4016\",\n" +
				"            \"callTime\": \"2024-10-11 13:36:09\",\n" +
				"            \"cityCode\": \"0010\",\n" +
				"            \"netType\": \"2\",\n" +
				"            \"orderChannel\": \"SSCJ\",\n" +
				"            \"phoneNum\": \"***********\",\n" +
				"            \"phoneType\": \"1\",\n" +
				"            \"proCode\": \"011\",\n" +
				"            \"touchId\": \"1728624916-2900887\",\n" +
				"            \"routeValue\":\"\"\n" +
				"        }\n" +
				"    },\n" +
				"    \"UNI_BSS_HEAD\": {\n" +
				"        \"APP_ID\": \"6YEacacCkA\",\n" +
				"        \"TIMESTAMP\": \"2024-10-11 13:38:39 421\",\n" +
				"        \"TOKEN\": \"994c466cb923a3a5c1e588fae6268554\",\n" +
				"        \"TRANS_ID\": \"20241011133839421060709\"\n" +
				"    }\n" +
				"}";
		String str = HttpRequest.post("http://10.124.150.230:8000/api/chinaUnicom/midplatform/customerServiceApplication/sendBookingOrderForIvr/v1")
				.header(Header.ACCEPT_ENCODING,"")
				.header(Header.CONTENT_TYPE,"application/json")
				.body(json).execute().body();
		System.out.println("args = " + str);
	}
}
