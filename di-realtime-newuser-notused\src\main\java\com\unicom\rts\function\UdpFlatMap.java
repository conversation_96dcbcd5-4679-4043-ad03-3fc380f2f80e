package com.unicom.rts.function;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.enums.UdpEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UdpFlatMap implements FlatMapFunction<String, FormatData> {
    private final static Logger logger = LoggerFactory.getLogger(UdpFlatMap.class);

    @Override
    public void flatMap(String locStr, Collector<FormatData> collector) throws Exception {
        try {
            String[] lines = StringUtils.splitPreserveAllTokens(locStr, "|");
            for (int i = 1; i < lines.length; i++) {
                String[] line = StringUtils.splitPreserveAllTokens(lines[i], "\u0001");
                String device = line[UdpEnum.DEVICE_NUMBER.ordinal()];
                if (device.length() != 11) {
                    continue;
                }
                FormatData formatData = new FormatData();
                formatData.setDeviceNumber(device);
                formatData.setInDateTime(Long.parseLong(line[UdpEnum.TIME.ordinal()]));
                formatData.setDataSource(1);
                collector.collect(formatData);
            }
        } catch (Exception e) {
            logger.error("UdpClean Exception:{} locStr:{}", e, locStr);
        }
    }
}
