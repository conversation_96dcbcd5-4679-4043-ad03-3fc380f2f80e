package com.unicom.realtime.source;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;


public class KafkaSource {

    private static final String SINK_SUFFIX = "_Sink";


    public static FlinkKafkaConsumer<ConsumerRecord<String, String>> getRootConsumer(ParameterTool conf, String topic) throws Exception {

        List<String> topics = Arrays.asList(StringUtils.splitPreserveAllTokens(topic, ","));

        //kafka配置
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, conf.get("source.broker"));
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, conf.get("group.id"));
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, conf.get("enable.auto.commit"));
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");
        //properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, conf.get("offset.reset"));
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        boolean sourceSecurity = conf.getBoolean("source.kafka.security");
        String flag = conf.get("offset.flag");

        if (sourceSecurity) {
            String kafkaUser = conf.get("kafka.user");
            String kafkaPassword = conf.get("kafka.password");

            //天宫kafka安全设置
            properties.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", "SASL_PLAINTEXT");
            properties.setProperty("sasl.mechanism", "PLAIN");
        }

        FlinkKafkaConsumer<ConsumerRecord<String, String>> consumer = new FlinkKafkaConsumer<>(topics, new CustomDeSerializationSchema(), properties);
        if ("0".equals(flag)) {
            consumer.setStartFromEarliest();
        } else if ("1".equals(flag)) {
            consumer.setStartFromLatest();
        }
        consumer.setCommitOffsetsOnCheckpoints(true);

        return consumer;

    }

    public static void addSink(DataStream<Tuple3<String, String, Iterable<Header>>> stream, ParameterTool conf) {
        // sink: sink kafka配置
        Properties producerProp = new Properties();
        producerProp.setProperty("bootstrap.servers", conf.get("sink.kafka.bootstrap"));
        producerProp.put("acks", conf.get("acks", "0")); //Must set acks to all in order to use the idempotent producer
        producerProp.put("retries", conf.get("retries", "2"));
        producerProp.put("batch.size", conf.get("batch.size", "16384"));
        producerProp.put("linger.ms", conf.get("linger.ms", "50"));
        producerProp.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        producerProp.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));

        // 下发kafka开启认证
        boolean sinkSecurity = conf.getBoolean("sink.kafka.security");
        if (sinkSecurity) {
            String mechanism = conf.get("sasl.mechanism");
            String protocol = conf.get("security.protocol");
            String jaasConfig = conf.get("sasl.jaas.config");
            producerProp.setProperty("sasl.jaas.config", jaasConfig + " required username=\"" + conf.get("sink.kafka.user") + "\" password=\"" + conf.get("sink.kafka.password") + "\";");
            producerProp.setProperty("security.protocol", protocol);
            producerProp.setProperty("sasl.mechanism", mechanism);
        }


        KafkaSink<Tuple3<String, String, Iterable<Header>>> sink = KafkaSink.<Tuple3<String, String, Iterable<Header>>>builder()
                .setBootstrapServers(conf.get("sink.kafka.bootstrap"))
                .setKafkaProducerConfig(producerProp)
                //.setRecordSerializer(new MultiTopicSerializationSchema())
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();

        stream.sinkTo(sink).uid("KafkaOcOrder" + SINK_SUFFIX).setParallelism(Integer.parseInt(conf.get("sink.parallelism")));
    }

}
