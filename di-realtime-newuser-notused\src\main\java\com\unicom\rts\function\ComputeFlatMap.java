package com.unicom.rts.function;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.bean.NewUserState;
import com.unicom.rts.utils.HbaseUtil;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class ComputeFlatMap extends RichFlatMapFunction<FormatData, Tuple2<String, String>> {
    private ValueState<NewUserState> newUser;
    private String outTopic;
    private ParameterTool conf;
    private HbaseUtil hbaseUtil = null;
    private HTable table = null;
    private static byte[] FAMILYNAME = "f".getBytes();

    public ComputeFlatMap(String outTopic, ParameterTool conf) {
        this.outTopic = outTopic;
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        newUser = getRuntimeContext().getState(new ValueStateDescriptor<NewUserState>("NewUserState", NewUserState.class));
        //初始化hbase链接
        hbaseUtil = new HbaseUtil();
        Connection connection = hbaseUtil.init(conf.get("hbase.zookeeper"), conf.get("hbase.zookeeper.port"), conf.get("hbase.user"));
        table = (HTable) connection.getTable(TableName.valueOf(conf.get("hbaseTable.duplicate")));
    }

    @Override
    public void flatMap(FormatData data, Collector<Tuple2<String, String>> collector) throws Exception {
        // bus 新用户数据，更新新用户状态
        if (data.getDataSource() == 0) {
            NewUserState newUserState = new NewUserState(data.getEparchyCode(), data.getInDateTime());
            newUser.update(newUserState);
            return;
        }
        // 信令数据，查询新用户状态
        NewUserState newUserStateValue = newUser.value();
        // 非新用户，跳过不做处理
        if (newUserStateValue != null) {
            // 信令发生事件-建档时间，大于等于12小时，下发数据并且删除新用户状态
            if (data.getInDateTime() - newUserStateValue.getInDateTime() >= 12 * 60 * 60 * 1000L) {
                // 取状态里的归属地市，来源于bus,开户时间取自新用户状态
                data.setEparchyCode(newUserStateValue.getEparchyCode());
                data.setInDateTime(newUserStateValue.getInDateTime());
                Tuple2<String, String> outData = new Tuple2<>();
                outData.f0 = outTopic;
                outData.f1 = data.toString();
                // 数据下发去重
                if (notSendOut(data.getDeviceNumber() + outTopic)) {
                    // 下发数据
                    collector.collect(outData);
                }
                // 删除状态
                newUser.update(null);
            } else {
                newUser.update(null);
            }
        }

    }

    public boolean notSendOut(String key) throws IOException {
        //先查询，是否下发过，下发过result isEmpty true
        Result result = hbaseUtil.getDataFromHbase(key, table);
        //未下发过，需要下发，且向hbase中存储
        if (result.isEmpty()) {
            //负载均衡
            Put put = new Put(hbaseUtil.getRowKeySimple(key));
            put.addColumn(FAMILYNAME, "key".getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8));
            if (!put.isEmpty()) {
                table.put(put);
            }
        }
        return result.isEmpty();
    }
}
