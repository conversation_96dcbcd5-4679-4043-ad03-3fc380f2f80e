package com.unicom.realtime.source;

import com.unicom.realtime.bean.UnionBean;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

public class CustPersonPsptSource {

    public DataStream<UnionBean> build(StreamExecutionEnvironment env, ParameterTool conf) {
        StreamTableEnvironment tabEnv = StreamTableEnvironment.create(env);
        String warehouse = conf.get("warehouse");
        String database = conf.get("default-database");
        String options = conf.get("paimon.options", "");
        if (!"".equals(options)) {
            options = "/*+ OPTIONS(" + options + ")*/";
        }
        tabEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehouse + "',\n" +
                "    'default-database'='" + database + "');");
        tabEnv.executeSql("use catalog paimon_catalog;");
        Table userTable = tabEnv.sqlQuery(TF_F_CUST_PERSON_PSPT + options);
        return tabEnv.toChangelogStream(userTable).flatMap((FlatMapFunction<Row, UnionBean>) (row, collector) -> {
            UnionBean unionBean = new UnionBean();
            unionBean.setCustId((String)row.getField("CUST_ID"));
            unionBean.setPsptTypeCode((String)row.getField("PSPT_TYPE_CODE"));
            unionBean.setPsptId((String)row.getField("PSPT_ID"));
            unionBean.setOpt((String)row.getField("OPT"));
            unionBean.setTableName("TF_F_CUST_PERSON_PSPT");
            unionBean.setRowKind(row.getKind());
            collector.collect(unionBean);
        }).returns(UnionBean.class).uid("PsptInfoFlatMap").name("PsptInfoFlatMap")
                .setParallelism(conf.getInt("PsptInfoFlatMap.parallelism", env.getParallelism()));
    }

    private static final String TF_F_CUST_PERSON_PSPT = "SELECT\n" +
            "    OPT,\n" +
            "    CUST_ID CUST_ID,\n" +
            "    PSPT_TYPE_CODE PSPT_TYPE_CODE,\n" +
            "    PSPT_ID PSPT_ID\n" +
            "    FROM ods_r_paimon_tf_f_cust_person_pspt";
}
