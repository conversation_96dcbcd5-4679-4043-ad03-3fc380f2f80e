package com.unicom.rts.function;

import com.google.gson.Gson;
import com.unicom.rts.entity.VcfData;
import com.unicom.rts.entity.VcfJson;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class VcfClean extends RichFlatMapFunction<ConsumerRecord<String, String>, VcfData> {
    private final static Logger logger = LoggerFactory.getLogger(VcfClean.class);
    private transient long delay = 0L;
    private transient long filter = 0L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record,Collector<VcfData> out) throws Exception {
        VcfData vcf = new VcfData();
        parse(vcf, record);
        //增加头
        vcf.parseHeader(record);
        out.collect(vcf);
    }

    public void parse(VcfData vcf, ConsumerRecord<String,String> record) {

        Gson gson = new Gson();
        VcfJson vcfJson = gson.fromJson(record.value(), VcfJson.class);
        vcf.setVcfJson(vcfJson);
    }
}
