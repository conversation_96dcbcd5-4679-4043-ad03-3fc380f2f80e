package com.unicom.rts.function;

import com.unicom.rts.bean.FormatData;
import com.unicom.rts.enums.RoamEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

public class RoamFlatMap implements FlatMapFunction<String, FormatData> {
    private final static Logger logger = LoggerFactory.getLogger(RoamFlatMap.class);

    @Override
    public void flatMap(String roamStr, Collector<FormatData> collector) {
        try {
            FormatData formatData = new FormatData();
            String[] roam = StringUtils.splitPreserveAllTokens(roamStr, "|");
            long startTime = Timestamp.valueOf(roam[RoamEnum.START_TIME.ordinal()]).getTime();
            formatData.setDeviceNumber(roam[RoamEnum.MSISDN.ordinal()]);
            formatData.setInDateTime(startTime);
            formatData.setDataSource(2);
            formatData.setEparchyCode(roam[RoamEnum.HAREA_ID.ordinal()]);
            collector.collect(formatData);
        } catch (Exception e) {
            logger.error("roamStr: {} Exception: {}", roamStr, e);
        }

    }
}
