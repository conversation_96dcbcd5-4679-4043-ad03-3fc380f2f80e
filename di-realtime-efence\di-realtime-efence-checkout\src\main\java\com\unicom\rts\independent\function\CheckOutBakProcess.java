package com.unicom.rts.independent.function;

import com.unicom.rts.independent.bean.CheckoutDataBean;
import com.unicom.rts.independent.enums.MidDataEnum;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/19
 **/
public class CheckOutBakProcess extends ProcessFunction<Tuple2<String, String>, CheckoutDataBean> {

    private OutputTag<CheckoutDataBean> checkoutBak;

    public CheckOutBakProcess(OutputTag<CheckoutDataBean> checkoutBak) {
        this.checkoutBak = checkoutBak;
    }

    @Override
    public void processElement(Tuple2<String, String> input, Context ctx, Collector<CheckoutDataBean> collector) throws Exception {
        CheckoutDataBean checkoutDataBean = new CheckoutDataBean();
        checkoutDataBean.setSinkTopic(input.f0);
        String[] str = input.f1.split("\1");

        checkoutDataBean.setDeviceNumber(str[MidDataEnum.DEVEICENUMBER.getCode()]);
        checkoutDataBean.setTime(str[MidDataEnum.TIME.getCode()]);
        checkoutDataBean.setHprovName(str[MidDataEnum.HPROVNAME.getCode()]);
        checkoutDataBean.setHareaName(str[MidDataEnum.HAREANAME.getCode()]);
        checkoutDataBean.setArea(str[MidDataEnum.AREA.getCode()]);
        checkoutDataBean.setImei(str[MidDataEnum.IMEI.getCode()]);
        checkoutDataBean.setImsi(str[MidDataEnum.IMSI.getCode()]);
        checkoutDataBean.setCurrentTime(str[MidDataEnum.CURRENTTIME.getCode()]);
        checkoutDataBean.setLac(str[MidDataEnum.LAC.getCode()]);
        checkoutDataBean.setCi(str[MidDataEnum.CI.getCode()]);
        if (str.length > 10) {
            checkoutDataBean.setLongitude(str[MidDataEnum.LONGITUDE.getCode()]);
            checkoutDataBean.setLatitude(str[MidDataEnum.LATITUDE.getCode()]);
        }

        if (str.length > 12) {
            checkoutDataBean.setBak(String.join(",", Arrays.copyOfRange(str, 12, str.length)));
        }
        ctx.output(checkoutBak, checkoutDataBean);
    }
}
