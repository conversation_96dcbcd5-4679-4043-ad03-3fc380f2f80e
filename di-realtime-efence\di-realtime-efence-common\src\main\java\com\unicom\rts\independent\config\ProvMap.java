package com.unicom.rts.independent.config;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/24
 **/
public class ProvMap {

    public static HashMap<String, String> getProvMap() {
        HashMap<String,String> provMap = new HashMap<>();
        provMap.put("011","北京");
        provMap.put("031","上海");
        provMap.put("051","广东");
        provMap.put("083","重庆");
        provMap.put("074","湖南");
        provMap.put("070","青海");
        provMap.put("030","安徽");
        provMap.put("038","福建");
        provMap.put("087","甘肃");
        provMap.put("059","广西");
        provMap.put("050","海南");
        provMap.put("075","江西");
        provMap.put("013","天津");
        provMap.put("090","吉林");
        provMap.put("097","黑龙江");
        provMap.put("091","辽宁");
        provMap.put("071","湖北");
        provMap.put("089","新疆");
        provMap.put("088","宁夏");
        provMap.put("086","云南");
        provMap.put("036","浙江");
        provMap.put("017","山东");
        provMap.put("079","西藏");
        provMap.put("010","内蒙古");
        provMap.put("076","河南");
        provMap.put("081","四川");
        provMap.put("018","河北");
        provMap.put("019","山西");
        provMap.put("084","陕西");
        provMap.put("034","江苏");
        provMap.put("085","贵州");
        return provMap;
    }
}
