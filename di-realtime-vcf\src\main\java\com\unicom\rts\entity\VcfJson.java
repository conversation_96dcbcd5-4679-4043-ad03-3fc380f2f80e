package com.unicom.rts.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-12-19 20:30
 */
@Data
public class VcfJson {

    private String userid;
    private String serialnumber; //手机号码

    private String eparchycode;
    private String provincecode;

    private List<Consume> consume;

    private String intime;
    private String dataSource;
    private String smstime;
    private String cdhtime;



    @Override
    public String toString() {
        return "{" +
                "\"userid\":\"" + userid + '\"' +
                ", \"serialnumber\":\"" + serialnumber + '\"' +
                ", \"eparchycode\":\"" + eparchycode + '\"' +
                ", \"provincecode\":\"" + provincecode + '\"' +
                ", \"consume\":" + consume  +
                ", \"intime\":\"" + intime + '\"' +
                ", \"dataSource\":\"" + dataSource + '\"' +
                ", \"cdhtime\":\"" + cdhtime + '\"' +
                '}';
    }
}